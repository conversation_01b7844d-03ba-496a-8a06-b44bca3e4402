#!/bin/bash

# Python开发环境设置脚本
# 用于解决Trae编辑器中Python函数定义跳转问题

echo "🚀 开始设置Python开发环境..."

# 检查Python版本
echo "📋 检查Python版本..."
python3 --version

# 检查是否安装了uv
if ! command -v uv &> /dev/null; then
    echo "❌ uv未安装，正在安装..."
    curl -LsSf https://astral.sh/uv/install.sh | sh
    source $HOME/.cargo/env
else
    echo "✅ uv已安装"
fi

# 创建虚拟环境
echo "🔧 创建Python虚拟环境..."
uv venv --python 3.10

# 激活虚拟环境并安装依赖
echo "📦 安装项目依赖..."
source .venv/bin/activate
uv pip install -e .

# 安装开发工具
echo "🛠️ 安装开发工具..."
uv pip install black flake8 pylint mypy

# 验证安装
echo "✅ 验证Python环境..."
which python
python --version
pip list | grep -E "(flask|openai|pandas)"

echo ""
echo "🎉 环境设置完成！"
echo "📝 接下来请在Trae编辑器中："
echo "   1. 重启编辑器 (Cmd+Shift+P -> Developer: Reload Window)"
echo "   2. 选择Python解释器 (Cmd+Shift+P -> Python: Select Interpreter)"
echo "   3. 选择 ./.venv/bin/python"
echo "   4. 等待Pylance语言服务器初始化完成"
echo ""
echo "🔍 现在应该可以正常跳转到函数定义了！"