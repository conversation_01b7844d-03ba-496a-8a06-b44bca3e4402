#!/usr/bin/env python3
"""
测试新的流式更新架构 - 并发版本
验证预插入机制和实时持久化功能，支持并发测试和结构化输出
支持动态模型配置，可在运行时指定不同的模型进行测试
支持用户参数和动态查询功能
"""

import sys
import os
import time
import argparse
import re
import random
import yaml
import tempfile
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Any, Optional
import json
import logging

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.services.agent.api_query_processor import APIQueryProcessor
from src.services.feishu.query_processor import FeishuQueryProcessor
from src.services.chatbot.history_service import get_conversation_history_as_input_list, save_user_message
from src.services.user_query_service import UserQueryService
from src.utils.logger import logger

# 定义多个测试查询，覆盖所有agent
test_queries = [
    {
        "query": "杭州市昨天新增多少新注册门店？",
        "description": "测试sales_order_analytics - 新门店注册分析",
        "expected_agent": "sales_order_analytics",
    },
    {
        "query": "为什么客户 TeaBaby茶宝宝 (ID: 559867) 不算拉新？",
        "description": "测试sales_order_analytics - 有效拉新判断",
        "expected_agent": "sales_order_analytics",
    },
    {
        "query": "分析一下杭州市的客户昨天PB商品销售额",
        "description": "测试sales_order_analytics - PB商品销售额分析",
        "expected_agent": "sales_order_analytics",
    },
    {
        "query": "查询大客户ID=1132155的门店，本月份的订单明细。列出大客户名字、门店名字和ID、门店地址、订单号、订单金额、商品名字、商品SKU、商品金额、商品件数、下单日",
        "description": "测试sales_order_analytics - 订单明细查询",
        "expected_agent": "sales_order_analytics",
    },
    {
        "query": "查询大客户ID=1132155的门店，本月份的履约订单明细",
        "description": "测试sales_order_analytics - 履约订单明细查询",
        "expected_agent": "sales_order_analytics",
    },
    {
        "query": "帮我查一下SKU是 '三麟苏打汽水' 的商品最新的质检报告链接，如果查到了多个SKU，请查询全部SKU",
        "description": "测试warehouse_and_fulfillment - 质检报告查询",
        "expected_agent": "warehouse_and_fulfillment",
    },
    {
        "query": "梁舒婷的客户，客户名称：桔瀧，在2025年7月履约SKU明细是哪些",
        "description": "测试sales_order_analytics - 履约SKU明细查询",
        "expected_agent": "sales_order_analytics",
    },
    {
        "query": "对比一下'象牌苏打水', '三麟苏打汽水'在过去2个月的销售情况",
        "description": "测试sales_order_analytics - 销售额对比分析",
        "expected_agent": "sales_order_analytics",
    },
    {
        "query": "M1 李钱程团队本月的绩效分析",
        "description": "测试sales_kpi_analytics - 销售团队绩效分析",
        "expected_agent": "sales_kpi_analytics",
    },
    {
        "query": "BD彭为昨天有多少PB品销售额",
        "description": "测试sales_order_analytics - BD的PB品销售额查询",
        "expected_agent": "sales_order_analytics",
    },
    {
        "query": "分析一下安佳淡奶油全部的SKU在嘉兴仓的库存",
        "description": "测试warehouse_and_fulfillment - 库存分析",
        "expected_agent": "warehouse_and_fulfillment",
    },
    {
        "query": "你好，黑海盗纯牛奶什么时候有货，东莞仓。如果查到了多个SKU，请查询全部SKU",
        "description": "测试warehouse_and_fulfillment - 库存查询",
        "expected_agent": "warehouse_and_fulfillment",
    },{
        "query": "大客户楼下酸奶最近2周下单的sku和日下单平均数量",
        "description": "测试sales_order_analytics - 大客户下单数量分析",
        "expected_agent": "sales_order_analytics",
    }
]

class ModelConfigManager:
    """模型配置管理器，支持动态模型配置覆盖"""
    
    def __init__(self):
        self.original_configs = {}  # 存储原始配置
        self.temp_config_files = []  # 存储临时配置文件路径
    
    def override_agent_model(self, agent_name: str, model_provider: str, model_name: str) -> str:
        """
        为指定agent创建临时配置文件，覆盖模型设置
        
        Args:
            agent_name: agent名称
            model_provider: 模型提供者 (xm, openrouter)
            model_name: 模型名称
            
        Returns:
            str: 临时配置文件路径
        """
        config_file = f"{agent_name}.yml"
        
        try:
            # 加载原始配置
            from src.utils.resource_manager import load_resource
            yaml_content = load_resource("data_fetcher_bot_config", config_file)
            
            if not yaml_content:
                logger.warning(f"无法加载配置文件: {config_file}")
                return config_file
            
            # 解析YAML配置
            config = yaml.safe_load(yaml_content)
            
            # 保存原始配置
            if agent_name not in self.original_configs:
                self.original_configs[agent_name] = {
                    'model_provider': config.get('model_provider'),
                    'model': config.get('model')
                }
            
            # 覆盖模型配置
            config['model_provider'] = model_provider
            config['model'] = model_name
            
            # 创建临时配置文件
            temp_file = tempfile.NamedTemporaryFile(
                mode='w', 
                suffix='.yml', 
                prefix=f'{agent_name}_temp_',
                delete=False,
                encoding='utf-8'
            )
            
            yaml.dump(config, temp_file, default_flow_style=False, allow_unicode=True)
            temp_file.close()
            
            self.temp_config_files.append(temp_file.name)
            
            logger.info(f"为 {agent_name} 创建临时配置: {model_provider}/{model_name}")
            logger.debug(f"临时配置文件: {temp_file.name}")
            
            return temp_file.name
            
        except Exception as e:
            logger.exception(f"创建临时配置失败 {agent_name}: {e}")
            return config_file
    
    def apply_model_overrides(self, model_overrides: Dict[str, Dict[str, str]]):
        """
        批量应用模型覆盖配置
        
        Args:
            model_overrides: 格式为 {agent_name: {provider: str, model: str}}
        """
        if not model_overrides:
            return
            
        logger.info(f"应用模型覆盖配置: {model_overrides}")
        
        # 动态修补配置加载函数
        self._patch_config_loader(model_overrides)
    
    def _patch_config_loader(self, model_overrides: Dict[str, Dict[str, str]]):
        """动态修补配置加载函数以支持模型覆盖"""
        from src.services.agent.bots import data_fetcher_bot
        
        # 保存原始的_load_config函数
        original_load_config = data_fetcher_bot._load_config
        
        def patched_load_config(config_file: str) -> Dict[str, Any]:
            # 调用原始函数获取配置
            config = original_load_config(config_file)
            
            # 提取agent名称（去掉.yml后缀）
            agent_name = config_file.replace('.yml', '')
            
            # 禁止覆盖coordinator_bot的模型配置
            if agent_name == 'coordinator_bot':
                logger.info(f"跳过模型覆盖 {agent_name} (不允许覆盖coordinator_bot配置)")
                return config
            
            # 如果有覆盖配置，则应用（跳过coordinator_bot）
            if agent_name in model_overrides and agent_name != 'coordinator_bot':
                override = model_overrides[agent_name]
                original_provider = config.get('model_provider')
                original_model = config.get('model')
                
                config['model_provider'] = override.get('provider', original_provider)
                config['model'] = override.get('model', original_model)
                
                logger.info(f"应用模型覆盖 {agent_name}: {original_provider}/{original_model} -> {config['model_provider']}/{config['model']}")
            
            return config
        
        # 替换函数
        data_fetcher_bot._load_config = patched_load_config
    
    def cleanup(self):
        """清理临时文件"""
        for temp_file in self.temp_config_files:
            try:
                if os.path.exists(temp_file):
                    os.unlink(temp_file)
                    logger.debug(f"清理临时配置文件: {temp_file}")
            except Exception as e:
                logger.warning(f"清理临时文件失败 {temp_file}: {e}")
        
        self.temp_config_files.clear()
    
    def __del__(self):
        """析构函数，确保清理临时文件"""
        self.cleanup()


class TestResult:
    """测试结果数据结构"""
    def __init__(self, test_case: Dict[str, Any], test_id: int, model_config: Optional[Dict[str, str]] = None):
        self.test_case = test_case
        self.test_id = test_id
        self.model_config = model_config or {}  # 记录使用的模型配置
        self.start_time = None
        self.end_time = None
        self.duration = None
        self.actual_agent = None
        self.response_count = 0
        self.status = "pending"
        self.error_message = None
        self.responses = []
        self.conversation_id = None
        self.tool_usage = {}  # 记录每个tool的使用次数
        self.all_tools = []   # 记录所有使用过的tools

    def to_dict(self) -> Dict[str, Any]:
        return {
            "test_id": self.test_id,
            "query": self.test_case["query"],
            "description": self.test_case["description"],
            "expected_agent": self.test_case["expected_agent"],
            "actual_agent": self.actual_agent,
            "model_config": self.model_config,
            "duration": self.duration,
            "response_count": self.response_count,
            "status": self.status,
            "error_message": self.error_message,
            "conversation_id": self.conversation_id,
            "responses": self.responses,
            "tool_usage": self.tool_usage,
            "all_tools": self.all_tools
        }

def extract_tool_calls_from_content(content: str) -> Dict[str, int]:
    """
    从内容中提取工具调用次数统计
    只统计 '=== Coordinator执行日志 ===' 之后的工具调用
    支持多种格式：
    1. type='function_call'中的name字段
    2. "tool_name called with args:" 格式
    3. "Called tool:tool_name with args" 格式

    Returns:
        Dict[str, int]: 工具名称到调用次数的映射
    """
    tool_usage = {}
    content_str = str(content)

    # 查找 Coordinator执行日志 的分隔符
    coordinator_log_marker = "=== Coordinator执行日志 ==="
    coordinator_start = content_str.find(coordinator_log_marker)

    # 如果找到了分隔符，只处理分隔符之后的内容
    if coordinator_start != -1:
        content_str = content_str[coordinator_start + len(coordinator_log_marker):]
    else:
        # 如果没有找到分隔符，可能是旧格式的日志，仍然处理全部内容
        # 但优先查找是否有其他可能的分隔符
        pass

    # 方法1：精确匹配：查找所有type='function_call'的ResponseFunctionToolCall中的name值
    # 示例：ResponseFunctionCall(...type='function_call'...name='fetch_ddl_for_table'...)
    pattern = r"ResponseFunction(?:ToolCall)?\([^)]*type=['\"]function_call['\"][^)]*?name=['\"]([^'\"]+)['\"]"
    matches = re.findall(pattern, content_str)

    # 统计所有匹配到的tool name
    for match in matches:
        if match and match != "function_call":
            if match not in tool_usage:
                tool_usage[match] = 0
            tool_usage[match] += 1

    # 方法2：匹配 "tool_name called with args:" 格式
    # 示例：fetch_ddl_for_table called with args: {"table_names": ["merchant"]}
    called_pattern = r"(\w+)\s+called\s+with\s+args:"
    called_matches = re.findall(called_pattern, content_str)
    for match in called_matches:
        if match:
            if match not in tool_usage:
                tool_usage[match] = 0
            tool_usage[match] += 1

    # 方法3：匹配 "Called tool:tool_name with args" 格式
    # 示例：Called tool:fetch_odps_sql_result with args
    called_tool_pattern = r"Called tool:(\w+) with args"
    called_tool_matches = re.findall(called_tool_pattern, content_str)
    for match in called_tool_matches:
        if match:
            if match not in tool_usage:
                tool_usage[match] = 0
            tool_usage[match] += 1

    # 方法4：尝试更简单的模式匹配
    # 匹配所有name='xxx'，但只在包含function_call的上下文中
    func_pattern = r"name=['\"]([^'\"]+)['\"][^)]*type=['\"]function_call['\"]"
    func_matches = re.findall(func_pattern, content_str)
    for match in func_matches:
        if match and match != "function_call":
            if match not in tool_usage:
                tool_usage[match] = 0
            tool_usage[match] += 1

    return tool_usage

def extract_tool_names_from_content(content: str) -> List[str]:
    """
    从内容中提取toolname（保持向后兼容性）
    现在基于extract_tool_calls_from_content实现
    """
    tool_usage = extract_tool_calls_from_content(content)
    return list(tool_usage.keys())

def extract_sql_error_stats_from_logs(logs: str) -> (int, List[str]):
    """
    仅统计“=== Coordinator执行日志 ===”之后的 SQL 错误：
    - 必须严格匹配形如 error="Error executing SQL query on database: ..." 的片段，才算一次唯一SQL错误
    - 尽量从该片段中提取括号内的具体错误（例如 (error: 1054 (42S22): Unknown column 'i.pd_name' in 'field list')）

    为什么这样做：用户只关心协调器日志收敛后的、由 SQL 执行失败导致的规范化错误；其他零散关键字（如 Unknown column）可能出现在上下文说明，不应误计。
    """
    if not logs:
        return 0, []

    coordinator_log_marker = "=== Coordinator执行日志 ==="
    start = logs.find(coordinator_log_marker)
    if start == -1:
        # 未出现分隔符，则不计入（按需求1：只统计分隔符后的日志）
        return 0, []

    tail = logs[start + len(coordinator_log_marker):]

    # 严格匹配以 error="Error executing SQL query on database: 开头到下一处引号结束的片段
    pattern = r'error="Error executing SQL query on database:[^\"]*"'
    matches = re.findall(pattern, tail)

    details: List[str] = []
    for m in matches:
        # 从匹配片段中提取括号内的 (error: ...)
        inner = re.search(r'\(error:\s*[^\)]+\)', m)
        if inner:
            details.append(inner.group(0))
        else:
            # 兜底：返回精简的匹配摘要，避免过长
            details.append(m[:200])

    return len(matches), details

def run_single_test(test_result: TestResult, user_info: Dict[str, str], model_config_manager: Optional[ModelConfigManager] = None) -> TestResult:
    """运行单个测试"""
    processor = APIQueryProcessor()
    test_result.start_time = time.time()
    test_result.conversation_id = f"test_conv_{int(time.time())}_{test_result.test_id}"
    
    try:
        # 保存用户查询到chat_history
        save_success = save_user_message(
            username=user_info["name"],
            email=user_info["email"],
            conversation_id=test_result.conversation_id,
            content=test_result.test_case["query"]
        )
        
        if not save_success:
            test_result.status = "failed"
            test_result.error_message = "Failed to save user query"
            return test_result

        # 执行流式查询
        response_count = 0
        agent_detected = False
        
        for response in processor.run_query(
            user_query=test_result.test_case["query"],
            user_info=user_info,
            conversation_id=test_result.conversation_id,
        ):
            response_count += 1
            test_result.responses.append(response)
            
            # 检测实际调用的agent
            if test_result.test_case["expected_agent"] in response and not agent_detected:
                test_result.actual_agent = test_result.test_case["expected_agent"]
                agent_detected = True

        test_result.response_count = response_count
        
        # 验证数据库中的记录
        history = get_conversation_history_as_input_list(
            user_info["name"], user_info["email"], test_result.conversation_id
        )
        
        # 提取tool usage信息
        try:
            from src.services.chatbot.history_service import get_conversation_messages
            messages = get_conversation_messages(
                conversation_id=test_result.conversation_id,
                username=user_info["name"],
                email=user_info["email"]
            )
            for msg in messages:
                if msg.get("role") == "assistant":
                    content = msg.get("content", "")
                    logs = str(msg.get("logs", ""))

                    # 从内容和日志中提取工具调用次数统计
                    tools_in_content = extract_tool_calls_from_content(content)
                    tools_in_logs = extract_tool_calls_from_content(logs)

                    # 合并工具使用统计
                    for tool_name, count in tools_in_content.items():
                        if tool_name not in test_result.tool_usage:
                            test_result.tool_usage[tool_name] = 0
                        test_result.tool_usage[tool_name] += count

                        if tool_name not in test_result.all_tools:
                            test_result.all_tools.append(tool_name)

                    for tool_name, count in tools_in_logs.items():
                        if tool_name not in test_result.tool_usage:
                            test_result.tool_usage[tool_name] = 0
                        test_result.tool_usage[tool_name] += count

                        if tool_name not in test_result.all_tools:
                            test_result.all_tools.append(tool_name)
        except Exception as e:
            logger.warning(f"提取tool usage失败: {str(e)}")
        
        if response_count > 0 and len(history) > 0:
            test_result.status = "passed"
        else:
            test_result.status = "failed"
            test_result.error_message = "No responses or history records"
            
    except Exception as e:
        test_result.status = "failed"
        test_result.error_message = str(e)
        logger.exception(f"测试查询 {test_result.test_id} 异常")
    
    test_result.end_time = time.time()
    test_result.duration = test_result.end_time - test_result.start_time
    
    return test_result

def run_concurrent_tests(queries: List[Dict[str, Any]], max_workers: int = 3, model_overrides: Optional[Dict[str, Dict[str, str]]] = None, user_info: Optional[Dict[str, str]] = None) -> List[TestResult]:
    """运行并发测试"""
    # 如果没有提供用户信息，使用默认测试用户
    if user_info is None:
        today = datetime.now().strftime('%Y%m%d')
        user_info = {
            "name": "ChatBI自动化测试",
            "email": f"test_user_{today}@summerfarm.net",
            "open_id": "test_open_id",
        }
    
    results = []
    model_config_manager = None
    
    # 如果有模型覆盖配置，创建配置管理器
    if model_overrides:
        model_config_manager = ModelConfigManager()
        model_config_manager.apply_model_overrides(model_overrides)
        logger.info(f"应用模型覆盖配置: {model_overrides}")
    
    try:
        # 创建测试任务
        test_tasks = []
        for i, query in enumerate(queries, 1):
            # 构建模型配置信息用于记录
            model_config = {}
            if model_overrides:
                expected_agent = query.get("expected_agent")
                if expected_agent in model_overrides:
                    override = model_overrides[expected_agent]
                    model_config = {
                        "provider": override.get("provider"),
                        "model": override.get("model")
                    }
            
            test_result = TestResult(query, i, model_config)
            test_tasks.append((test_result, user_info, model_config_manager))
        
        # 使用线程池执行并发测试
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_result = {
                executor.submit(run_single_test, task[0], task[1], task[2]): task[0] 
                for task in test_tasks
            }
            
            for future in as_completed(future_to_result):
                test_result = future.result()
                results.append(test_result)
                model_info = ""
                if test_result.model_config:
                    model_info = f" (模型: {test_result.model_config.get('provider', 'default')}/{test_result.model_config.get('model', 'default')})"
                print(f"✓ 测试 {test_result.test_id} 完成，耗时 {test_result.duration:.2f}s{model_info}")
        
    finally:
        # 清理模型配置管理器
        if model_config_manager:
            model_config_manager.cleanup()
    
    return sorted(results, key=lambda x: x.test_id)

def generate_markdown_report(results: List[TestResult], total_time: float, content_length: int = 2000, concurrent_users: int = 3, model_overrides: Optional[Dict[str, Dict[str, str]]] = None, user_info: Optional[Dict[str, str]] = None) -> str:
    """生成markdown格式的测试报告"""
    today = datetime.now().strftime('%Y%m%d')

    # 使用传入的用户信息，如果没有则创建默认测试用户信息
    if user_info is None:
        user_info = {
            "name": "ChatBI自动化测试",
            "email": f"test_user_{today}@summerfarm.net",
            "open_id": "test_open_id",
        }
    
    # 计算统计信息
    total_tests = len(results)
    passed_tests = sum(1 for r in results if r.status == "passed")
    failed_tests = total_tests - passed_tests
    pass_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
    
    # 统计SQL错误（严格版）：仅统计“Coordinator执行日志”之后且完全匹配 error="Error executing SQL query on database: ..." 的错误
    sql_error_count = 0
    sql_error_details = []

    for result in results:
        error_count = 0
        error_details_list = []

        try:
            from src.services.chatbot.history_service import get_conversation_messages

            messages = get_conversation_messages(
                conversation_id=result.conversation_id,
                username=user_info["name"],
                email=user_info["email"]
            )

            for msg in messages:
                if msg.get("role") == "assistant":
                    logs = str(msg.get("logs", ""))
                    cnt, details = extract_sql_error_stats_from_logs(logs)
                    if cnt:
                        error_count += cnt
                        error_details_list.extend(details)
        except Exception as e:
            logger.warning(f"检测SQL错误时失败: {str(e)}")

        sql_error_count += error_count
        if error_count > 0:
            sql_error_details.append({
                "test_id": result.test_id,
                "query": result.test_case["query"],
                "error_count": error_count,
                "errors": error_details_list
            })
    
    # 按agent分组统计
    agent_stats = {}
    total_tool_usage = {}  # 全局tool usage统计
    model_usage_stats = {}  # 模型使用统计
    
    for result in results:
        agent = result.test_case["expected_agent"]
        if agent not in agent_stats:
            agent_stats[agent] = {
                "total": 0, "passed": 0, "failed": 0, "avg_duration": 0, 
                "min_duration": None, "max_duration": None, "median_duration": 0,
                "durations": [], "sql_errors": 0, "tool_usage": {}, "tools_used": [], "model_info": {}
            }
        agent_stats[agent]["total"] += 1
        agent_stats[agent]["passed"] += 1 if result.status == "passed" else 0
        agent_stats[agent]["failed"] += 1 if result.status == "failed" else 0
        agent_stats[agent]["avg_duration"] += result.duration
        agent_stats[agent]["durations"].append(result.duration)
        
        # 记录模型使用信息
        if result.model_config:
            provider = result.model_config.get('provider', 'default')
            model = result.model_config.get('model', 'default')
            model_key = f"{provider}/{model}"
            agent_stats[agent]["model_info"] = {"provider": provider, "model": model, "model_key": model_key}
            
            # 全局模型使用统计
            if model_key not in model_usage_stats:
                model_usage_stats[model_key] = {"count": 0, "passed": 0, "failed": 0, "agents": set()}
            model_usage_stats[model_key]["count"] += 1
            model_usage_stats[model_key]["passed"] += 1 if result.status == "passed" else 0
            model_usage_stats[model_key]["failed"] += 1 if result.status == "failed" else 0
            model_usage_stats[model_key]["agents"].add(agent)
        
        # 合并tool usage
        for tool_name, count in result.tool_usage.items():
            if tool_name not in agent_stats[agent]["tool_usage"]:
                agent_stats[agent]["tool_usage"][tool_name] = 0
            agent_stats[agent]["tool_usage"][tool_name] += count
            
            if tool_name not in agent_stats[agent]["tools_used"]:
                agent_stats[agent]["tools_used"].append(tool_name)
                
            # 更新全局统计
            if tool_name not in total_tool_usage:
                total_tool_usage[tool_name] = 0
            total_tool_usage[tool_name] += count
        
        # 统计该agent的SQL错误总数
        agent_sql_errors = 0
        for error_detail in sql_error_details:
            if error_detail["test_id"] == result.test_id:
                agent_sql_errors += error_detail["error_count"]
                break
        
        agent_stats[agent]["sql_errors"] += agent_sql_errors
    
    for agent in agent_stats:
        if agent_stats[agent]["total"] > 0:
            agent_stats[agent]["avg_duration"] /= agent_stats[agent]["total"]
            
            # 计算每个Agent的耗时分位数统计
            durations = agent_stats[agent]["durations"]
            if durations:
                agent_stats[agent]["min_duration"] = min(durations)
                agent_stats[agent]["max_duration"] = max(durations)
                durations_sorted = sorted(durations)
                n = len(durations_sorted)
                agent_stats[agent]["median_duration"] = durations_sorted[n//2]
            else:
                agent_stats[agent]["min_duration"] = 0.0
                agent_stats[agent]["max_duration"] = 0.0
                agent_stats[agent]["median_duration"] = 0.0
    
    # 计算耗时统计信息
    all_durations = [r.duration for r in results if r.duration is not None]
    if all_durations:
        min_duration = min(all_durations)
        max_duration = max(all_durations)
        median_duration = sorted(all_durations)[len(all_durations)//2]
    else:
        min_duration = max_duration = median_duration = 0.0
    
    # 生成报告
    report = f"""# 流式架构测试报告 - {today}

## 测试概要

- **测试时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- **测试用户**: {user_info['name']} ({user_info['email']})
- **测试用例总数**: {total_tests}
- **通过测试**: {passed_tests}
- **失败测试**: {failed_tests}
- **通过率**: {pass_rate:.1f}%
- **SQL错误总数**: {sql_error_count}
- **总耗时**: {total_time:.2f}秒
- **并发数**: {concurrent_users}
- **最小耗时**: {min_duration:.2f}秒
- **最大耗时**: {max_duration:.2f}秒
- **耗时中位数**: {median_duration:.2f}秒"""

    # 添加详细的模型配置信息
    if model_overrides:
        report += "\n- **🤖 模型配置**: 已启用覆盖配置\n"
        
        # 统计使用的模型类型
        unique_models = set()
        for config in model_overrides.values():
            model_key = f"{config.get('provider', 'default')}/{config.get('model', 'default')}"
            unique_models.add(model_key)
        
        if len(unique_models) == 1:
            report += f"- **模型类型**: 全局统一使用 `{list(unique_models)[0]}`\n"
        else:
            report += f"- **模型类型**: 混合配置 ({len(unique_models)} 种模型)\n"
            
        report += f"- **配置Agent数量**: {len(model_overrides)} 个\n"
        
        report += "\n### 🔧 模型配置详情\n"
        for agent_name, config in sorted(model_overrides.items()):
            provider = config.get('provider', 'default')
            model = config.get('model', 'default')
            report += f"- **{agent_name}**: `{provider}/{model}`\n"
    else:
        report += "\n- **🤖 模型配置**: 使用默认YAML配置"

    report += "\n\n## SQL错误详情\n"

    if sql_error_details:
        report += f"检测到 **{sql_error_count}** 个SQL错误:\n\n"
        for error in sql_error_details:
            report += f"- **测试 {error['test_id']}**: {error['query'][:100]}{'...' if len(error['query']) > 100 else ''}\n"
            report += f"  - **错误次数**: {error['error_count']} 次\n"
            if error['errors']:
                for i, err in enumerate(error['errors'][:3], 1):  # 最多显示3个错误
                    error_summary = str(err)[:150] + '...' if len(str(err)) > 150 else str(err)
                    report += f"    {i}. {error_summary}\n"
                if len(error['errors']) > 3:
                    report += f"    ... 还有 {len(error['errors']) - 3} 个错误\n"
            report += "\n"
    else:
        report += "✅ 未检测到SQL错误\n\n"

    report += """## Tool Usage 统计

### 全局Tool使用统计
"""
    
    if total_tool_usage:
        for tool_name, count in sorted(total_tool_usage.items(), key=lambda x: x[1], reverse=True):
            report += f"- **{tool_name}**: {count} 次\n"
    else:
        report += "- 未检测到tool调用\n"
    
    # 添加模型使用统计
    if model_usage_stats:
        report += "\n## 🤖 模型性能统计\n\n"
        
        # 按通过率排序显示模型性能
        sorted_models = sorted(model_usage_stats.items(), 
                             key=lambda x: (x[1]["passed"] / x[1]["count"] if x[1]["count"] > 0 else 0), 
                             reverse=True)
        
        for model_key, stats in sorted_models:
            model_pass_rate = (stats["passed"] / stats["count"] * 100) if stats["count"] > 0 else 0
            agents_list = ', '.join(sorted(stats["agents"]))
            
            # 根据通过率添加表情符号
            if model_pass_rate >= 90:
                performance_icon = "🟢"
            elif model_pass_rate >= 70:
                performance_icon = "🟡"
            else:
                performance_icon = "🔴"
                
            report += f"### {performance_icon} {model_key}\n"
            report += f"- **测试次数**: {stats['count']}\n"
            report += f"- **通过次数**: {stats['passed']}\n"
            report += f"- **失败次数**: {stats['failed']}\n"
            report += f"- **通过率**: {model_pass_rate:.1f}%\n"
            report += f"- **使用Agent**: {agents_list}\n"
            
            # 计算平均耗时（如果有数据）
            total_duration = 0
            count = 0
            for result in results:
                if (result.model_config and 
                    f"{result.model_config.get('provider', 'default')}/{result.model_config.get('model', 'default')}" == model_key):
                    total_duration += result.duration
                    count += 1
            
            if count > 0:
                avg_duration = total_duration / count
                report += f"- **平均耗时**: {avg_duration:.2f}秒\n"
            
            report += "\n"
        
        # 添加模型对比总结
        if len(sorted_models) > 1:
            best_model = sorted_models[0]
            worst_model = sorted_models[-1]
            best_rate = (best_model[1]["passed"] / best_model[1]["count"] * 100) if best_model[1]["count"] > 0 else 0
            worst_rate = (worst_model[1]["passed"] / worst_model[1]["count"] * 100) if worst_model[1]["count"] > 0 else 0
            
            report += "### 📊 模型对比总结\n"
            report += f"- **最佳表现**: `{best_model[0]}` (通过率: {best_rate:.1f}%)\n"
            report += f"- **最差表现**: `{worst_model[0]}` (通过率: {worst_rate:.1f}%)\n"
            report += f"- **性能差距**: {best_rate - worst_rate:.1f}%\n\n"

    report += "\n## 🎯 Agent详细统计\n\n"
    
    # 按通过率排序显示Agent
    sorted_agents = sorted(agent_stats.items(), 
                          key=lambda x: (x[1]["passed"] / x[1]["total"] if x[1]["total"] > 0 else 0), 
                          reverse=True)
    
    for agent, stats in sorted_agents:
        agent_pass_rate = (stats["passed"] / stats["total"] * 100) if stats["total"] > 0 else 0
        tools_used_str = ', '.join(stats["tools_used"]) if stats["tools_used"] else '无'
        
        # 根据通过率添加状态图标
        if agent_pass_rate >= 90:
            status_icon = "🟢"
        elif agent_pass_rate >= 70:
            status_icon = "🟡"
        else:
            status_icon = "🔴"
        
        report += f"\n### {status_icon} {agent}\n"
        report += f"- **测试数量**: {stats['total']}\n"
        report += f"- **通过数量**: {stats['passed']}\n"
        report += f"- **失败数量**: {stats['failed']}\n"
        report += f"- **通过率**: {agent_pass_rate:.1f}%\n"
        
        # 突出显示模型信息
        if stats["model_info"]:
            model_info = stats["model_info"]
            report += f"- **🤖 使用模型**: `{model_info['provider']}/{model_info['model']}` (覆盖配置)\n"
        else:
            report += "- **🤖 使用模型**: `默认YAML配置`\n"
            
        report += f"- **SQL错误**: {stats['sql_errors']}\n"
        report += f"- **平均耗时**: {stats['avg_duration']:.2f}秒"
        
        # 新增耗时统计信息
        if stats['min_duration'] is not None and stats['max_duration'] is not None:
            report += f"""
- **最小耗时**: {stats['min_duration']:.2f}秒
- **最大耗时**: {stats['max_duration']:.2f}秒
- **中位耗时**: {stats['median_duration']:.2f}秒"""

        report += f"\n- **使用Tools**: {tools_used_str}\n"
        
        if stats["tool_usage"]:
            report += "- **Tool使用详情**:\n"
            for tool_name, count in sorted(stats["tool_usage"].items(), key=lambda x: x[1], reverse=True):
                report += f"  - {tool_name}: {count} 次\n"
        
        report += "\n"
    
    report += "## 详细测试结果\n\n"
    
    for result in results:
        status_icon = "✅" if result.status == "passed" else "❌"
        
        report += f"\n### {status_icon} 测试 {result.test_id}: {result.test_case['description']}\n\n"
        report += "**查询内容**:\n"
        report += "```\n"
        report += f"{result.test_case['query']}\n"
        report += "```\n\n"
        report += f"**预期Agent**: `{result.test_case['expected_agent']}`\n"
        report += f"**实际Agent**: `{result.actual_agent or '未检测到'}`\n"
        
        # 突出显示模型配置信息
        if result.model_config:
            provider = result.model_config.get('provider', 'default')
            model = result.model_config.get('model', 'default')
            report += f"**🤖 使用模型**: `{provider}/{model}` (覆盖配置)\n"
        else:
            report += f"**🤖 使用模型**: `默认配置`\n"
            
        report += f"**状态**: `{result.status}`\n"
        report += f"**耗时**: {result.duration:.2f}秒\n"
        report += f"**响应数量**: {result.response_count}\n"
        report += f"**会话ID**: `{result.conversation_id}`\n"
        
        if result.tool_usage:
            report += "**Tool使用情况**:\n"
            for tool_name, count in sorted(result.tool_usage.items(), key=lambda x: x[1], reverse=True):
                report += f"- {tool_name}: {count} 次\n"
            report += "\n"
        
        if result.all_tools:
            report += f"**使用Tools**: {', '.join(result.all_tools)}\n\n"
        
        if result.error_message:
            report += f"**错误信息**: {result.error_message}\n\n"
        
        # 从数据库获取完整的聊天内容
        try:
            from src.services.chatbot.history_service import get_conversation_messages
            
            messages = get_conversation_messages(
                conversation_id=result.conversation_id,
                username=user_info["name"],
                email=user_info["email"]
            )
            
            if messages:
                # 找到最后一条助手的回复
                assistant_messages = [msg for msg in messages if msg.get("role") == "assistant"]
                if not assistant_messages:
                    report += "**最终测试结果**: 未找到助手的回复\n\n"
                    
                # 显示完整的对话记录
                report += "**完整对话记录**:\n"
                for i, msg in enumerate(messages, 1):
                    role = "用户" if msg.get("role") == "user" else "助手"
                    content = msg.get("content", "")
                    logs = msg.get("logs")
                    
                    # 严格检测SQL错误：仅依据 extract_sql_error_stats_from_logs 的匹配结果
                    if role == "助手" and logs:
                        logs_str = str(logs)
                        cnt, details = extract_sql_error_stats_from_logs(logs_str)
                        if cnt:
                            report += "**⚠️SQL错误**: 检测到SQL执行错误（严格匹配）\n\n"
                            # 展示前1条详细原因
                            if details:
                                detail_summary = details[0]
                                report += f"原因: {detail_summary}\n\n"
                    
                    report += f"**{i}. {role}**: {content[:content_length]}{'...' if len(content) > content_length else ''}\n\n"
            else:
                report += "**测试结果**: 无法从数据库获取对话记录\n"
        except Exception as e:
            logger.exception(f"获取{result.conversation_id}的测试结果失败: {str(e)}")
            report += f"**获取测试结果失败**: {str(e)}\n"
        
        report += "\n---\n\n"
    
    return report

def save_markdown_report(report: str, filename: str = None):
    """保存markdown报告到文件"""
    today = datetime.now().strftime('%Y%m%d')
    
    # 创建测试结果的目录路径
    test_results_dir = f"./test_results/{today}"
    
    # 如果目录不存在，创建目录
    if not os.path.exists(test_results_dir):
        os.makedirs(test_results_dir)
        print(f"📁 创建测试报告目录: {test_results_dir}")
    
    if filename is None:
        filename = f"test_streaming_result_{today}.md"
    
    # 拼接完整路径
    full_path = os.path.join(test_results_dir, filename)
    
    with open(full_path, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"✅ 测试报告已保存到: {full_path}")
    return full_path

def test_streaming_threshold():
    """测试流式更新阈值设置"""
    print("=" * 60)
    print("测试流式更新阈值设置")
    print("=" * 60)

    from src.services.agent.base_query_processor import STREAMING_UPDATE_THRESHOLD

    print(f"当前流式更新阈值: {STREAMING_UPDATE_THRESHOLD} 字符")

    if STREAMING_UPDATE_THRESHOLD == 15:
        print("✓ 阈值已正确设置为15字符")
        return True
    else:
        print(f"✗ 阈值应该是15字符，但当前是{STREAMING_UPDATE_THRESHOLD}字符")
        return False

async def test_feishu_streaming():
    """测试飞书流式更新架构"""
    print("=" * 60)
    print("测试飞书流式更新架构")
    print("=" * 60)

    try:
        processor = FeishuQueryProcessor()
        print("飞书处理器创建成功")
        print("注意：完整的飞书测试需要真实的飞书环境")
        return True
    except Exception as e:
        print(f"飞书测试失败: {e}")
        logger.exception("飞书测试异常")
        return False

def get_user_info_by_username(username: str) -> Optional[Dict[str, str]]:
    """
    根据用户名获取用户信息，并尝试附加 access token

    Args:
        username: 用户名

    Returns:
        Dict包含用户信息及 access_token，未找到返回 None，token 不存在则 access_token 为 None
    """
    try:
        user_info = UserQueryService.get_user_info_by_username(username)
        if not user_info:
            logger.warning(f"❌ 未找到用户: {username}")
            return None

        # 获取有效 access token
        from src.services.auth.user_session_service import user_session_service
        open_id = user_info.get("open_id")
        access_token = user_session_service.get_user_access_token(open_id) if open_id else None
        user_info["access_token"] = access_token

        logger.info(f"✅ 成功获取用户信息及 token: {user_info['name']} ({user_info['email']})")
        return user_info
    except Exception as e:
        logger.exception(f"获取用户信息失败: {e}")
        return None

def parse_model_overrides(model_override_str: str) -> Dict[str, Dict[str, str]]:
    """
    解析模型覆盖配置字符串
    
    格式支持:
    1. agent1:provider/model,agent2:provider/model
    2. provider/model (应用到所有agent)
    
    Args:
        model_override_str: 模型覆盖配置字符串
        
    Returns:
        Dict[str, Dict[str, str]]: 解析后的配置
    """
    if not model_override_str:
        return {}
    
    overrides = {}
    
    # 检查是否是全局配置格式 (provider/model)
    if ',' not in model_override_str and ':' not in model_override_str:
        if '/' in model_override_str:
            provider, model = model_override_str.split('/', 1)
            # 应用到所有已知的agent，排除coordinator_bot
            known_agents = [
                "sales_order_analytics", "sales_kpi_analytics", 
                "warehouse_and_fulfillment", "general_chat_bot"
            ]
            for agent in known_agents:
                overrides[agent] = {"provider": provider.strip(), "model": model.strip()}
            logger.info(f"跳过coordinator_bot的模型覆盖（不允许覆盖coordinator_bot配置）")
            return overrides
    
    # 解析具体的agent配置
    for item in model_override_str.split(','):
        item = item.strip()
        if ':' in item:
            agent_name, model_spec = item.split(':', 1)
            agent_name = agent_name.strip()
            model_spec = model_spec.strip()
            
            # 不允许覆盖coordinator_bot的模型配置
            if agent_name == 'coordinator_bot':
                logger.warning(f"不允许覆盖coordinator_bot的模型配置，已忽略: {item}")
                continue
            
            if '/' in model_spec:
                provider, model = model_spec.split('/', 1)
                overrides[agent_name] = {
                    "provider": provider.strip(),
                    "model": model.strip()
                }
            else:
                # 只指定了模型名，使用默认provider
                overrides[agent_name] = {
                    "provider": "openrouter",
                    "model": model_spec
                }
    
    return overrides


def setup_test_file_logging(model_override_str: Optional[str], model_overrides: Optional[Dict[str, Dict[str, str]]]) -> Optional[str]:
    """
    为本次测试运行追加文件日志输出，文件名格式为 ./logs/test_model_{model_name_choosen}_{current_date}.log。

    为什么这样做：
    - 以“所选模型 + 日期”命名，便于按模型与日期快速溯源。
    - 在测试脚本侧追加 FileHandler，不影响全局默认日志配置与其他运行环境。
    """
    try:
        # 推断模型名：
        # 1) 单一全局 --model=provider/model => 取最后一段 model
        # 2) 多 agent 覆盖：若所有覆盖模型一致，取该模型；否则标记为 mixed
        # 3) 未指定 --model 则记为 default
        model_name_chosen = "default"

        if model_override_str:
            # 判断是否为简单全局形式（无逗号、无冒号，且包含斜杠）
            if ("," not in model_override_str) and (":" not in model_override_str) and ("/" in model_override_str):
                # 形如 provider/model 或 provider/vendor/model
                parts = model_override_str.split('/')
                model_name_chosen = parts[-1].strip() or "default"
            else:
                # 使用解析后的覆盖映射来判断是否统一
                if model_overrides:
                    models = set()
                    for cfg in model_overrides.values():
                        prov = (cfg.get("provider") or "").strip()
                        mod = (cfg.get("model") or "").strip()
                        models.add(f"{prov}/{mod}" if prov and mod else mod or prov)
                    if len(models) == 1:
                        # 统一模型时取最后一段
                        only = next(iter(models))
                        if only and "/" in only:
                            model_name_chosen = only.split('/')[-1].strip() or "default"
                        else:
                            model_name_chosen = (only or "default").strip() or "default"
                    else:
                        model_name_chosen = "mixed"

        # 规范化模型名，避免路径/非法字符
        safe_model = (
            model_name_chosen
            .replace('/', '-')
            .replace(':', '-')
            .replace(' ', '-')
        ) or "default"

        today = datetime.now().strftime('%Y%m%d')
        logs_dir = "./logs"
        if not os.path.exists(logs_dir):
            os.makedirs(logs_dir, exist_ok=True)

        logfile = os.path.join(logs_dir, f"test_model_{safe_model}_{today}.log")

        # 复用现有的业务 logger，避免改变全局根 logger 行为
        from src.utils.logger import logger as app_logger

        # 避免重复添加同一路径的 FileHandler（多次运行 main() 时）
        for h in list(app_logger.handlers):
            if isinstance(h, logging.FileHandler):
                try:
                    if getattr(h, 'baseFilename', None) and str(h.baseFilename) == os.path.abspath(logfile):
                        return logfile
                except Exception:
                    pass

        file_handler = logging.FileHandler(logfile, mode='a', encoding='utf-8')
        file_handler.setLevel(logging.INFO)
        file_handler.setFormatter(logging.Formatter(
            "[%(asctime)s] - %(levelname)s - %(name)s - %(message)s", datefmt="%Y-%m-%d %H:%M:%S"
        ))
        app_logger.addHandler(file_handler)

        # 同步把控制台 logger 的级别也确保为 INFO（不改变外层配置，只做兜底）
        if app_logger.level > logging.INFO:
            app_logger.setLevel(logging.INFO)

        return logfile
    except Exception as e:
        # 失败不影响主流程，仅记录一次警告
        try:
            from src.utils.logger import logger as app_logger
            app_logger.warning(f"追加文件日志失败: {e}")
        except Exception:
            pass
        return None


def main():
    """主测试函数 - 并发版本"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='并发测试流式更新架构，支持动态模型配置和用户参数')
    parser.add_argument('--cu', type=int, default=4,
                       help='并发数 (concurrent users), 默认为4')
    parser.add_argument('--content-length', type=int, default=4000,
                       help='内容截取长度，默认为4000字符')
    parser.add_argument('--n', type=int, default=None,
                       help='指定测试用例数量，默认为全部，如果指定且数量少于测试用例数量，则随机选择')
    parser.add_argument('--model', type=str, default=None,
                       help='模型覆盖配置。格式: agent1:provider/model,agent2:provider/model 或 provider/model (全局)')
    parser.add_argument('--user', type=str, default=None,
                       help='指定用户名，根据用户名动态获取用户信息来执行测试')
    parser.add_argument('--query', type=str, default=None,
                       help='指定查询内容，使用用户输入的查询而不是预定义的测试问题')
    parser.add_argument('--list-models', action='store_true',
                       help='列出可用的模型配置示例')
    args = parser.parse_args()
    
    # 如果请求列出模型，显示示例并退出
    if args.list_models:
        print("可用的模型配置示例:")
        print("1. 全局配置 (应用到所有agent):")
        print("   --model openrouter/anthropic/claude-sonnet-4")
        print("   --model xm/kimi-k2")
        print("   --model xm/kimi-k2-turbo")
        print("   --model openrouter/google/gemini-2.5-pro")
        print("   --model openrouter/openai/gpt-oss-120b")
        print("")
        print("2. 特定agent配置:")
        print("   --model sales_order_analytics:xm/kimi-k2,warehouse_and_fulfillment:openrouter/anthropic/claude-sonnet-4")
        print("   --model general_chat_bot:openrouter/google/gemini-2.5-pro")
        print("   ⚠️ 注意: coordinator_bot不允许模型覆盖")
        print("")
        print("3. 支持的provider:")
        print("   - xm: 鲜沐内部API")
        print("   - openrouter: OpenRouter API")
        print("")
        print("4. 常用模型:")
        print("   - xm: deepseek-v3-250324, kimi-k2")
        print("   - openrouter: anthropic/claude-sonnet-4, google/gemini-2.5-pro, openai/gpt-5-mini")
        print("")
        print("5. 用户和查询参数示例:")
        print("   --user 张三 --query \"杭州今天的新注册门店清单\"")
        print("   --user 李四 --query \"分析一下昨天的销售数据\"")
        print("   --user 王五  # 使用指定用户运行预定义测试用例")
        print("   --query \"查询库存信息\"  # 使用默认测试用户运行自定义查询")
        return 0
    
    concurrent_users = args.cu
    content_length = args.content_length
    test_case_count = args.n
    model_override_str = args.model
    username = args.user
    custom_query = args.query

    # 解析模型覆盖配置
    model_overrides = parse_model_overrides(model_override_str) if model_override_str else None

    # 在解析出模型配置后，立即设置本次测试的文件日志输出
    logfile_path = setup_test_file_logging(model_override_str, model_overrides)
    if logfile_path:
        print(f"日志输出文件: {logfile_path}")

    # 获取用户信息
    user_info = None
    if username:
        user_info = get_user_info_by_username(username)
        if not user_info:
            print(f"❌ 错误: 未找到用户 '{username}'，请检查用户名是否正确")
            return 1
        print(f"✅ 使用用户: {user_info['name']} ({user_info['email']})")

    # 处理自定义查询
    selected_queries = test_queries
    if custom_query:
        # 如果提供了自定义查询，创建一个包含该查询的测试用例
        custom_test_case = {
            "query": custom_query,
            "description": f"自定义查询测试: {custom_query[:50]}{'...' if len(custom_query) > 50 else ''}",
            "expected_agent": "unknown",  # 由于是自定义查询，无法预知会调用哪个agent
        }
        selected_queries = [custom_test_case]
        print(f"✅ 使用自定义查询: {custom_query}")
    elif test_case_count is not None and test_case_count > 0 and test_case_count < len(test_queries):
        # 随机选择指定数量的测试用例
        selected_queries = random.sample(test_queries, min(test_case_count, len(test_queries)))
        print(f"随机选择 {len(selected_queries)} 个测试用例")
    else:
        # 使用全部测试用例
        print(f"使用全部 {len(test_queries)} 个测试用例")

    
    print("开始并发测试新的流式更新架构")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # 显示用户信息
    if user_info:
        print(f"测试用户: {user_info['name']} ({user_info['email']})")
    else:
        print("测试用户: 默认测试用户")

    # 显示查询信息
    if custom_query:
        print(f"查询类型: 自定义查询")
        print(f"查询内容: {custom_query}")
    else:
        print(f"查询类型: 预定义测试用例")
        print(f"实际测试用例数: {len(selected_queries)}")

    print(f"并发数: {concurrent_users}")
    print(f"内容截取长度: {content_length}字符")

    # 显示模型配置信息
    if model_overrides:
        print(f"模型配置覆盖: 已启用")
        for agent_name, config in model_overrides.items():
            print(f"  {agent_name}: {config['provider']}/{config['model']}")
    else:
        print("模型配置: 使用默认配置")
    
    start_time = time.time()
    
    # 运行并发测试
    print("\n" + "=" * 80)
    print("开始并发测试...")
    print("=" * 80)
    
    results = run_concurrent_tests(selected_queries, max_workers=concurrent_users, model_overrides=model_overrides, user_info=user_info)
    
    total_time = time.time() - start_time
    
    # 打印简要结果
    passed_tests = sum(1 for r in results if r.status == "passed")
    total_tests = len(results)
    pass_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
    
    # 计算SQL错误数（严格版）
    sql_error_count = 0
    for result in results:
        error_count = 0
        try:
            from src.services.chatbot.history_service import get_conversation_messages

            messages = get_conversation_messages(
                conversation_id=result.conversation_id,
                username=(user_info["name"] if user_info else "ChatBI自动化测试"),
                email=(user_info["email"] if user_info else f"test_user_{datetime.now().strftime('%Y%m%d')}@summerfarm.net")
            )

            for msg in messages:
                if msg.get("role") == "assistant":
                    logs = str(msg.get("logs", ""))
                    cnt, _ = extract_sql_error_stats_from_logs(logs)
                    if cnt:
                        error_count += cnt
        except Exception as e:
            logger.warning(f"检测SQL错误时失败: {str(e)}")

        sql_error_count += error_count
    
    # 计算全局tool usage
    global_tool_usage = {}
    for result in results:
        for tool_name, count in result.tool_usage.items():
            if tool_name not in global_tool_usage:
                global_tool_usage[tool_name] = 0
            global_tool_usage[tool_name] += count
    
    # 生成并保存报告
    report = generate_markdown_report(results, total_time, content_length, concurrent_users, model_overrides, user_info)
    
    # 根据模型配置调整文件名
    filename_suffix = ""
    if model_overrides:
        # 如果是全局配置，使用简化的文件名
        if len(model_overrides) > 3:  # 假设是全局配置
            first_config = next(iter(model_overrides.values()))
            model_name = first_config['model'].replace('/', '-').replace(':', '-')
            filename_suffix = f"-{model_name}"
        else:
            # 特定agent配置，使用agent名称
            agent_names = list(model_overrides.keys())[:2]  # 最多取前2个
            filename_suffix = f"-{'-'.join(agent_names)}"
    
    filename = save_markdown_report(report, f"test_streaming_result_{datetime.now().strftime('%Y%m%d')}{filename_suffix}.md")
    
    print("\n" + "=" * 80)
    print("并发测试完成")
    print("=" * 80)

    # 显示用户信息
    if user_info:
        print(f"测试用户: {user_info['name']} ({user_info['email']})")
    else:
        print("测试用户: 默认测试用户")

    # 显示查询信息
    if custom_query:
        print(f"查询类型: 自定义查询 - {custom_query[:50]}{'...' if len(custom_query) > 50 else ''}")
    else:
        print(f"查询类型: 预定义测试用例")

    print(f"总测试用例: {total_tests}")
    print(f"通过测试: {passed_tests}")
    print(f"失败测试: {total_tests - passed_tests}")
    print(f"通过率: {pass_rate:.1f}%")
    print(f"SQL错误数: {sql_error_count}")
    print(f"总耗时: {total_time:.2f}秒")
    print(f"并发数: {concurrent_users}")
    print(f"报告文件: {filename}")
    print(f"内容截取长度: {content_length}字符")

    if model_overrides:
        print("模型配置覆盖:")
        for agent_name, config in model_overrides.items():
            print(f"  {agent_name}: {config['provider']}/{config['model']}")
    else:
        print("模型配置: 使用默认配置")
    
    if global_tool_usage:
        print("\nTool使用统计:")
        for tool_name, count in sorted(global_tool_usage.items(), key=lambda x: x[1], reverse=True):
            print(f"  {tool_name}: {count} 次")
    else:
        print("\n未检测到tool调用")
    
    if passed_tests == total_tests:
        print("🎉 所有并发测试都通过了！")
        return 0
    else:
        print("⚠️  部分测试失败，请查看详细报告")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)