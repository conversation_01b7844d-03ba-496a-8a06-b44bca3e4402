{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/__init__.py:35: NotOpenSSLWarning: urllib3 v2 only supports OpenSSL 1.1.1+, currently the 'ssl' module is compiled with 'LibreSSL 2.8.3'. See: https://github.com/urllib3/urllib3/issues/3020\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["my ip:***************\n", "\n"]}], "source": ["import requests\n", "import os\n", "\n", "from dotenv import load_dotenv\n", "\n", "load_dotenv()\n", "\n", "WORKING_DIR = \"resources/ddl\"\n", "OPENAI_API_KEY = os.getenv(\"XM_OPENAI_API_KEY\")\n", "OPENAI_API_BASE = os.getenv(\"OPENAI_API_BASE\")\n", "OPENAI_MODEL = \"deepseek/deepseek-r1-0528:free\"  # os.getenv(\"OPENAI_MODEL\")\n", "\n", "print(\"my ip:\" + requests.get(\"https://ifconfig.io/ip\").text)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Defaulting to user installation because normal site-packages is not writeable\n", "Looking in indexes: https://pypi.tuna.tsinghua.edu.cn/simple\n", "Requirement already satisfied: mysql-connector-python in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (9.3.0)\n", "\u001b[33mWARNING: You are using pip version 21.2.4; however, version 25.1.1 is available.\n", "You should consider upgrading via the '/Library/Developer/CommandLineTools/usr/bin/python3 -m pip install --upgrade pip' command.\u001b[0m\n", "Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["pip install mysql-connector-python"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["| 会话ID | 用户名 | 用户内容 | 最后创建时间 | 仪表盘链接 |\n", "|--------|--------|----------|--------------|------------|\n", "| om_x100b4cbd6b05dca80ecee5e93da7022 | 韦贵丰 | 我团队5月份完成的非安佳铁塔交易额是多少 | 2025-05-30 | https://chat-bi.summerfarm.net/dashboard?chat=om_x100b4cbd6b05dca80ecee5e93da7022 |\n", "| om_x100b4c4f8119b4ac0f31cdaa0cc8ba5 | 张志强 | 马苏里拉干酪碎什么时候到货,全部 | 2025-05-29 | https://chat-bi.summerfarm.net/dashboard?chat=om_x100b4c4f8119b4ac0f31cdaa0cc8ba5 |\n", "| om_x100b4c5c6b97acb00f3b3be30d8aef5 | 彭为 | 我的安佳无盐大黄油25KG客户有哪些？ | 2025-05-28 | https://chat-bi.summerfarm.net/dashboard?chat=om_x100b4c5c6b97acb00f3b3be30d8aef5 |\n", "| 922494fa-1120-486f-a8d9-572ca098cfcc | 陈强 | 4月和5月，永州市每日的下单客户数，按照日期倒序排序,4月和5月，永州市每日的下单客户数，按照日期倒序排序 | 2025-05-27 | https://chat-bi.summerfarm.net/dashboard?chat=922494fa-1120-486f-a8d9-572ca098cfcc |\n", "| om_x100b4c6121576ae00f18fcd05f8d13f | 胡译丰 | 帮忙匹配下这些商品苏州大区的采购成本，填到成本那列 | 2025-05-27 | https://chat-bi.summerfarm.net/dashboard?chat=om_x100b4c6121576ae00f18fcd05f8d13f |\n", "| 650c2c1a-35ef-4396-bf60-cf4f48bb4cfd | 康凯 | 4 月在顺鹿达购买无籽红提的客户有哪些 | 2025-05-27 | https://chat-bi.summerfarm.net/dashboard?chat=650c2c1a-35ef-4396-bf60-cf4f48bb4cfd |\n", "| om_x100b4c66e5533cf80e39d0a3e6739b6 | 彭为 | 我的杨梅客户数有哪些？ | 2025-05-27 | https://chat-bi.summerfarm.net/dashboard?chat=om_x100b4c66e5533cf80e39d0a3e6739b6 |\n", "| om_x100b4c662990acfc0f2bca5c0067ae1 | 胡译丰 | 160g-200g是单果规格 | 2025-05-27 | https://chat-bi.summerfarm.net/dashboard?chat=om_x100b4c662990acfc0f2bca5c0067ae1 |\n", "| om_x100b4c662ada54140f19942d14ae679 | 胡译丰 | 牛油果，160g-200g，东莞仓的sku编码 | 2025-05-27 | https://chat-bi.summerfarm.net/dashboard?chat=om_x100b4c662ada54140f19942d14ae679 |\n", "| om_x100b4c04083bd0b80ecd23b811c6113 | 唐鹏 | 安佳奶油奶酪 什么时候才能到货 | 2025-05-25 | https://chat-bi.summerfarm.net/dashboard?chat=om_x100b4c04083bd0b80ecd23b811c6113 |\n", "| om_x100b4c2df61e44800f2f24806199904 | 彭为 | 我本月订单提成有多少了 | 2025-05-24 | https://chat-bi.summerfarm.net/dashboard?chat=om_x100b4c2df61e44800f2f24806199904 |\n", "| om_x100b4c2de6e07d580f22c20dd247530 | 彭为 | 我本月非ATGMV履约有多少 | 2025-05-24 | https://chat-bi.summerfarm.net/dashboard?chat=om_x100b4c2de6e07d580f22c20dd247530 |\n", "| om_x100b4c33f58d04800f216edff1bd3cc | 吕建杰 | 新版佣金-高价值今日达标，上海大区 | 2025-05-23 | https://chat-bi.summerfarm.net/dashboard?chat=om_x100b4c33f58d04800f216edff1bd3cc |\n", "| om_x100b4c335d42d08c0e3ce5b9544008d | 李韬 | 买过法瑞芙大黄油, 584027027103, 25KG*1箱/25KG | 2025-05-23 | https://chat-bi.summerfarm.net/dashboard?chat=om_x100b4c335d42d08c0e3ce5b9544008d |\n", "| om_x100b4dcbb982e4840f31638f52d022c | 彭为 | 我这个月的水果履约数据有多少？ | 2025-05-23 | https://chat-bi.summerfarm.net/dashboard?chat=om_x100b4dcbb982e4840f31638f52d022c |\n", "| om_x100b4dca957c30f80f4dddd3e3006c6 | 林金秋 | 福州马苏里拉芝士碎今天有到吗 | 2025-05-23 | https://chat-bi.summerfarm.net/dashboard?chat=om_x100b4dca957c30f80f4dddd3e3006c6 |\n", "| om_x100b4dc8ee00502c0f2e13fff6750f0 | 马华健 | 我的客户中，本月截止目前达到高价值客户门槛的客户明细，按当前的GMV和履约商品件数排序 | 2025-05-23 | https://chat-bi.summerfarm.net/dashboard?chat=om_x100b4dc8ee00502c0f2e13fff6750f0 |\n", "| om_x100b4dda479761c40e3f4980ad77009 | 陈默 | 我的客户中购买法瑞芙大黄油, 25KG*1箱/25KG, SKU: 584027027103的客户 | 2025-05-22 | https://chat-bi.summerfarm.net/dashboard?chat=om_x100b4dda479761c40e3f4980ad77009 |\n", "| 9517b022-45a5-4cf3-976f-b12c6fb6940e | 陈强 | 高价值客户数近3个月履约数量,高价值客户数（履约实付金额超过一定阈值（如2000元）且购买商品种数达到一定数量（如4种）的客户）近3个月(2月、3月、4月)履约客户数量，按照销售区域分组展示 | 2025-05-22 | https://chat-bi.summerfarm.net/dashboard?chat=9517b022-45a5-4cf3-976f-b12c6fb6940e |\n", "| om_x100b4dda701504840f30b331e98380b | 陈彤彤 | 立高东莞仓什么时候到货？ | 2025-05-22 | https://chat-bi.summerfarm.net/dashboard?chat=om_x100b4dda701504840f30b331e98380b |\n"]}], "source": ["CHATBI_DB_HOST = os.getenv(\"CHATBI_MYSQL_HOST\", \"mysql-xm.summerfarm.net\")\n", "CHATBI_DB_PORT = int(os.getenv(\"CHATBI_MYSQL_PORT\", 3308))\n", "CHATBI_DB_USER = os.getenv(\"CHATBI_MYSQL_USER\", \"qa\")\n", "CHATBI_DB_PASSWORD = os.getenv(\"CHATBI_MYSQL_PASSWORD\", \"xianmu619\")\n", "CHATBI_DB_NAME = \"chatbi\"\n", "CHATBI_POOL_NAME = \"chatbi_pool\"\n", "CHATBI_POOL_SIZE = int(os.getenv(\"CHATBI_MYSQL_POOL_SIZE\", 5))  # 可配置的池大小\n", "CHATBI_CONNECT_TIMEOUT = int(os.getenv(\"CHATBI_CONNECT_TIMEOUT\", 10))  # 连接超时（秒）\n", "\n", "# 建立数据库连\n", "import mysql.connector\n", "from mysql.connector import pooling\n", "\n", "dbconfig = {\n", "    \"host\": CHATBI_DB_HOST,\n", "    \"port\": CHATBI_DB_PORT,\n", "    \"user\": CHATBI_DB_USER,\n", "    \"password\": CHATBI_DB_PASSWORD,\n", "    \"database\": CHATBI_DB_NAME,\n", "    \"connection_timeout\": CHATBI_CONNECT_TIMEOUT,\n", "}\n", "\n", "pool = mysql.connector.pooling.MySQLConnectionPool(\n", "    pool_name=CHATBI_POOL_NAME, pool_size=CHATBI_POOL_SIZE, **dbconfig\n", ")\n", "\n", "conn = pool.get_connection()\n", "cur = conn.cursor()\n", "cur.execute(\"\"\"select max(id) as max_id,conversation_id,username,group_concat(content) as user_content,\n", "            date_format(max(created_at),'%Y-%m-%d') as last_created_at,\n", "            concat('https://chat-bi.summerfarm.net/dashboard?chat=',conversation_id) as dashboard_link\n", "            from chat_history \n", "            where is_bad_case = 1 and role = 'user' \n", "            group by conversation_id,username\n", "            order by max_id desc limit 20;\"\"\")\n", "rows = cur.fetchall()\n", "\n", "# 将结果格式化为markdown表格\n", "print(\"| 会话ID | 用户名 | 用户内容 | 最后创建时间 | 仪表盘链接 |\")\n", "print(\"|--------|--------|----------|--------------|------------|\")\n", "\n", "for row in rows:\n", "    # 转义markdown特殊字符\n", "    content = str(row[3]).replace(\"|\", \"\\\\|\").replace(\"\\n\", \" \")\n", "    print(f\"| {row[1]} | {row[2]} | {content} | {row[4]} | {row[5]} |\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from openai import OpenAI\n", "import os\n", "\n", "client = OpenAI(base_url=OPENAI_API_BASE, api_key=OPENAI_API_KEY)\n", "\n", "OPENAI_MODEL = os.getenv(\"OPENAI_MODEL\")\n", "\n", "SYSTEM_PROMPT = \"\"\"请你根据以下表结构定义语句，以及样例数据，重新补充完整DDL语句的注释部分。\n", "\n", "请注意：\n", "1. 注释部分需要用中文描述。\n", "2. 注释部分需要详细描述表的各个字段的含义。\n", "3. 注释部分用200字来作为该表的注释描述表的主要功能。\n", "4. 结合样例数据，尽可能为每个字段提供数据举例。\n", "5. 为了避免繁杂的数据，请你把DDL语句精简，把那些和业务无关的字段和索引部分去掉。\n", "这样AI才好用来根据DDL语句编写SQL，用以回答问题。\"\"\"\n", "\n", "\n", "def refine_ddl(ddl_doc: str) -> str:\n", "    response = client.chat.completions.create(\n", "        model=OPENAI_MODEL,\n", "        messages=[\n", "            {\n", "                \"role\": \"prompt\",\n", "                \"content\": SYSTEM_PROMPT,\n", "            },\n", "            {\n", "                \"role\": \"user\",\n", "                \"content\": f\"请你直接返回补充完整后的DDL语句，不要返回其他内容:\\n\\n{ddl_doc}\",\n", "            },\n", "        ],\n", "        stream=True,\n", "    )\n", "\n", "    # 使用生成器表达式迭代处理流式响应\n", "    # response.choices[0].delta.content 总是 None, 因此需要迭代\n", "    collected_chunks = []\n", "    for chunk in response:\n", "        if chunk.choices[0].delta.content is not None:\n", "            chunk_text = chunk.choices[0].delta.content\n", "            print(chunk_text, end=\"\")\n", "            collected_chunks.append(chunk_text)\n", "    return \"\".join(collected_chunks)\n", "\n", "\n", "merchant_tables = [\n", "    \"orders\",\n", "    \"order_item\",\n", "    \"merchant\",\n", "    \"inventory\",\n", "    # \"products\",\n", "    # \"category\",\n", "    # \"crm_bd_org\",\n", "    # \"follow_up_record\",\n", "    # \"follow_up_relation\",\n", "    \"warehouse_storage_center\",\n", "    # \"warehouse_logistics_center\",\n", "    \"area\",\n", "    \"delivery_plan\",\n", "    # \"admin\",\n", "]\n", "\n", "total_docs = \"\"\n", "\n", "for table_name in merchant_tables:\n", "    with open(f\"{WORKING_DIR}/{table_name}_ddl.md\", \"r\") as f:\n", "        ddl_doc = f.read()\n", "    refined_ddl_doc = refine_ddl(ddl_doc)\n", "    with open(f\"{WORKING_DIR}/{table_name}_ddl_refined.md\", \"w\") as f:\n", "        f.write(refined_ddl_doc)\n", "        total_docs += refined_ddl_doc\n", "        total_docs += \"\\n\\n\"\n", "\n", "# write total_docs into file:\n", "with open(f\"{WORKING_DIR}/total_docs.md\", \"w\") as f:\n", "    f.write(total_docs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# read from file f\"{WORKING_DIR}/total_docs.md\"\n", "total_docs = open(f\"{WORKING_DIR}/total_docs.md\", \"r\").read()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from typing_extensions import Any, Dict\n", "from openai import AsyncOpenAI\n", "\n", "# 导入 agents 库的相关组件\n", "from agents import (\n", "    Agent,\n", "    Runner,\n", "    function_tool,\n", "    set_tracing_disabled,\n", "    OpenAIChatCompletionsModel,\n", "    <PERSON><PERSON><PERSON><PERSON>,\n", "    Model,\n", "    RunConfig,\n", ")\n", "\n", "# 导入自定义的 MySQL 客户端函数\n", "from services.db.mysql_client import execute_sql_query\n", "from openai.types.responses import ResponseTextDeltaEvent\n", "\n", "# 假设 OPENAI_API_KEY, OPENAI_API_BASE, OPENAI_MODEL, total_docs 在之前的单元格已定义\n", "\n", "# 设置 agents 库使用的默认 OpenAI 客户端\n", "# 确保 OPENAI_API_KEY 和 OPENAI_API_BASE 已正确设置\n", "client = AsyncOpenAI(base_url=OPENAI_API_BASE, api_key=OPENAI_API_KEY)\n", "set_tracing_disabled(disabled=True)\n", "\n", "\n", "class CustomModelProvider(ModelProvider):\n", "    def get_model(self, model_name: str = None) -> Model:\n", "        return OpenAIChatCompletionsModel(\n", "            model=model_name or OPENAI_MODEL, openai_client=client\n", "        )\n", "\n", "\n", "CUSTOM_MODEL_PROVIDER = CustomModelProvider()\n", "\n", "\n", "# 定义一个函数工具，用于执行 MySQL 查询\n", "@function_tool\n", "async def fetch_mysql_sql_result(\n", "    sql: str, description: str\n", ") -> tuple[Dict[str, Any], str]:\n", "    \"\"\"使用SQL来查询MySQL数据库，返回查询结果(字典格式)和对查询的描述。\n", "\n", "    Args:\n", "        sql: 要执行的 SQL 查询语句。\n", "        description: 对 SQL 查询的简短描述。\n", "    \"\"\"\n", "    # 调用 mysql_client 中的函数执行查询\n", "    # 假设 execute_sql_query 返回一个适合 agent 使用的字典格式结果\n", "    return execute_sql_query(sql), description\n", "\n", "\n", "# 主异步函数\n", "async def main():\n", "    # 创建 Agent 实例\n", "    # 确保提供了模型名称和所需的工具\n", "    agent = Agent(\n", "        name=\"MySQL Expert Assistant\",  # 更具描述性的 Agent 名称\n", "        instructions=f\"你是一个MySQL数据专家，专门帮助用户查询MySQL数据库。以下是数据库的DDL语句：\\n\\n{total_docs}\\n\\n请根据用户的问题生成并执行SQL查询。请注意不要使用with子句，因为该子句已经被服务端禁止。\",\n", "        model=OPENAI_MODEL,  # 明确指定要使用的模型\n", "        tools=[\n", "            fetch_mysql_sql_result,  # 将 SQL 查询工具提供给 Agent\n", "        ],\n", "    )\n", "\n", "    # 定义用户输入的问题\n", "    user_input = \"查询注册城市为杭州市的用户，3月下单额比对2月下单额下降最大的20个门店是哪些？列出门店名称和下单金额、最后下单日、下单商品列表（聚合），按照2月下单额降序排序\"\n", "\n", "    print(f\"User Query: {user_input}\\n\")\n", "    print(\"Agent Response:\\n\")\n", "\n", "    # 使用 Runner 流式运行 Agent\n", "    result = Runner.run_streamed(\n", "        agent,\n", "        input=user_input,\n", "        run_config=RunConfig(model_provider=CUSTOM_MODEL_PROVIDER),\n", "    )\n", "\n", "    # 异步迭代处理流式事件\n", "    async for event in result.stream_events():\n", "        # 检查事件类型是否为原始响应事件，并且数据是文本增量\n", "        # 注意：'raw_response_event' 是 agents 库定义的事件类型，需要确认是否准确\n", "        # 如果 agents 库有更具体的事件类型如 'text_delta_event'，应使用那个\n", "        if event.type == \"raw_response_event\" and isinstance(\n", "            event.data, ResponseTextDeltaEvent\n", "        ):\n", "            # 打印收到的文本增量，不换行，并立即刷新输出\n", "            # 检查 delta 是否为 None，虽然理论上 isinstance 检查后不应为 None，但增加健壮性\n", "            if event.data.delta:\n", "                print(event.data.delta, end=\"\", flush=True)\n", "\n", "    print(\"\\n--- End of Response ---\")  # 标记响应结束\n", "\n", "\n", "await main()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.6"}}, "nbformat": 4, "nbformat_minor": 2}