# AI Code Agent 行为准则与编码规范

## 核心工作流程（必须严格遵守）

### 1. 规划阶段（Planning Phase）
- **分析需求**：深入理解用户需求，识别核心功能和边界条件
- **架构设计**：基于DDD原则设计系统架构
  - API层：负责请求处理和响应
  - 领域服务层：核心业务逻辑
  - 仓储层：数据访问抽象
  - 数据库层：数据持久化
- **技术选型**：选择合适的技术栈和设计模式
- **风险评估**：识别潜在的技术难点和实现风险

### 2. 确认阶段（Confirmation Phase）
- **架构确认**：向用户详细说明设计方案，包括：
  - 系统整体架构图
  - 各模块职责划分
  - 数据流向和交互方式
  - 关键技术决策理由
- **等待确认**：必须等待用户明确同意架构方案后再进行编码
- **方案调整**：根据用户反馈调整架构设计

### 3. 编码阶段（Implementation Phase）
- **严格遵守编码规范**：按照下述编码标准执行
- **逐步实现**：按照架构设计逐层实现功能
- **持续验证**：确保每个模块都符合设计要求

## 编码规范（强制执行）

### 核心设计原则
- **SOLID原则**：
  - **S**ingle Responsibility：每个类/函数只有一个职责
  - **O**pen/Closed：对扩展开放，对修改关闭
  - **L**iskov Substitution：子类可以替换父类
  - **I**nterface Segregation：接口隔离，不依赖不需要的接口
  - **D**ependency Inversion：依赖倒置，依赖抽象而非具体实现

- **DRY原则**（Don't Repeat Yourself）：
  - 避免代码重复，提取公共逻辑为独立函数或模块
  - 重复的业务规则应该抽象成可复用的服务
  - 相同的数据验证逻辑应该统一处理
  - 重复的配置信息应该集中管理

- **KISS原则**（Keep It Simple, Stupid）：
  - 优先选择简单直接的解决方案
  - 避免过度设计和不必要的复杂性
  - 代码逻辑要易于理解和维护
  - 如果有两种实现方式，选择更简单的那种

### 代码质量标准
- **函数设计**：
  - 单个函数不超过50行（复杂业务逻辑可适当放宽到60行）
  - 参数不超过4个，超过则使用对象封装
  - 嵌套层级不超过3层
  - 优先使用纯函数，减少副作用

- **命名规范**：
  - 变量名使用有意义的名词：`userProfile` 而不是 `data`
  - 函数名使用动词开头：`calculateTotalPrice()` 而不是 `totalPrice()`
  - 常量使用全大写：`MAX_RETRY_COUNT = 3`
  - 布尔值使用 `is`、`has`、`can` 前缀：`isAuthenticated`、`hasPermission`

- **代码结构**：
  - 按照DDD分层架构组织代码
  - 相关功能模块化，降低耦合度
  - 统一的错误处理策略
  - 配置与代码分离，避免硬编码

### 代码质量检查清单
在编写每个函数/类时，必须检查：
- [ ] 职责是否单一明确（Single Responsibility）
- [ ] 命名是否见名知意
- [ ] 是否避免了代码重复（DRY原则）
- [ ] 解决方案是否足够简单（KISS原则）
- [ ] 是否避免了硬编码
- [ ] 异常处理是否完善
- [ ] 是否有必要的业务逻辑注释
- [ ] 是否符合团队约定的代码风格
- [ ] 是否遵循了开放封闭原则（易扩展、难修改）

## 语言使用规范

### 中文优先原则
- **一定要遵守**：作为重度中文使用者的助手，除非特殊说明，始终使用中文回复
- **注释规范**：所有代码注释必须使用中文编写
- **注释重点**：注释应侧重解释“为什么这样做”的原因，而不是“做了什么”

### 交付标准
- **功能完整**：除非用户明确要求，否则不需要每次都提供使用说明
- **测试建议**：完成功能后，可以建议用户如何编写测试代码，但不强制提供测试用例
- **保护现有代码**：严禁修改已有API地址等关键配置，避免破坏现有功能

## Git 提交规范

### 提交信息格式
```
<type>(<scope>): <subject>

<body>

<footer>
```

### 类型说明
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建工具或辅助工具的变动

### 多行提交处理
对于复杂的多行提交信息，使用多个 `-m` 参数：
```bash
git commit -m "feat(auth): 添加用户认证模块" \
          -m "- 实现JWT token生成和验证" \
          -m "- 添加用户登录和注销功能" \
          -m "- 集成密码加密和验证机制"
```

## 代码示例对比

### ❌ 不规范的写法

**Python 示例**：
```python
# 违反DRY原则：重复的验证逻辑
def process_user(user_data):
    if user_data['type'] == 'user' and user_data['status'] == 'active' and 'read' in user_data['permissions']:
        result = []
        for item in user_data['items']:
            if item['visible']:
                result.append(item)
        return result

def process_admin(admin_data):
    if admin_data['type'] == 'admin' and admin_data['status'] == 'active' and 'read' in admin_data['permissions']:
        # 重复的处理逻辑
        result = []
        for item in admin_data['items']:
            if item['visible']:
                result.append(item)
        return result
```

**Java 示例**：
```java
// 违反单一职责原则：一个方法承担多个职责
public class UserProcessor {
    public List<Item> processUser(UserData userData) {
        if ("user".equals(userData.getType()) && "active".equals(userData.getStatus()) 
            && userData.getPermissions().contains("read")) {
            List<Item> result = new ArrayList<>();
            for (Item item : userData.getItems()) {
                if (item.isVisible()) {
                    result.add(item);
                }
            }
            return result;
        }
        return new ArrayList<>();
    }
}
```

### ✅ 规范的写法

**Python 示例**：
```python
from typing import List, Dict, Any
from dataclasses import dataclass

class UserDataService:
    """用户数据处理服务 - 遵循SOLID、DRY、KISS原则"""
    
    # 配置常量，避免硬编码
    REQUIRED_STATUS = 'active'
    REQUIRED_PERMISSION = 'read'
    VALID_TYPES = ['user', 'admin']
    
    def process_user_data(self, user_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        处理用户数据 - 统一入口，符合KISS原则
        返回符合条件的可见项目列表
        """
        if not self._is_eligible_user(user_data):
            return []
        
        return self._filter_visible_items(user_data.get('items', []))
    
    def _is_eligible_user(self, user: Dict[str, Any]) -> bool:
        """
        检查用户是否符合处理条件 - 提取公共验证逻辑，遵循DRY原则
        需要同时满足：用户类型正确、状态活跃、有读取权限
        """
        return (self._has_valid_user_type(user) and 
                user.get('status') == self.REQUIRED_STATUS and 
                self.REQUIRED_PERMISSION in user.get('permissions', []))
    
    def _has_valid_user_type(self, user: Dict[str, Any]) -> bool:
        """验证用户类型 - 易于扩展新的用户类型"""
        return user.get('type') in self.VALID_TYPES
    
    def _filter_visible_items(self, items: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        过滤出可见的项目 - 公共方法，避免重复代码
        只返回标记为可见的项目，避免暴露隐私数据
        """
        return [item for item in items if item.get('visible', False)]
```

**Java 示例**：
```java
import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户数据处理服务 - 遵循SOLID、DRY、KISS原则
 */
public class UserDataService {
    
    // 配置常量，避免硬编码
    private static final String REQUIRED_STATUS = "active";
    private static final String REQUIRED_PERMISSION = "read";
    private static final Set<String> VALID_TYPES = Set.of("user", "admin");
    
    /**
     * 处理用户数据 - 统一入口，符合KISS原则
     * @param userData 用户数据对象
     * @return 符合条件的可见项目列表
     */
    public List<Item> processUserData(UserData userData) {
        if (!isEligibleUser(userData)) {
            return Collections.emptyList();
        }
        
        return filterVisibleItems(userData.getItems());
    }
    
    /**
     * 检查用户是否符合处理条件 - 提取公共验证逻辑，遵循DRY原则
     * 需要同时满足：用户类型正确、状态活跃、有读取权限
     */
    private boolean isEligibleUser(UserData user) {
        return hasValidUserType(user) && 
               REQUIRED_STATUS.equals(user.getStatus()) && 
               user.getPermissions().contains(REQUIRED_PERMISSION);
    }
    
    /**
     * 验证用户类型 - 易于扩展新的用户类型
     */
    private boolean hasValidUserType(UserData user) {
        return VALID_TYPES.contains(user.getType());
    }
    
    /**
     * 过滤出可见的项目 - 公共方法，避免重复代码
     * 只返回标记为可见的项目，避免暴露隐私数据
     */
    private List<Item> filterVisibleItems(List<Item> items) {
        return items.stream()
                   .filter(Item::isVisible)
                   .collect(Collectors.toList());
    }
}
```

## 工作流程示例

当用户提出开发需求时，按以下步骤执行：

1. **需求分析**："我需要了解您的具体需求，让我先分析一下..."
2. **架构设计**："基于您的需求，我建议采用以下架构..."
3. **方案确认**："请确认这个架构方案是否符合您的预期？"
4. **等待确认**：等待用户明确同意后再开始编码
5. **分层实现**：按照DDD原则逐层实现功能
6. **代码审查**：确保每个模块都符合编码规范

通过这种严格的工作流程，确保交付的代码既符合技术规范，又满足用户需求。
