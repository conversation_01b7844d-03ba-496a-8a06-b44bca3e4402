# 定义基础镜像 (base stage) 用于安装依赖
FROM xianmu-registry-registry.cn-hangzhou.cr.aliyuncs.com/base/python:3.12-slim-uv AS base

# Copy the project into the image
ADD . /app

# Sync the project into a new environment, asserting the lockfile is up to date
WORKDIR /app

# 设置 OpenTelemetry 环境变量，避免 service.name 为 None 的错误
ENV OTEL_SERVICE_NAME=test-chat-bi
ENV OTEL_SERVICE_VERSION=0.1.0
ENV OTEL_RESOURCE_ATTRIBUTES=service.name=test-chat-bi,service.version=0.1.0
# 暂时禁用 OpenTelemetry SDK 以避免启动问题
# ENV OTEL_SDK_DISABLED=true
# Set pip mirror via environment variables
ENV PIP_INDEX_URL=https://mirrors.aliyun.com/pypi/simple/
ENV PIP_TRUSTED_HOST=mirrors.aliyun.com

ENV ARMS_IS_PUBLIC=true

RUN uv --version && uv venv --seed && uv sync --locked \
    && python -V \
    && uv pip install aliyun-bootstrap
# Create pip config directory for the virtual environment
RUN mkdir -p /app/.venv/pip && \
    echo '[global]' > /app/.venv/pip/pip.conf && \
    echo 'index-url = https://mirrors.aliyun.com/pypi/simple/' >> /app/.venv/pip/pip.conf && \
    echo 'trusted-host = mirrors.aliyun.com' >> /app/.venv/pip/pip.conf \
    && ARMS_REGION_ID=cn-hangzhou .venv/bin/aliyun-bootstrap -a install


COPY env.test .env

# 设置时区为北京时间
RUN ln -snf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo Asia/Shanghai > /etc/timezone

# 使 5700 端口对容器外部世界可用
EXPOSE 5700

# 设置虚拟环境的PATH
ENV PATH="/app/.venv/bin:$PATH"

CMD ["aliyun-instrument", ".venv/bin/gunicorn", "-w", "1", "-b", "0.0.0.0:5700", "--timeout", "60", "--graceful-timeout", "10", "main:app"]