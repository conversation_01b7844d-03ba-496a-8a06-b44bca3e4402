"""
定时清理任务调度器

定期执行消息清理任务，确保系统健康运行
"""

import schedule
import time
import threading
from datetime import datetime
from src.services.chatbot.message_cleanup_service import message_cleanup_service
from src.utils.logger import logger

class CleanupScheduler:
    """清理任务调度器"""
    
    def __init__(self):
        self.is_running = False
        self.scheduler_thread = None
        
    def run_cleanup_task(self):
        """执行清理任务"""
        try:
            logger.info("开始执行定时清理任务")
            
            # 获取系统健康状态
            health_info = message_cleanup_service.health_check()
            logger.info(f"系统健康状态: {health_info}")
            
            # 如果有过期消息，执行清理
            if health_info.get('stale_messages', 0) > 0:
                stats = message_cleanup_service.cleanup_all_stale_messages()
                logger.info(f"定时清理任务完成: {stats}")
                
                # 如果清理了大量消息，发送告警
                if stats.get('cleaned', 0) > 10:
                    logger.warning(f"清理了大量过期消息: {stats['cleaned']} 个，请检查系统状态")
            else:
                logger.info("没有发现过期消息，跳过清理")
                
        except Exception as e:
            logger.exception(f"定时清理任务执行失败: {e}", exc_info=True)
    
    def start_scheduler(self):
        """启动调度器"""
        if self.is_running:
            logger.warning("清理调度器已在运行中")
            return
        
        logger.info("启动消息清理调度器")
        
        # 配置定时任务
        # 每小时执行一次清理检查
        schedule.every().hour.do(self.run_cleanup_task)
        
        # 每天凌晨2点执行深度清理
        schedule.every().day.at("02:00").do(self.run_cleanup_task)
        
        # 启动调度器线程
        self.is_running = True
        self.scheduler_thread = threading.Thread(target=self._run_scheduler, daemon=True)
        self.scheduler_thread.start()
        
        logger.info("消息清理调度器启动成功")
    
    def stop_scheduler(self):
        """停止调度器"""
        if not self.is_running:
            return
        
        logger.info("停止消息清理调度器")
        self.is_running = False
        
        # 清除所有任务
        schedule.clear()
        
        if self.scheduler_thread and self.scheduler_thread.is_alive():
            self.scheduler_thread.join(timeout=5)
        
        logger.info("消息清理调度器已停止")
    
    def _run_scheduler(self):
        """调度器主循环"""
        logger.info("清理调度器线程启动")
        
        while self.is_running:
            try:
                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次
            except Exception as e:
                logger.exception(f"调度器运行异常: {e}", exc_info=True)
                time.sleep(60)
        
        logger.info("清理调度器线程结束")
    
    def run_manual_cleanup(self):
        """手动执行清理任务"""
        logger.info("手动执行清理任务")
        self.run_cleanup_task()

# 全局调度器实例
cleanup_scheduler = CleanupScheduler()

def start_cleanup_scheduler():
    """启动清理调度器"""
    cleanup_scheduler.start_scheduler()

def stop_cleanup_scheduler():
    """停止清理调度器"""
    cleanup_scheduler.stop_scheduler()

def manual_cleanup():
    """手动清理"""
    cleanup_scheduler.run_manual_cleanup()
