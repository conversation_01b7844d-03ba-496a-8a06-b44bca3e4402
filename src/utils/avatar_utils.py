"""
Avatar utility functions.

This module provides utility functions for handling user avatars.
"""

import hashlib
from typing import Optional


def get_user_avatar(user_info: dict, user_email: Optional[str] = None) -> str:
    """
    获取用户头像URL，优先使用数据库中的avatar字段，如果没有则使用飞书API返回的avatar_thumb，
    如果都没有或者是"None"，则使用Gravatar默认头像服务。
    
    Args:
        user_info (dict): 用户信息字典
        user_email (str, optional): 用户邮箱，如果不提供则从user_info中获取
        
    Returns:
        str: 头像URL
    """
    # 优先使用数据库中的avatar字段，如果没有则使用飞书API返回的avatar_thumb
    user_avatar = user_info.get("avatar") or user_info.get("avatar_thumb")
    
    # 如果都没有或者是"None"字符串，则使用Gravatar默认头像服务
    if not user_avatar or user_avatar == "None":
        # 获取用户邮箱
        if not user_email:
            user_email = user_info.get("email", "")
        
        if user_email:
            # 使用Gravatar默认头像服务，基于用户邮箱生成
            email_hash = hashlib.md5(user_email.lower().encode('utf-8')).hexdigest()
            return f"https://www.gravatar.com/avatar/{email_hash}?d=identicon&s=40"
        else:
            # 如果连邮箱都没有，使用一个通用的默认头像
            return "https://www.gravatar.com/avatar/00000000000000000000000000000000?d=identicon&s=40"
    
    return user_avatar


def generate_gravatar_url(email: str, size: int = 40, default: str = "identicon") -> str:
    """
    生成Gravatar头像URL
    
    Args:
        email (str): 用户邮箱
        size (int): 头像大小，默认40px
        default (str): 默认头像类型，默认为identicon
        
    Returns:
        str: Gravatar头像URL
    """
    email_hash = hashlib.md5(email.lower().encode('utf-8')).hexdigest()
    return f"https://www.gravatar.com/avatar/{email_hash}?d={default}&s={size}"