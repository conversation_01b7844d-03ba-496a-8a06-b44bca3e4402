"""
Conversation Context Manager

提供基于线程本地存储的conversation_id上下文管理功能，
确保在整个请求处理链路中conversation_id的正确传递和隔离。

核心功能：
1. 线程本地存储conversation_id
2. 上下文生命周期管理（设置、获取、清理）
3. 线程间隔离保证
4. 离线任务保护机制
"""

import threading
import uuid
from typing import Optional, Any, Dict
from contextlib import contextmanager


class ConversationContext:
    """
    Conversation上下文管理器
    
    使用threading.local确保不同线程间的conversation_id完全隔离，
    避免对话A的conversation_id影响对话B。
    """
    
    def __init__(self):
        # 使用threading.local确保线程安全
        self._local = threading.local()
        
    def set_conversation_id(self, conversation_id: str) -> None:
        """
        设置当前线程的conversation_id
        
        Args:
            conversation_id: 对话ID
        """
        if not conversation_id:
            logger.warning("尝试设置空的conversation_id")
            return
            
        self._local.conversation_id = conversation_id
        self._local.thread_id = threading.get_ident()
        # 延迟导入logger以避免循环导入
        try:
            from src.utils.logger import logger
            logger.debug(f"设置conversation_id: {conversation_id} (线程: {self._local.thread_id})")
        except ImportError:
            pass  # 如果导入失败，跳过日志记录
        
    def get_conversation_id(self) -> Optional[str]:
        """
        获取当前线程的conversation_id
        
        Returns:
            当前线程的conversation_id，如果未设置则返回None
        """
        return getattr(self._local, 'conversation_id', None)
        
    def clear_conversation_id(self) -> None:
        """
        清理当前线程的conversation_id
        """
        conversation_id = self.get_conversation_id()
        if conversation_id:
            # 延迟导入logger以避免循环导入
            try:
                from src.utils.logger import logger
                logger.debug(f"清理conversation_id: {conversation_id} (线程: {threading.get_ident()})")
            except ImportError:
                pass  # 如果导入失败，跳过日志记录

        # 清理所有本地存储的属性
        if hasattr(self._local, 'conversation_id'):
            delattr(self._local, 'conversation_id')
        if hasattr(self._local, 'thread_id'):
            delattr(self._local, 'thread_id')
            
    def get_context_info(self) -> Dict[str, Any]:
        """
        获取当前上下文的详细信息（用于调试）
        
        Returns:
            包含上下文信息的字典
        """
        return {
            'conversation_id': self.get_conversation_id(),
            'thread_id': threading.get_ident(),
            'stored_thread_id': getattr(self._local, 'thread_id', None),
            'has_context': hasattr(self._local, 'conversation_id')
        }
        
    @contextmanager
    def conversation_scope(self, conversation_id: str):
        """
        Conversation上下文管理器，确保在作用域结束时自动清理
        
        Args:
            conversation_id: 对话ID
            
        Usage:
            with conversation_context.conversation_scope("conv_123"):
                # 在这个作用域内，所有日志都会包含conversation_id
                logger.info("这条日志会包含conversation_id")
        """
        old_conversation_id = self.get_conversation_id()
        
        try:
            self.set_conversation_id(conversation_id)
            yield
        finally:
            # 恢复之前的conversation_id（如果有的话）
            if old_conversation_id:
                self.set_conversation_id(old_conversation_id)
            else:
                self.clear_conversation_id()


# 全局单例实例
conversation_context = ConversationContext()


def get_current_conversation_id() -> Optional[str]:
    """
    便捷函数：获取当前conversation_id
    
    Returns:
        当前线程的conversation_id，如果未设置则返回None
    """
    return conversation_context.get_conversation_id()


def set_current_conversation_id(conversation_id: str) -> None:
    """
    便捷函数：设置当前conversation_id
    
    Args:
        conversation_id: 对话ID
    """
    conversation_context.set_conversation_id(conversation_id)


def clear_current_conversation_id() -> None:
    """
    便捷函数：清理当前conversation_id
    """
    conversation_context.clear_conversation_id()


def generate_conversation_id() -> str:
    """
    生成新的conversation_id
    
    Returns:
        新的UUID格式的conversation_id
    """
    return str(uuid.uuid4())


def is_offline_task() -> bool:
    """
    检测当前是否为离线任务（如定时任务、后台服务）
    
    离线任务通常不应该有conversation_id，这个函数用于保护机制。
    
    Returns:
        如果是离线任务返回True，否则返回False
    """
    # 检查线程名称，离线任务通常有特定的线程名称模式
    thread_name = threading.current_thread().name
    
    # 常见的离线任务线程名称模式
    offline_patterns = [
        'token_refresh',
        'session_cleanup', 
        'db_monitor',
        'background',
        'scheduler',
        'timer',
        'daemon'
    ]
    
    return any(pattern in thread_name.lower() for pattern in offline_patterns)


def safe_get_conversation_id() -> Optional[str]:
    """
    安全获取conversation_id，对离线任务返回None
    
    这个函数确保离线任务不会意外获取到其他对话的conversation_id。
    
    Returns:
        对话ID或None（离线任务时）
    """
    if is_offline_task():
        return None
    return get_current_conversation_id()
