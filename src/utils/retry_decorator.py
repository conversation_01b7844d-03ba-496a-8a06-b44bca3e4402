"""
通用重试装饰器工具模块

提供异步函数的重试功能，特别针对飞书API的限流和并发控制场景优化。
遵循DRY原则，避免在各个函数中重复实现重试逻辑。
"""

import asyncio
import random
import functools
from typing import Callable, Any, Optional, Union, List
from src.utils.logger import logger


def retryable(
    max_times: int = 3,
    min_wait_seconds: float = 1.0,
    max_wait_seconds: float = 2.0,
    rate_limit_keywords: Optional[List[str]] = None,
    retry_on_exceptions: Optional[List[type]] = None,
):
    """
    异步函数重试装饰器
    
    专为飞书API等外部服务调用设计，支持限流检测和智能重试策略。
    当遇到限流错误或指定异常时，会随机等待指定时间后重试。
    
    Args:
        max_times: 最大重试次数，默认3次
        min_wait_seconds: 最小等待时间（秒），默认1.0秒
        max_wait_seconds: 最大等待时间（秒），默认2.0秒
        rate_limit_keywords: 用于检测限流错误的关键词列表，默认包含飞书限流关键词
        retry_on_exceptions: 需要重试的异常类型列表，默认为None表示重试所有异常
    
    Returns:
        装饰后的异步函数
        
    使用示例:
        @retryable(max_times=3, min_wait_seconds=1, max_wait_seconds=2)
        async def call_feishu_api():
            # API调用逻辑
            pass
            
        @retryable(max_times=5, min_wait_seconds=0.5, max_wait_seconds=1.5)
        async def custom_retry_function():
            # 自定义重试参数的函数
            pass
    """
    # 默认的限流检测关键词（主要针对飞书API）
    if rate_limit_keywords is None:
        rate_limit_keywords = ["request trigger frequency limit", "rate limit", "too many requests"]
    
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs) -> Any:
            retry_count = 0
            func_name = func.__name__
            
            while retry_count <= max_times:
                try:
                    # 记录重试信息（仅在重试时记录，避免正常调用时的日志噪音）
                    if retry_count > 0:
                        logger.debug(
                            f"函数 {func_name} 第 {retry_count} 次重试，参数: args={len(args)}, kwargs={list(kwargs.keys())}"
                        )
                    
                    # 执行原函数
                    result = await func(*args, **kwargs)
                    
                    # 成功执行，返回结果
                    if retry_count > 0:
                        logger.info(f"函数 {func_name} 重试成功，总重试次数: {retry_count}")
                    
                    return result
                    
                except Exception as e:
                    error_msg = str(e).lower()
                    should_retry = False
                    
                    # 检查是否是需要重试的异常类型
                    if retry_on_exceptions is None or any(isinstance(e, exc_type) for exc_type in retry_on_exceptions):
                        should_retry = True
                    
                    # 检查是否是限流错误（优先级更高）
                    is_rate_limit = any(keyword in error_msg for keyword in rate_limit_keywords)
                    
                    if should_retry and retry_count < max_times:
                        # 计算等待时间
                        sleep_time = random.uniform(min_wait_seconds, max_wait_seconds)
                        
                        # 根据错误类型记录不同级别的日志
                        if is_rate_limit:
                            logger.warning(
                                f"函数 {func_name} 遇到限流错误，等待 {sleep_time:.2f} 秒后重试 (第 {retry_count + 1} 次)，错误: {str(e)}"
                            )
                        else:
                            logger.warning(
                                f"函数 {func_name} 执行异常，等待 {sleep_time:.2f} 秒后重试 (第 {retry_count + 1} 次)，错误: {str(e)}"
                            )
                        
                        # 等待后重试
                        await asyncio.sleep(sleep_time)
                        retry_count += 1
                        continue
                    else:
                        # 达到最大重试次数或不需要重试的异常，直接抛出
                        if retry_count >= max_times:
                            logger.error(
                                f"函数 {func_name} 达到最大重试次数 {max_times}，最终失败，错误: {str(e)}"
                            )
                        else:
                            logger.error(
                                f"函数 {func_name} 遇到不可重试的异常，错误: {str(e)}"
                            )
                        raise e
            
            # 理论上不会到达这里，但为了代码完整性
            raise RuntimeError(f"函数 {func_name} 重试逻辑异常")
        
        return wrapper
    return decorator


def retryable_with_response_check(
    max_times: int = 3,
    min_wait_seconds: float = 1.0,
    max_wait_seconds: float = 2.0,
    response_check_func: Optional[Callable[[Any], bool]] = None,
):
    """
    带响应检查的重试装饰器
    
    除了异常重试外，还支持基于返回值的重试逻辑。
    适用于API调用成功但返回结果不符合预期的场景。
    
    Args:
        max_times: 最大重试次数
        min_wait_seconds: 最小等待时间
        max_wait_seconds: 最大等待时间  
        response_check_func: 响应检查函数，返回True表示成功，False表示需要重试
        
    Returns:
        装饰后的异步函数
        
    使用示例:
        def check_api_response(response):
            return response.get('code') == 0
            
        @retryable_with_response_check(response_check_func=check_api_response)
        async def call_api():
            return await some_api_call()
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs) -> Any:
            retry_count = 0
            func_name = func.__name__
            
            while retry_count <= max_times:
                try:
                    result = await func(*args, **kwargs)
                    
                    # 如果没有提供检查函数，或检查通过，直接返回结果
                    if response_check_func is None or response_check_func(result):
                        if retry_count > 0:
                            logger.info(f"函数 {func_name} 响应检查通过，重试成功，总重试次数: {retry_count}")
                        return result
                    
                    # 响应检查失败，需要重试
                    if retry_count < max_times:
                        sleep_time = random.uniform(min_wait_seconds, max_wait_seconds)
                        logger.warning(
                            f"函数 {func_name} 响应检查失败，等待 {sleep_time:.2f} 秒后重试 (第 {retry_count + 1} 次)"
                        )
                        await asyncio.sleep(sleep_time)
                        retry_count += 1
                        continue
                    else:
                        logger.error(f"函数 {func_name} 响应检查失败，已达到最大重试次数")
                        return result  # 返回最后一次的结果
                        
                except Exception as e:
                    if retry_count < max_times:
                        sleep_time = random.uniform(min_wait_seconds, max_wait_seconds)
                        logger.warning(
                            f"函数 {func_name} 执行异常，等待 {sleep_time:.2f} 秒后重试 (第 {retry_count + 1} 次)，错误: {str(e)}"
                        )
                        await asyncio.sleep(sleep_time)
                        retry_count += 1
                        continue
                    else:
                        logger.error(f"函数 {func_name} 异常重试失败，已达到最大重试次数，错误: {str(e)}")
                        raise e
            
            raise RuntimeError(f"函数 {func_name} 重试逻辑异常")
        
        return wrapper
    return decorator