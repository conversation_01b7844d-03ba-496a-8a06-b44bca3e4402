import logging
import sys

# Configure root logger to prevent duplicate logs
root_logger = logging.getLogger()
if not root_logger.handlers:
    # Add a NullHandler to the root logger to prevent "No handlers could be found" warnings
    root_logger.addHandler(logging.NullHandler())

# Get the chat_bi_mysql logger
logger = logging.getLogger("chat_bi_mysql")

# Only configure the logger if it hasn't been configured yet
if not logger.handlers:
    logger.setLevel(logging.INFO)

    # Prevent propagation to the root logger to avoid duplicate logs
    logger.propagate = False

    # Create a StreamHandler (console handler) and set level to INFO
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)

    # Create a Formatter and add it to the handler
    formatter = logging.Formatter(
        "[%(asctime)s] - %(levelname)s - %(name)s - %(message)s", datefmt="%Y-%m-%d %H:%M:%S"
    )
    console_handler.setFormatter(formatter)

    # Add the handler to the logger
    logger.addHandler(console_handler)

# 立即升级logger以支持conversation_id
def _upgrade_logger_now():
    """立即升级logger以支持conversation_id"""
    try:
        from src.utils.enhanced_logger import upgrade_existing_logger
        global logger
        logger = upgrade_existing_logger(logger)
        return True
    except ImportError:
        return False  # 如果导入失败，保持原始logger

# 尝试立即升级logger
_upgrade_success = _upgrade_logger_now()

# 如果立即升级失败，在程序退出时再次尝试
if not _upgrade_success:
    import atexit
    atexit.register(_upgrade_logger_now)