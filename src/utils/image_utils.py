"""
图片处理工具模块
负责图片下载、转换和处理相关功能
"""

import base64
import requests
from typing import Optional
from src.utils.logger import logger


def download_image_to_base64(image_url: str, timeout: int = 10) -> Optional[str]:
    """
    下载CDN图片并转换为base64编码
    
    Args:
        image_url: 图片的CDN URL
        timeout: 请求超时时间（秒）
        
    Returns:
        str: base64编码的图片数据（包含data:image前缀），如果失败返回None
    """
    try:
        # 验证URL
        if not image_url or not isinstance(image_url, str):
            logger.warning(f"无效的图片URL: {image_url}")
            return None
            
        # 如果已经是base64编码，直接返回
        if image_url.startswith('data:image/'):
            logger.info("图片已经是base64编码格式，直接返回")
            return image_url
            
        # 下载图片
        logger.info(f"开始下载图片: {image_url[:100]}...")
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        response = requests.get(image_url, headers=headers, timeout=timeout)
        response.raise_for_status()
        
        # 获取内容类型
        content_type = response.headers.get('content-type', 'image/jpeg')
        if not content_type.startswith('image/'):
            logger.warning(f"响应内容类型不是图片: {content_type}")
            # 尝试从URL推断图片类型
            if image_url.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.webp')):
                if image_url.lower().endswith('.png'):
                    content_type = 'image/png'
                elif image_url.lower().endswith('.gif'):
                    content_type = 'image/gif'
                elif image_url.lower().endswith('.webp'):
                    content_type = 'image/webp'
                else:
                    content_type = 'image/jpeg'
            else:
                content_type = 'image/jpeg'  # 默认为jpeg
        
        # 转换为base64
        image_data = response.content
        base64_data = base64.b64encode(image_data).decode('utf-8')
        
        # 构建完整的data URL
        data_url = f"data:{content_type};base64,{base64_data}"
        
        logger.info(f"图片下载成功，大小: {len(image_data)} bytes, 类型: {content_type}")
        return data_url
        
    except requests.exceptions.Timeout:
        logger.exception(f"下载图片超时: {image_url}")
        return None
    except requests.exceptions.RequestException as e:
        logger.exception(f"下载图片失败: {image_url}, 错误: {e}")
        return None
    except Exception as e:
        logger.exception(f"处理图片时发生未知错误: {image_url}, 错误: {e}")
        return None


def is_valid_image_url(url: str) -> bool:
    """
    验证是否为有效的图片URL
    
    Args:
        url: 要验证的URL
        
    Returns:
        bool: 是否为有效的图片URL
    """
    if not url or not isinstance(url, str):
        return False
        
    # 如果已经是base64编码，认为是有效的
    if url.startswith('data:image/'):
        return True
        
    # 检查URL长度（简单验证）
    if len(url) < 10:
        return False
        
    # 检查是否包含常见的图片文件扩展名或CDN域名
    url_lower = url.lower()
    image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp']
    cdn_domains = ['summerfarm.net', 'azure.summerfarm.net', 'cdn.summerfarm.net']
    
    has_image_extension = any(ext in url_lower for ext in image_extensions)
    has_cdn_domain = any(domain in url_lower for domain in cdn_domains)
    
    return has_image_extension or has_cdn_domain


def process_images_for_ai(image_urls: list) -> list:
    """
    批量处理图片URL，下载并转换为base64编码
    
    Args:
        image_urls: 图片URL列表
        
    Returns:
        list: 处理后的base64编码图片列表（过滤掉失败的）
    """
    if not image_urls:
        return []
        
    processed_images = []
    
    for image_url in image_urls:
        if not is_valid_image_url(image_url):
            logger.warning(f"跳过无效的图片URL: {image_url}")
            continue
            
        base64_image = download_image_to_base64(image_url)
        if base64_image:
            processed_images.append(base64_image)
        else:
            logger.warning(f"图片处理失败，跳过: {image_url}")
            
    logger.info(f"成功处理 {len(processed_images)}/{len(image_urls)} 张图片")
    return processed_images
