import time
import functools
import threading
from typing import Callable, Any, Dict

# 全局缓存字典，用于存储不同函数的缓存
# 结构: { 'function_qualname': { 'cache_key': (timestamp, value) } }
_caches: Dict[str, Dict[Any, tuple[float, Any]]] = {}

# 读写锁，用于线程安全的缓存访问
class ReadWriteLock:
    """读写锁实现，允许多个读者同时访问，但写者独占访问"""

    def __init__(self):
        self._read_ready = threading.Condition(threading.RLock())
        self._readers = 0

    def acquire_read(self):
        """获取读锁"""
        self._read_ready.acquire()
        try:
            self._readers += 1
        finally:
            self._read_ready.release()

    def release_read(self):
        """释放读锁"""
        self._read_ready.acquire()
        try:
            self._readers -= 1
            if self._readers == 0:
                self._read_ready.notify_all()
        finally:
            self._read_ready.release()

    def acquire_write(self):
        """获取写锁"""
        self._read_ready.acquire()
        while self._readers > 0:
            self._read_ready.wait()

    def release_write(self):
        """释放写锁"""
        self._read_ready.release()

# 为每个函数创建读写锁
_locks: Dict[str, ReadWriteLock] = {}

# 正在加载的缓存键，用于防止重复加载
_loading_keys: Dict[str, set] = {}

def in_memory_cache(expire_seconds: int):
    """
    一个内存缓存装饰器，支持设置过期时间。
    使用读写锁机制来减少并发查询时的重复请求。

    Args:
        expire_seconds (int): 缓存的过期时间（秒）。
    """
    def decorator(func: Callable) -> Callable:
        func_qualname = func.__qualname__ # 使用函数的限定名作为缓存的顶级键
        if func_qualname not in _caches:
            _caches[func_qualname] = {}
            _locks[func_qualname] = ReadWriteLock() # 为每个函数创建一个读写锁
            _loading_keys[func_qualname] = set() # 为每个函数创建正在加载的键集合

        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            # 创建缓存键，基于函数参数
            # 注意：kwargs中的顺序可能影响键，因此我们对其进行排序
            # 同时，确保所有参数都是可哈希的
            try:
                key_parts = [args]
                if kwargs:
                    key_parts.append(tuple(sorted(kwargs.items())))
                cache_key = tuple(key_parts)
            except TypeError:
                # 如果参数不可哈希，则不使用缓存，直接调用函数
                # 或者可以记录一个警告
                print(f"警告: 函数 {func_qualname} 的参数不可哈希，无法使用缓存。")
                return func(*args, **kwargs)

            current_time = time.time()
            cache_for_func = _caches[func_qualname]
            lock_for_func = _locks[func_qualname]
            loading_keys_for_func = _loading_keys[func_qualname]

            # 首先使用读锁检查缓存
            lock_for_func.acquire_read()
            try:
                if cache_key in cache_for_func:
                    cached_time, cached_value = cache_for_func[cache_key]
                    if current_time - cached_time < expire_seconds:
                        # 缓存有效
                        print(f"命中缓存: {func_qualname} (key: {str(cache_key)[:100]}...)")
                        return cached_value
            finally:
                lock_for_func.release_read()

            # 缓存未命中或已过期，尝试获取写锁来更新缓存
            while True:
                lock_for_func.acquire_write()
                try:
                    # 双重检查：在获取写锁后再次检查缓存，防止其他线程已经更新了缓存
                    if cache_key in cache_for_func:
                        cached_time, cached_value = cache_for_func[cache_key]
                        if current_time - cached_time < expire_seconds:
                            # 缓存有效（其他线程已更新）
                            print(f"命中缓存（双重检查）: {func_qualname} (key: {str(cache_key)[:100]}...)")
                            return cached_value
                        else:
                            # 缓存过期，删除旧缓存
                            print(f"缓存过期: {func_qualname} (key: {str(cache_key)[:100]}...)")
                            del cache_for_func[cache_key]

                    # 检查是否有其他线程正在加载相同的键
                    if cache_key in loading_keys_for_func:
                        # 有其他线程正在加载，释放写锁并等待
                        pass
                    else:
                        # 没有其他线程在加载，标记正在加载并跳出循环
                        loading_keys_for_func.add(cache_key)
                        break
                finally:
                    lock_for_func.release_write()

                # 如果有其他线程在加载，等待一段时间后重试
                time.sleep(0.01)
                # 再次检查缓存是否已被其他线程更新
                lock_for_func.acquire_read()
                try:
                    if cache_key in cache_for_func:
                        cached_time, cached_value = cache_for_func[cache_key]
                        if current_time - cached_time < expire_seconds:
                            print(f"等待后命中缓存: {func_qualname} (key: {str(cache_key)[:100]}...)")
                            return cached_value
                finally:
                    lock_for_func.release_read()



            try:
                # 在锁外执行实际的函数调用，避免长时间占用锁
                print(f"缓存未命中，执行函数: {func_qualname} (key: {str(cache_key)[:100]}...)")
                result = func(*args, **kwargs)

                # 获取写锁来更新缓存
                lock_for_func.acquire_write()
                try:
                    cache_for_func[cache_key] = (time.time(), result)
                    loading_keys_for_func.discard(cache_key)  # 移除加载标记
                finally:
                    lock_for_func.release_write()

                return result
            except Exception as e:
                # 发生异常时也要移除加载标记
                lock_for_func.acquire_write()
                try:
                    loading_keys_for_func.discard(cache_key)
                finally:
                    lock_for_func.release_write()
                raise e
        return wrapper
    return decorator

def clear_cache(func_qualname: str = None, cache_key: Any = None) -> None:
    """
    清除指定函数或特定键的缓存。

    Args:
        func_qualname (str, optional): 要清除缓存的函数的限定名。
                                     如果为 None，则清除所有缓存。
        cache_key (Any, optional): 要清除的特定缓存键。
                                  仅当 func_qualname 被指定时有效。
                                  如果为 None，则清除该函数的所有缓存。
    """
    if func_qualname is None:
        # 清除所有缓存，需要获取所有函数的写锁
        for qualname in list(_locks.keys()):
            lock = _locks[qualname]
            lock.acquire_write()
            try:
                _caches[qualname].clear()
                _loading_keys[qualname].clear()
            finally:
                lock.release_write()
        print("所有内存缓存已清除。")
    elif func_qualname in _caches and func_qualname in _locks:
        lock = _locks[func_qualname]
        lock.acquire_write()
        try:
            if cache_key is None:
                _caches[func_qualname].clear()
                _loading_keys[func_qualname].clear()
                print(f"函数 {func_qualname} 的所有缓存已清除。")
            elif cache_key in _caches[func_qualname]:
                del _caches[func_qualname][cache_key]
                _loading_keys[func_qualname].discard(cache_key)  # 也移除加载标记
                print(f"函数 {func_qualname} 的键 {str(cache_key)[:100]}... 的缓存已清除。")
            else:
                print(f"函数 {func_qualname} 中未找到键 {str(cache_key)[:100]}... 的缓存。")
        finally:
            lock.release_write()
    else:
        print(f"未找到函数 {func_qualname} 的缓存。")