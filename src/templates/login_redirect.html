<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录重定向 - ChatBI</title>

    <!-- Tailwind CSS and DaisyUI via CDN (Latest version) -->
    <script src="{{ url_for('static', filename='js/lib/tailwindcss-3.4.16.js', t=cache_control_timestamp) }}"></script>
    <link href="https://cdnfe-azure.summerfarm.net/common/jstest/full.min.css" rel="stylesheet"
          type="text/css"/>

    <!-- Tailwind Config -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    animation: {
                        'fade': 'fade 0.5s ease-in-out',
                    },
                    keyframes: {
                        fade: {
                            '0%': {opacity: '0'},
                            '100%': {opacity: '1'},
                        },
                    },
                },
            },
            daisyui: {
                themes: ["light", "dark"],
            },
        }
    </script>
    <!-- ES Module Shims polyfill for Import Maps compatibility -->
    <script async src="https://cdnfe-azure.summerfarm.net/common/jstest/<EMAIL>"></script>
    
    <style>
        /* 简约卡片样式 */
        .simple-card {
            background-color: #ffffff;
            border: 1px solid rgba(0, 0, 0, 0.05);
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            transition: background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
        }

        /* 暗色模式下的卡片样式 */
        [data-theme="dark"] .simple-card {
            background-color: #232323;
            border: 1px solid rgba(255, 255, 255, 0.05);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        /* 加载动画 */
        .loading-dots {
            display: inline-flex;
            align-items: center;
        }

        .loading-dots span {
            width: 6px;
            height: 6px;
            margin: 0 2px;
            border-radius: 50%;
            background-color: currentColor;
            opacity: 0.6;
        }

        .loading-dots span:nth-child(1) {
            animation: dot-fade 1.2s infinite ease-in-out;
        }

        .loading-dots span:nth-child(2) {
            animation: dot-fade 1.2s infinite ease-in-out 0.2s;
        }

        .loading-dots span:nth-child(3) {
            animation: dot-fade 1.2s infinite ease-in-out 0.4s;
        }

        @keyframes dot-fade {
            0%, 100% {
                opacity: 0.3;
                transform: scale(0.8);
            }
            50% {
                opacity: 1;
                transform: scale(1);
            }
        }
    </style>

    <!-- 初始化主题 -->
    <script>
        // 检测系统主题
        function systemPrefersDark() {
            return window.matchMedia('(prefers-color-scheme: dark)').matches;
        }

        // 获取当前主题
        function getCurrentTheme() {
            return localStorage.getItem('theme') || 'system';
        }

        // 获取有效主题
        function getEffectiveTheme(theme) {
            if (theme === 'system') {
                return systemPrefersDark() ? 'dark' : 'light';
            }
            return theme;
        }

        // 应用主题
        function applyTheme() {
            const currentTheme = getCurrentTheme();
            const effectiveTheme = getEffectiveTheme(currentTheme);
            document.documentElement.setAttribute('data-theme', effectiveTheme);
        }

        // 页面加载时应用主题
        document.addEventListener('DOMContentLoaded', applyTheme);
        // 立即应用主题以避免闪烁
        applyTheme();
    </script>
</head>
<body class="min-h-screen flex justify-center items-center bg-base-100 p-4 animate-fade">
<div class="w-full max-w-md simple-card">
    <div class="p-6">
        {% if success %}
            <div class="text-center">
                <h2 class="text-2xl font-medium mb-4">登录成功</h2>
                <p class="text-base mb-1">欢迎回来，{{ username }}</p>
                <p class="text-sm opacity-70 flex items-center justify-center">
                    正在重定向
                    <span class="loading-dots ml-1">
                        <span></span>
                        <span></span>
                        <span></span>
                    </span>
                </p>
                <div class="flex justify-center my-4">
                    <span class="loading loading-spinner loading-sm text-primary"></span>
                </div>
            </div>
            <script>
                // 1.5秒后重定向到主页
                setTimeout(function () {
                    window.location.href = "{{ redirect_url }}";
                }, 1500);
            </script>
        {% else %}
            <div class="text-center">
                <h2 class="text-2xl font-medium text-error mb-4">登录失败</h2>
                <div class="alert alert-error mb-4 justify-center">
                    <span>{{ error_message }}</span>
                </div>
                <div class="mt-4">
                    <button class="btn btn-primary" onclick="window.location.href='/login'">
                        重新登录
                    </button>
                </div>
            </div>
        {% endif %}
    </div>
</div>
</body>
</html>
