"""
Database enum module.

This module defines the database types used in the application.
"""

from enum import Enum

class Database(Enum):
    """
    Enum representing the different databases used in the application.
    """
    CHATBI = "chatbi"
    BUSINESS = "business"
    LOGICAL_DW = "logical_dw"
    
    def __str__(self) -> str:
        """
        Return the string value of the enum.
        """
        return self.value
