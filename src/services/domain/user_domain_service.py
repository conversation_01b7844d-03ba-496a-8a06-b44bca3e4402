"""
用户领域服务

处理用户相关的业务逻辑，遵循DDD架构模式
"""

from typing import Optional, List, Dict, Any
from src.models.user_info_class import User
from src.repositories.chatbi.user import UserRepository
from src.utils.logger import logger


class UserDomainService:
    """用户领域服务"""
    
    def __init__(self, user_repository: UserRepository):
        """
        初始化用户领域服务
        
        Args:
            user_repository: 用户仓储接口实现
        """
        self.user_repository = user_repository
    
    def find_user_by_username(self, username: str) -> Optional[User]:
        """
        根据用户名查找用户
        
        Args:
            username: 用户名
            
        Returns:
            Optional[User]: 用户实体，未找到时返回None
        """
        if not username or not username.strip():
            logger.warning("用户名不能为空")
            return None
            
        return self.user_repository.find_by_username(username.strip())
    
    def find_user_by_email(self, email: str) -> Optional[User]:
        """
        根据邮箱查找用户
        
        Args:
            email: 邮箱地址
            
        Returns:
            Optional[User]: 用户实体，未找到时返回None
        """
        if not email or not email.strip():
            logger.warning("邮箱不能为空")
            return None
            
        email = email.strip().lower()  # 邮箱统一转小写处理
        
        # 基本邮箱格式验证
        if '@' not in email:
            logger.warning(f"邮箱格式无效: {email}")
            return None
            
        return self.user_repository.find_by_email(email)
    
    def find_user_by_open_id(self, open_id: str) -> Optional[User]:
        """
        根据open_id查找用户
        
        Args:
            open_id: 用户open_id
            
        Returns:
            Optional[User]: 用户实体，未找到时返回None
        """
        if not open_id or not open_id.strip():
            logger.warning("open_id不能为空")
            return None
            
        return self.user_repository.find_by_open_id(open_id.strip())
    
    def find_user_by_query(self, input_str: str) -> Optional[User]:
        """
        根据输入字符串智能查找用户（支持姓名或邮箱）
        
        Args:
            input_str: 可以是姓名或邮箱地址
            
        Returns:
            Optional[User]: 用户实体，未找到时返回None
        """
        if not input_str or not input_str.strip():
            logger.warning("查询字符串不能为空")
            return None
            
        input_str = input_str.strip()
        
        # 首先尝试邮箱格式匹配
        if '@' in input_str:
            user = self.find_user_by_email(input_str)
            if user:
                return user
        
        # 如果不是邮箱格式或邮箱查找失败，按姓名处理
        return self.find_user_by_username(input_str)
    
    def search_users(self, search_term: str, limit: int = 5) -> List[User]:
        """
        模糊搜索用户
        
        Args:
            search_term: 搜索关键词（姓名或邮箱）
            limit: 限制返回结果数量
            
        Returns:
            List[User]: 用户列表
        """
        if not search_term or not search_term.strip():
            logger.warning("搜索关键词不能为空")
            return []
            
        if limit <= 0:
            logger.warning("限制数量必须大于0")
            return []
            
        # 限制最大搜索结果数量，防止性能问题
        max_limit = 50
        if limit > max_limit:
            logger.warning(f"搜索结果限制过大，调整为最大值: {max_limit}")
            limit = max_limit
            
        return self.user_repository.search_users(search_term.strip(), limit)
    
    def list_users(self, limit: int = 10) -> List[User]:
        """
        获取用户列表（用于调试或管理功能）
        
        Args:
            limit: 限制返回数量
            
        Returns:
            List[User]: 用户列表
        """
        if limit <= 0:
            logger.warning("限制数量必须大于0")
            return []
            
        # 限制最大列表数量，防止性能问题
        max_limit = 100
        if limit > max_limit:
            logger.warning(f"列表限制过大，调整为最大值: {max_limit}")
            limit = max_limit
            
        return self.user_repository.list_users(limit)
    
    def validate_user_info(self, user: User) -> List[str]:
        """
        验证用户信息的完整性
        
        Args:
            user: 用户实体
            
        Returns:
            List[str]: 验证错误信息列表，空列表表示验证通过
        """
        errors = []
        
        if not user.name or not user.name.strip():
            errors.append("用户姓名不能为空")
            
        if not user.email or not user.email.strip():
            errors.append("用户邮箱不能为空")
        elif not user.is_email_valid():
            errors.append("用户邮箱格式无效")
            
        if not user.open_id or not user.open_id.strip():
            errors.append("用户open_id不能为空")
            
        return errors
    
    def get_user_summary(self, user: User) -> str:
        """
        获取用户信息摘要
        
        Args:
            user: 用户实体
            
        Returns:
            str: 用户信息摘要
        """
        return f"{user.get_display_name()} ({user.email}) - {user.get_department_display()}/{user.get_job_title_display()}"
