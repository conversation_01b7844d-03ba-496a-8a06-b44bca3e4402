"""
部门Top10常问清单导出服务

提供Excel导出功能，将分析结果导出为格式化的Excel文件
"""

from typing import Optional
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
from openpyxl.utils import get_column_letter

from src.models.department_top_questions import DepartmentTopQuestionsWeekly
from src.utils.logger import logger


class DepartmentTopQuestionsExportService:
    """部门Top10常问清单导出服务"""

    def __init__(self):
        """初始化导出服务"""
        pass

    def export_to_excel(self, record: DepartmentTopQuestionsWeekly) -> Workbook:
        """
        将部门Top10常问清单导出为Excel文件
        
        Args:
            record: 部门Top10问题记录
            
        Returns:
            Workbook: Excel工作簿对象
        """
        try:
            # 创建工作簿
            wb = Workbook()
            
            # 创建概览工作表
            self._create_overview_sheet(wb, record)
            
            # 创建详情工作表
            self._create_details_sheet(wb, record)
            
            # 删除默认工作表
            if 'Sheet' in wb.sheetnames:
                wb.remove(wb['Sheet'])
                
            logger.info(f"成功生成Excel文件: {record.department_name} {record.get_week_display()}")
            return wb
            
        except Exception as e:
            logger.error(f"生成Excel文件时发生错误: {e}", exc_info=True)
            raise

    def _create_overview_sheet(self, wb: Workbook, record: DepartmentTopQuestionsWeekly):
        """创建概览工作表"""
        ws = wb.active
        ws.title = "概览"
        
        # 设置标题样式
        title_font = Font(name='微软雅黑', size=16, bold=True)
        header_font = Font(name='微软雅黑', size=12, bold=True)
        content_font = Font(name='微软雅黑', size=11)
        
        # 标题
        ws['A1'] = f"{record.department_name} - Top10常问清单"
        ws['A1'].font = title_font
        ws.merge_cells('A1:D1')
        
        # 基本信息
        row = 3
        ws[f'A{row}'] = "分析周期:"
        ws[f'A{row}'].font = header_font
        ws[f'B{row}'] = record.get_week_display()
        ws[f'B{row}'].font = content_font
        
        row += 1
        ws[f'A{row}'] = "部门名称:"
        ws[f'A{row}'].font = header_font
        ws[f'B{row}'] = record.department_name
        ws[f'B{row}'].font = content_font
        
        row += 1
        ws[f'A{row}'] = "总对话数:"
        ws[f'A{row}'].font = header_font
        ws[f'B{row}'] = record.total_conversations
        ws[f'B{row}'].font = content_font
        
        row += 1
        ws[f'A{row}'] = "总查询数:"
        ws[f'A{row}'].font = header_font
        ws[f'B{row}'] = record.total_user_queries
        ws[f'B{row}'].font = content_font
        
        row += 1
        ws[f'A{row}'] = "分析状态:"
        ws[f'A{row}'].font = header_font
        ws[f'B{row}'] = record.get_analysis_status_display()
        ws[f'B{row}'].font = content_font
        
        # 设置列宽
        ws.column_dimensions['A'].width = 15
        ws.column_dimensions['B'].width = 30
        ws.column_dimensions['C'].width = 15
        ws.column_dimensions['D'].width = 30

    def _create_details_sheet(self, wb: Workbook, record: DepartmentTopQuestionsWeekly):
        """创建详情工作表"""
        ws = wb.create_sheet("Top10问题详情")
        
        # 设置样式
        header_font = Font(name='微软雅黑', size=12, bold=True, color='FFFFFF')
        content_font = Font(name='微软雅黑', size=10)
        header_fill = PatternFill(start_color='4472C4', end_color='4472C4', fill_type='solid')
        border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        # 表头
        headers = ['排名', '问题类别', '代表性问题', '出现频次', '占比(%)', '相似问题']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = header_font
            cell.fill = header_fill
            cell.border = border
            cell.alignment = Alignment(horizontal='center', vertical='center')
        
        # 数据行
        if record.top_questions:
            for row, question in enumerate(record.top_questions, 2):
                # 排名
                cell = ws.cell(row=row, column=1, value=question.rank)
                cell.font = content_font
                cell.border = border
                cell.alignment = Alignment(horizontal='center', vertical='center')
                
                # 问题类别
                cell = ws.cell(row=row, column=2, value=question.question_category)
                cell.font = content_font
                cell.border = border
                cell.alignment = Alignment(horizontal='left', vertical='center')
                
                # 代表性问题
                cell = ws.cell(row=row, column=3, value=question.representative_question)
                cell.font = content_font
                cell.border = border
                cell.alignment = Alignment(horizontal='left', vertical='center', wrap_text=True)
                
                # 出现频次
                cell = ws.cell(row=row, column=4, value=question.frequency)
                cell.font = content_font
                cell.border = border
                cell.alignment = Alignment(horizontal='center', vertical='center')
                
                # 占比
                cell = ws.cell(row=row, column=5, value=f"{question.percentage:.1f}%")
                cell.font = content_font
                cell.border = border
                cell.alignment = Alignment(horizontal='center', vertical='center')
                
                # 相似问题
                similar_questions_text = '; '.join(question.similar_questions) if question.similar_questions else ''
                cell = ws.cell(row=row, column=6, value=similar_questions_text)
                cell.font = content_font
                cell.border = border
                cell.alignment = Alignment(horizontal='left', vertical='center', wrap_text=True)
        
        # 设置列宽
        ws.column_dimensions['A'].width = 8   # 排名
        ws.column_dimensions['B'].width = 20  # 问题类别
        ws.column_dimensions['C'].width = 40  # 代表性问题
        ws.column_dimensions['D'].width = 12  # 出现频次
        ws.column_dimensions['E'].width = 12  # 占比
        ws.column_dimensions['F'].width = 50  # 相似问题
        
        # 设置行高
        for row in range(2, len(record.top_questions) + 2 if record.top_questions else 2):
            ws.row_dimensions[row].height = 60  # 增加行高以适应换行文本
