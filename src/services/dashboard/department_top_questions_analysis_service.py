"""
部门Top10常问清单分析服务

使用LLM分析部门聊天记录，生成Top10常问问题清单
"""

from typing import List, Dict, Any, Optional
from datetime import datetime, date, timedelta
import json
import re

from src.models.department_top_questions import (
    DepartmentTopQuestionsWeekly, 
    TopQuestion, 
    AnalysisStatus
)
from src.repositories.chatbi.department_top_questions import DepartmentTopQuestionsRepository
from src.repositories.chatbi.history import (
    get_department_chat_history_for_week,
    get_department_conversation_stats_for_week
)
from src.repositories.chatbi.dashboard.users import get_departments
from src.services.agent.utils.model_provider import get_provider_config
from src.utils.logger import logger
from src.utils.resource_manager import load_resource


class DepartmentTopQuestionsAnalysisService:
    """部门Top10常问清单分析服务"""

    def __init__(self):
        """初始化分析服务"""
        self.repository = DepartmentTopQuestionsRepository()
        # 获取OpenRouter配置用于OpenAI API调用
        self.openrouter_config = get_provider_config("openrouter")

    def analyze_department_week(self, department_name: str, week_start_date: date) -> bool:
        """
        分析指定部门的指定周数据
        
        Args:
            department_name: 部门名称
            week_start_date: 周开始日期（周一）
            
        Returns:
            bool: 分析是否成功
        """
        try:
            # 计算周结束日期
            week_end_date = week_start_date + timedelta(days=6)
            
            logger.info(f"开始分析部门 {department_name} 的周数据: {week_start_date} 至 {week_end_date}")
            
            # 检查是否已存在分析记录
            existing_record = self.repository.find_by_department_and_week(
                department_name, week_start_date
            )
            
            if existing_record:
                if existing_record.is_analysis_completed():
                    logger.info(f"部门 {department_name} 的周数据已分析完成，跳过")
                    return True
                else:
                    # 重新开始分析（包括PROCESSING和FAILED状态）
                    logger.info(f"部门 {department_name} 存在未完成的分析记录，重新开始分析")
                    existing_record.mark_analysis_started()
                    self.repository.update(existing_record)
                    record = existing_record
            else:
                # 创建新的分析记录
                record = DepartmentTopQuestionsWeekly(
                    department_name=department_name,
                    week_start_date=week_start_date,
                    week_end_date=week_end_date,
                    top_questions=[],
                    total_conversations=0,
                    total_user_queries=0,
                    analysis_status=AnalysisStatus.PROCESSING
                )
                record_id = self.repository.create(record)
                if not record_id:
                    logger.exception(f"创建分析记录失败: {department_name}")
                    return False
                record.id = record_id

            # 获取聊天记录和统计数据
            week_start_str = week_start_date.strftime('%Y-%m-%d')
            week_end_str = week_end_date.strftime('%Y-%m-%d')
            
            chat_history = get_department_chat_history_for_week(
                department_name, week_start_str, week_end_str
            )
            
            stats = get_department_conversation_stats_for_week(
                department_name, week_start_str, week_end_str
            )
            
            # 更新统计数据
            record.total_conversations = stats.get('total_conversations', 0)
            record.total_user_queries = stats.get('total_user_queries', 0)
            
            if not chat_history or record.total_user_queries == 0:
                logger.info(f"部门 {department_name} 在 {week_start_str} 至 {week_end_str} 没有用户查询，跳过分析")
                record.mark_analysis_completed([])
                self.repository.update(record)
                return True
            
            # 使用LLM分析生成Top10问题
            top_questions = self._analyze_with_llm(chat_history, department_name)
            
            if top_questions:
                record.mark_analysis_completed(top_questions)
                logger.info(f"部门 {department_name} 分析完成，生成 {len(top_questions)} 个Top问题")
            else:
                record.mark_analysis_failed("LLM分析未返回有效结果")
                logger.exception(f"部门 {department_name} 分析失败：LLM未返回有效结果")
            
            # 更新记录
            self.repository.update(record)
            return top_questions is not None
            
        except Exception as e:
            logger.exception(f"分析部门 {department_name} 周数据时发生错误: {e}", exc_info=True)
            # 标记分析失败
            if 'record' in locals() and record:
                record.mark_analysis_failed(str(e))
                self.repository.update(record)
            return False

    def _analyze_with_llm(self, chat_history: List[Dict[str, Any]], department_name: str) -> Optional[List[TopQuestion]]:
        """
        使用LLM分析聊天记录生成Top10问题
        
        Args:
            chat_history: 聊天记录列表
            department_name: 部门名称
            
        Returns:
            Optional[List[TopQuestion]]: 分析结果，失败返回None
        """
        try:
            # 提取用户问题
            user_questions = []
            for record in chat_history:
                if record.get('role') == 'user' and record.get('content'):
                    content = record['content'].strip()
                    if content and len(content) > 5:  # 过滤太短的内容
                        user_questions.append(content)
            
            if len(user_questions) < 3:
                logger.info(f"部门 {department_name} 的有效用户问题太少（{len(user_questions)}个），跳过分析")
                return []
            
            logger.info(f"部门 {department_name} 共有 {len(user_questions)} 个用户问题待分析")
            
            # 构建LLM分析提示
            prompt = self._build_analysis_prompt(user_questions, department_name)
            
            # 调用LLM进行分析
            response = self._call_llm(prompt)
            
            if not response:
                logger.exception("LLM调用失败，未返回响应")
                return None
            
            # 解析LLM响应
            top_questions = self._parse_llm_response(response)
            
            return top_questions
            
        except Exception as e:
            logger.exception(f"LLM分析过程中发生错误: {e}", exc_info=True)
            return None

    def _build_analysis_prompt(self, user_questions: List[str], department_name: str) -> str:
        """
        构建LLM分析提示

        Args:
            user_questions: 用户问题列表
            department_name: 部门名称

        Returns:
            str: 分析提示文本
        """
        # 加载基础提示模板
        base_prompt = load_resource("prompt", "department_top_questions_analysis.md")
        if not base_prompt:
            logger.warning("未找到分析提示模板，使用默认提示")
            base_prompt = "请分析以下用户问题，生成Top10常问清单。"

        # 构建问题列表
        questions_text = "\n".join([f"{i+1}. {q}" for i, q in enumerate(user_questions)])

        # 组合完整提示
        prompt = f"""
{base_prompt}

## 当前分析任务

**部门**: {department_name}
**问题总数**: {len(user_questions)}

**用户问题列表**:
{questions_text}

请基于以上问题进行分析，严格按照要求的JSON格式返回Top10常问清单。
"""

        return prompt

    def _call_llm(self, prompt: str) -> Optional[str]:
        """
        调用LLM进行分析

        Args:
            prompt: 分析提示

        Returns:
            Optional[str]: LLM响应，失败返回None
        """
        try:
            import openai

            # 创建OpenAI客户端（通过OpenRouter）
            client = openai.OpenAI(
                api_key=self.openrouter_config["api_key"],
                base_url=self.openrouter_config["api_base"]
            )

            # 构建消息
            messages = [
                {
                    "role": "system",
                    "content": "你是一个专业的数据分析师，擅长分析聊天记录并提取常见问题。请严格按照要求的JSON格式返回结果。"
                },
                {
                    "role": "user",
                    "content": prompt
                }
            ]

            # 调用OpenAI API
            response = client.chat.completions.create(
                model="anthropic/claude-sonnet-4",
                messages=messages,
                temperature=0.1,
                max_tokens=4000,
                timeout=60
            )

            if response and response.choices:
                content = response.choices[0].message.content
                logger.info(f"LLM分析完成，响应长度: {len(content) if content else 0}")
                return content
            else:
                logger.exception("LLM响应格式异常")
                return None

        except Exception as e:
            logger.exception(f"调用LLM时发生错误: {e}", exc_info=True)
            return None

    def _parse_llm_response(self, response: str) -> Optional[List[TopQuestion]]:
        """
        解析LLM响应，提取Top10问题
        
        Args:
            response: LLM响应文本
            
        Returns:
            Optional[List[TopQuestion]]: 解析结果，失败返回None
        """
        try:
            # 提取JSON部分
            json_match = re.search(r'```json\s*(.*?)\s*```', response, re.DOTALL)
            if not json_match:
                # 尝试直接解析整个响应
                json_text = response.strip()
            else:
                json_text = json_match.group(1).strip()
            
            # 解析JSON
            data = json.loads(json_text)
            
            if not isinstance(data, list):
                logger.exception("LLM响应不是列表格式")
                return None
            
            top_questions = []
            for item in data:
                try:
                    question = TopQuestion(
                        rank=item['rank'],
                        question_category=item['question_category'],
                        representative_question=item['representative_question'],
                        frequency=item['frequency'],
                        percentage=float(item['percentage']),
                        similar_questions=item.get('similar_questions', [])
                    )
                    top_questions.append(question)
                except (KeyError, ValueError, TypeError) as e:
                    logger.warning(f"解析Top问题项时出错: {e}, 项目: {item}")
                    continue
            
            # 验证排名的连续性和唯一性
            ranks = [q.rank for q in top_questions]
            if len(set(ranks)) != len(ranks):
                logger.warning("LLM返回的排名不唯一，进行修正")
                for i, question in enumerate(top_questions):
                    question.rank = i + 1
            
            logger.info(f"成功解析 {len(top_questions)} 个Top问题")
            return top_questions[:10]  # 确保最多10个
            
        except json.JSONDecodeError as e:
            logger.exception(f"解析LLM响应JSON时出错: {e}")
            logger.debug(f"原始响应: {response}")
            return None
        except Exception as e:
            logger.exception(f"解析LLM响应时发生未知错误: {e}", exc_info=True)
            return None

    def analyze_all_departments_for_week(self, week_start_date: date) -> Dict[str, bool]:
        """
        分析所有部门的指定周数据
        
        Args:
            week_start_date: 周开始日期（周一）
            
        Returns:
            Dict[str, bool]: 各部门分析结果
        """
        try:
            # 获取所有部门
            departments = get_departments()
            
            if not departments:
                logger.warning("未找到任何部门数据")
                return {}
            
            results = {}
            
            for dept_name in departments:
                if not dept_name:
                    continue
                
                try:
                    success = self.analyze_department_week(dept_name, week_start_date)
                    results[dept_name] = success
                    logger.info(f"部门 {dept_name} 分析结果: {'成功' if success else '失败'}")
                except Exception as e:
                    logger.exception(f"分析部门 {dept_name} 时发生错误: {e}")
                    results[dept_name] = False
            
            success_count = sum(1 for success in results.values() if success)
            logger.info(f"周度分析完成: 总计 {len(results)} 个部门，成功 {success_count} 个")
            
            return results
            
        except Exception as e:
            logger.exception(f"批量分析部门周数据时发生错误: {e}", exc_info=True)
            return {}

    def get_last_monday(self, reference_date: Optional[date] = None) -> date:
        """
        获取上周一的日期
        
        Args:
            reference_date: 参考日期，默认为今天
            
        Returns:
            date: 上周一的日期
        """
        if reference_date is None:
            reference_date = date.today()
        
        # 计算本周一
        days_since_monday = reference_date.weekday()
        this_monday = reference_date - timedelta(days=days_since_monday)
        
        # 上周一
        last_monday = this_monday - timedelta(days=7)
        
        return last_monday
