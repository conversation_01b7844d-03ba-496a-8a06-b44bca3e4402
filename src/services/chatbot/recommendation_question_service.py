"""
推荐问题选择服务模块
处理用户对推荐问题的选择，并自动启动新的查询流程
"""

import asyncio
import threading
from src.services.feishu.query_processor import FeishuQueryProcessor
from src.utils.logger import logger


def handle_recommendation_question_choice(conversation_id: str, user_name: str, choice: str,
                                        chat_history_id: int = None, root_id: str = None,
                                        open_message_id: str = None, chat_id: str = None, **kwargs) -> bool:
    """处理用户的推荐问题选择并启动新查询

    Args:
        conversation_id: 对话ID（为了兼容性保留）
        user_name: 用户名
        choice: 用户选择的推荐问题
        chat_history_id: 聊天历史ID（为了兼容性保留）
        root_id: 根消息ID（为了兼容性保留）
        open_message_id: 飞书原始消息ID（必需）
        chat_id: 聊天ID（实际使用的对话ID）
        **kwargs: 其他参数

    Returns:
        bool: 处理是否成功
    """
    try:
        # 使用chat_id作为实际的对话ID
        actual_chat_id = chat_id or conversation_id
        logger.info(f"用户 {user_name} 选择了推荐问题: {choice}，chat_id: {actual_chat_id}")
        logger.info(f"附加信息 - open_message_id: {open_message_id}")

        # 检查必需参数
        if not open_message_id:
            logger.exception("缺少飞书原始消息ID，无法处理推荐问题选择")
            return False

        # 直接使用用户选择的问题作为查询内容
        user_query = choice.strip()

        # 通过用户名查找数据库中的用户信息
        from src.services.user_query_service import UserQueryService

        # 首先尝试通过用户名查找
        user_dict = UserQueryService.get_user_info_by_username(username=user_name)

        if not user_dict:
            logger.error(f"未找到用户名为 {user_name} 的用户")
            return False

        # 在新线程中处理查询，避免阻塞当前线程
        def run_async_query():
            try:
                # 创建新的事件循环
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

                # 运行异步查询处理
                loop.run_until_complete(_process_recommendation_question_query(
                    open_message_id=open_message_id,
                    user_query=user_query,
                    user_info_dict=user_dict,
                    root_id=open_message_id,  # 使用open_message_id作为root_id
                    conversation_id=actual_chat_id
                ))

                loop.close()
            except Exception as e:
                logger.exception(f"异步处理推荐问题查询时出错: {e}", exc_info=True)

        # 启动后台线程处理查询
        thread = threading.Thread(target=run_async_query, daemon=True)
        thread.start()

        return True

    except Exception as e:
        logger.exception(f"处理推荐问题选择时出错: {e}", exc_info=True)
        return False


async def _process_recommendation_question_query(open_message_id: str, user_query: str, user_info_dict: dict, root_id: str, conversation_id: str = None):
    """处理推荐问题查询

    Args:
        open_message_id: 飞书原始消息ID
        user_query: 用户查询（选择的推荐问题）
        user_info_dict: 用户信息字典
        root_id: 根消息ID
        conversation_id: 对话ID
    """
    try:
        logger.info(f"开始处理推荐问题查询 - open_message_id: {open_message_id}, query: {user_query}")
        
        # 直接调用query_processor处理查询
        await FeishuQueryProcessor.handle_agent_query(
            message_id=open_message_id,  # 使用飞书的原始消息ID
            user_query=user_query,
            user_info_dict=user_info_dict,
            root_id=root_id,
            parent_id=None,
            image_url=None,
            conversation_id=conversation_id
        )

        logger.info(f"推荐问题查询处理完成 - open_message_id: {open_message_id}")

    except Exception as e:
        logger.exception(f"处理推荐问题查询时出错: {e}", exc_info=True)