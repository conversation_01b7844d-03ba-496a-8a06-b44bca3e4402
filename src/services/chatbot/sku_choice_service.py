"""
SKU选择服务模块
处理用户对SKU选择的反馈，并自动启动新的查询流程
"""

import asyncio
import threading
from src.services.feishu.query_processor import FeishuQueryProcessor
from src.utils.logger import logger


def handle_sku_choice(conversation_id: str, user_name: str, choice: str,
                     chat_history_id: int = None, root_id: str = None,
                     open_message_id: str = None, **kwargs) -> bool:
    """处理用户的SKU选择并启动新查询

    Args:
        conversation_id: 对话ID（为了兼容性保留，实际不使用）
        user_name: 用户名
        choice: 用户选择的SKU
        chat_history_id: 聊天历史ID（为了兼容性保留，实际不使用）
        root_id: 根消息ID（为了兼容性保留，实际不使用）
        open_message_id: 飞书原始消息ID（必需）
        **kwargs: 其他参数（为了兼容性保留）

    Returns:
        bool: 处理是否成功
    """
    try:
        logger.info(f"用户 {user_name} 选择了SKU: {choice}，conversation_id: {conversation_id}")
        logger.info(f"附加信息 - open_message_id: {open_message_id}")

        # 直接使用query_processor处理SKU选择
        if not open_message_id:
            logger.exception("缺少飞书原始消息ID，无法处理SKU选择")
            return False

        # 构建用户查询
        user_query = f"我选择：{choice}"

        # 构建用户信息 - 需要从真实的用户信息中获取open_id
        # 从chat_history_id同步获取真实的用户信息
        real_user_info = _get_real_user_info_from_chat_history_sync(chat_history_id, user_name)

        user_info_dict = {
            'name': real_user_info.get('name', user_name),
            'email': real_user_info.get('email', f"{user_name}@unknown.com"),
            'open_id': real_user_info.get('open_id', 'unknown_user'),  # 使用真实的open_id
        }

        # 在有运行中的事件循环时直接调度任务；否则在后台线程中使用asyncio.run执行
        try:
            loop = asyncio.get_running_loop()
            loop.create_task(_process_sku_choice_query(
                open_message_id, user_query, user_info_dict, root_id, conversation_id
            ))
        except RuntimeError:
            # 没有运行中的事件循环（典型于同步上下文，如WSGI/回调线程），改为后台线程运行
            # 这样做的原因：避免阻塞当前线程，同时不依赖全局事件循环的存在
            threading.Thread(
                target=lambda: asyncio.run(
                    _process_sku_choice_query(open_message_id, user_query, user_info_dict, root_id, conversation_id)
                ),
                daemon=True
            ).start()

        logger.info(f"SKU选择查询已启动 - choice: {choice}, open_message_id: {open_message_id}")
        return True

    except Exception as e:
        logger.exception(f"处理SKU选择时出错: {e}", exc_info=True)
        return False


async def _process_sku_choice_query(open_message_id: str, user_query: str, user_info_dict: dict, root_id: str, conversation_id: str = None):
    """简化的SKU选择查询处理

    Args:
        open_message_id: 飞书原始消息ID
        user_query: 用户查询（选择的SKU）
        user_info_dict: 用户信息字典
        root_id: 根消息ID
    """
    try:
        logger.info(f"开始处理SKU选择查询 - open_message_id: {open_message_id}, query: {user_query}")
        # 直接调用query_processor处理查询
        await FeishuQueryProcessor.handle_agent_query(
            message_id=open_message_id,  # 使用飞书的原始消息ID
            user_query=user_query,
            user_info_dict=user_info_dict,
            root_id=root_id,
            parent_id=None,
            image_url=None,
            conversation_id=conversation_id
        )

        logger.info(f"SKU选择查询处理完成 - open_message_id: {open_message_id}")

    except Exception as e:
        logger.exception(f"处理SKU选择查询时出错: {e}", exc_info=True)


def _get_real_user_info_from_chat_history_sync(chat_history_id: int, user_name: str) -> dict:
    """同步获取真实的用户信息

    Args:
        chat_history_id: 聊天历史ID
        user_name: 用户名（作为fallback）

    Returns:
        dict: 包含用户信息的字典
    """
    try:
        if not chat_history_id:
            logger.warning("chat_history_id为空，使用默认用户信息")
            return {'name': user_name, 'email': f"{user_name}@unknown.com", 'open_id': 'unknown_user'}

        from src.repositories.chatbi.history import get_chat_history_by_id

        # 根据chat_history_id获取聊天记录
        chat_record = get_chat_history_by_id(chat_history_id)

        if not chat_record:
            logger.exception(f"未找到chat_history_id: {chat_history_id} 对应的记录")
            return {'name': user_name, 'email': f"{user_name}@unknown.com", 'open_id': 'unknown_user'}

        logger.info(f"从chat_history获取到记录: {chat_record}")

        # 提取用户信息
        username = chat_record.get('username', user_name)
        email = chat_record.get('email', f"{user_name}@unknown.com")

        # 尝试从用户服务获取open_id
        try:
            from src.services.user_query_service import UserQueryService
            # 使用静态方法获取用户信息
            user_dict = UserQueryService.get_user_info_by_email(email)

            if user_dict and user_dict.get('open_id'):
                open_id = user_dict.get('open_id')
                logger.info(f"成功获取用户open_id: {open_id}")
            else:
                logger.warning(f"用户 {email} 没有有效的open_id，使用默认值")
                open_id = 'unknown_user'
        except Exception as e:
            logger.warning(f"获取用户open_id时出错: {e}，使用默认值")
            open_id = 'unknown_user'

        return {
            'name': username,
            'email': email,
            'open_id': open_id
        }

    except Exception as e:
        logger.exception(f"同步获取用户信息时出错: {e}", exc_info=True)
        return {'name': user_name, 'email': f"{user_name}@unknown.com", 'open_id': 'unknown_user'}


