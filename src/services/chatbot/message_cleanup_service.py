"""
消息清理服务

处理各种edge cases，包括：
- 超时消息检测和清理
- 异常状态修复
- 资源清理
"""

import time
from datetime import datetime, timedelta
from typing import List, Dict, Optional
from src.utils.logger import logger
from src.repositories.chatbi.history import execute_db_query
from src.repositories.chatbi.history import update_streaming_assistant_message

class MessageCleanupService:
    """消息清理服务"""
    
    def __init__(self):
        self.max_processing_time = 30 * 60  # 30分钟超时
        self.cleanup_batch_size = 100
        
    def detect_stale_messages(self) -> List[Dict]:
        """
        检测超时的流式消息
        
        Returns:
            List[Dict]: 超时消息列表
        """
        try:
            # 计算超时时间点
            timeout_timestamp = datetime.now() - timedelta(seconds=self.max_processing_time)
            
            sql = """
                SELECT id, conversation_id, username, email, content, 
                       created_at, updated_at, agent, 
                       TIMESTAMPDIFF(SECOND, updated_at, NOW()) as seconds_since_update
                FROM chat_history 
                WHERE is_in_process = 1 
                AND role = 'assistant'
                AND updated_at < %s
                ORDER BY updated_at ASC
                LIMIT %s
            """
            
            stale_messages = execute_db_query(
                sql, 
                (timeout_timestamp, self.cleanup_batch_size), 
                fetch='all'
            )
            
            if stale_messages:
                logger.warning(f"发现 {len(stale_messages)} 个超时的流式消息")
                for msg in stale_messages:
                    logger.warning(
                        f"超时消息: ID={msg['id']}, "
                        f"用户={msg['username']}, "
                        f"更新时间={msg['updated_at']}, "
                        f"超时={msg['seconds_since_update']}秒"
                    )
            
            return stale_messages or []
            
        except Exception as e:
            logger.exception(f"检测超时消息失败: {e}", exc_info=True)
            return []
    
    def cleanup_stale_message(self, message_id: int, reason: str = "系统超时") -> bool:
        """
        清理单个超时消息
        
        Args:
            message_id (int): 消息ID
            reason (str): 清理原因
            
        Returns:
            bool: 清理是否成功
        """
        try:
            # 获取当前消息内容
            sql = "SELECT content FROM chat_history WHERE id = %s"
            result = execute_db_query(sql, (message_id,), fetch='one')
            
            if not result:
                logger.warning(f"消息 {message_id} 不存在，跳过清理")
                return False
            
            current_content = result.get('content', '')
            
            # 添加错误提示到内容末尾
            error_message = f"\n\n⚠️ **系统提示**: 此消息因{reason}而中断，内容可能不完整。"
            final_content = current_content + error_message
            
            # 更新消息状态为已完成，并添加错误日志
            success = update_streaming_assistant_message(
                message_id=message_id,
                content=final_content,
                logs=f"消息因{reason}被系统自动清理",
                is_completed=True
            )
            
            if success:
                logger.info(f"成功清理超时消息: ID={message_id}, 原因={reason}")
                return True
            else:
                logger.exception(f"清理超时消息失败: ID={message_id}")
                return False
                
        except Exception as e:
            logger.exception(f"清理消息 {message_id} 时发生错误: {e}", exc_info=True)
            return False
    
    def cleanup_all_stale_messages(self) -> Dict[str, int]:
        """
        清理所有超时消息
        
        Returns:
            Dict[str, int]: 清理统计信息
        """
        stats = {
            'detected': 0,
            'cleaned': 0,
            'failed': 0
        }
        
        try:
            stale_messages = self.detect_stale_messages()
            stats['detected'] = len(stale_messages)
            
            for message in stale_messages:
                message_id = message['id']
                seconds_timeout = message['seconds_since_update']
                
                success = self.cleanup_stale_message(
                    message_id, 
                    f"超时{seconds_timeout}秒"
                )
                
                if success:
                    stats['cleaned'] += 1
                else:
                    stats['failed'] += 1
            
            if stats['detected'] > 0:
                logger.info(
                    f"消息清理完成: 发现{stats['detected']}个, "
                    f"成功清理{stats['cleaned']}个, "
                    f"失败{stats['failed']}个"
                )
            
            return stats
            
        except Exception as e:
            logger.exception(f"批量清理超时消息失败: {e}", exc_info=True)
            return stats
    
    def get_processing_messages_stats(self) -> Dict[str, any]:
        """
        获取正在处理中的消息统计信息
        
        Returns:
            Dict[str, any]: 统计信息
        """
        try:
            sql = """
                SELECT 
                    COUNT(*) as total_processing,
                    COUNT(CASE WHEN TIMESTAMPDIFF(MINUTE, updated_at, NOW()) > 5 THEN 1 END) as over_5min,
                    COUNT(CASE WHEN TIMESTAMPDIFF(MINUTE, updated_at, NOW()) > 10 THEN 1 END) as over_10min,
                    COUNT(CASE WHEN TIMESTAMPDIFF(MINUTE, updated_at, NOW()) > 30 THEN 1 END) as over_30min,
                    MIN(updated_at) as oldest_update,
                    MAX(updated_at) as newest_update
                FROM chat_history 
                WHERE is_in_process = 1 AND role = 'assistant'
            """
            
            result = execute_db_query(sql, (), fetch='one')
            
            if result:
                stats = {
                    'total_processing': result['total_processing'],
                    'over_5min': result['over_5min'],
                    'over_10min': result['over_10min'],
                    'over_30min': result['over_30min'],
                    'oldest_update': result['oldest_update'],
                    'newest_update': result['newest_update']
                }
                
                logger.debug(f"处理中消息统计: {stats}")
                return stats
            
            return {}
            
        except Exception as e:
            logger.exception(f"获取处理中消息统计失败: {e}", exc_info=True)
            return {}
    
    def force_cleanup_message(self, message_id: int, admin_reason: str = "管理员强制清理") -> bool:
        """
        管理员强制清理指定消息
        
        Args:
            message_id (int): 消息ID
            admin_reason (str): 管理员清理原因
            
        Returns:
            bool: 清理是否成功
        """
        logger.warning(f"管理员强制清理消息: ID={message_id}, 原因={admin_reason}")
        return self.cleanup_stale_message(message_id, admin_reason)
    
    def health_check(self) -> Dict[str, any]:
        """
        系统健康检查
        
        Returns:
            Dict[str, any]: 健康状态信息
        """
        try:
            stats = self.get_processing_messages_stats()
            stale_count = len(self.detect_stale_messages())
            
            health_status = {
                'timestamp': datetime.now().isoformat(),
                'processing_messages': stats.get('total_processing', 0),
                'stale_messages': stale_count,
                'over_5min': stats.get('over_5min', 0),
                'over_10min': stats.get('over_10min', 0),
                'over_30min': stats.get('over_30min', 0),
                'status': 'healthy' if stale_count == 0 else 'warning' if stale_count < 10 else 'critical'
            }
            
            return health_status
            
        except Exception as e:
            logger.exception(f"健康检查失败: {e}", exc_info=True)
            return {
                'timestamp': datetime.now().isoformat(),
                'status': 'error',
                'error': str(e)
            }

# 全局实例
message_cleanup_service = MessageCleanupService()

def run_cleanup_task():
    """运行清理任务（可以被定时任务调用）"""
    logger.info("开始执行消息清理任务")
    stats = message_cleanup_service.cleanup_all_stale_messages()
    logger.info(f"消息清理任务完成: {stats}")
    return stats

def get_system_health():
    """获取系统健康状态"""
    return message_cleanup_service.health_check()
