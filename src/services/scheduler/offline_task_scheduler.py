"""
异步定时任务调度器。
实现类似refresh token刷新机制的离线推荐任务调度，不依赖于系统的cron。
"""

from datetime import datetime, timedelta
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.interval import IntervalTrigger
from typing import Optional, Dict

from src.services.recommendation.offline_recommendation_service import OfflineRecommendationService
from src.utils.logger import logger


class OfflineTaskScheduler:
    """
    离线任务调度器
    基于APScheduler实现的异步定时任务管理，类似refresh token的定期刷新机制
    """
    
    def __init__(self, recommendation_service: OfflineRecommendationService = None):
        """
        初始化任务调度器
        
        Args:
            recommendation_service: 离线推荐服务实例
        """
        self.scheduler = BackgroundScheduler()
        self.recommendation_service = recommendation_service or OfflineRecommendationService()
        self.job_id = "daily_offline_recommendation"
        
    def start(self):
        """启动任务调度器"""
        try:
            self.scheduler.start()
            logger.info("离线任务调度器已启动")
            
            # 立即安排每日任务
            self.schedule_daily_job()
            
        except Exception as e:
            logger.exception(f"启动任务调度器失败: {e}")
            raise
    
    def stop(self):
        """停止任务调度器"""
        try:
            self.scheduler.shutdown()
            logger.info("离线任务调度器已停止")
        except Exception as e:
            logger.exception(f"停止任务调度器失败: {e}")
    
    def schedule_daily_job(self):
        """安排每日推荐的定时任务"""
        try:
            # 移除已有的job
            if self.scheduler.get_job(self.job_id):
                self.scheduler.remove_job(self.job_id)
            
            # 添加新的每日任务
            self.scheduler.add_job(
                func=self._run_daily_recommendation,
                trigger=IntervalTrigger(
                    start_date=datetime.now().replace(hour=2, minute=0, second=0, microsecond=0),
                    hours=24
                ),
                id=self.job_id,
                name="每日Offline推荐生成任务",
                replace_existing=True,
                max_instances=1  # 避免任务重叠执行
            )
            
            next_run = self.scheduler.get_job(self.job_id).next_run_time
            logger.info(f"每日推荐任务已安排，下次执行时间: {next_run}")
            
        except Exception as e:
            logger.exception(f"安排每日任务失败: {e}")
    
    def _run_daily_recommendation(self):
        """
        每日推荐任务的执行函数
        """
        try:
            logger.info("开始执行每日离线推荐任务")
            self.recommendation_service.run_daily_recommendation_task()
            logger.info("每日离线推荐任务执行完成")
            
        except Exception as e:
            logger.exception(f"每日离线推荐任务执行失败: {e}", exc_info=True)
            # 这里可以发送监控告警
            raise
    
    def trigger_immediate_job(self):
        """立即触发一次推荐任务（手动触发）"""
        try:
            # 添加一次性任务立即执行
            job = self.scheduler.add_job(
                func=self._run_daily_recommendation,
                trigger='date',
                run_date=datetime.now(),
                id=f"manual_recommendation_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                name="手动触发的推荐任务"
            )
            
            logger.info("已触发手动推荐任务")
            return job.id
        except Exception as e:
            logger.exception(f"触发手动任务失败: {e}")
            raise
    
    def get_scheduler_status(self) -> Dict:
        """获取调度器状态"""
        return {
            "running": self.scheduler.running,
            "timezone": str(self.scheduler.timezone),
            "jobs": len(self.scheduler.get_jobs()),
            "daily_job": {
                "exists": bool(self.scheduler.get_job(self.job_id)),
                "next_run_time": str(self.scheduler.get_job(self.job_id).next_run_time) 
                               if self.scheduler.get_job(self.job_id) else None
            }
        }
    
    def reschedule_job(self, hours: int = 24, minute: int = 0):
        """
        重新调整任务执行间隔
        
        Args:
            hours: 多少小时执行一次，默认24小时
            minute: 多少分钟开始执行，默认0分钟
        """
        try:
            current_job = self.scheduler.get_job(self.job_id)
            if current_job:
                self.scheduler.reschedule_job(
                    self.job_id,
                    trigger=IntervalTrigger(
                        start_date=datetime.now().replace(
                            hour=datetime.now().hour,
                            minute=minute,
                            second=0,
                            microsecond=0
                        ),
                        hours=hours
                    )
                )
                next_run = self.scheduler.get_job(self.job_id).next_run_time
                logger.info(f"任务已重新安排为每{hours}小时执行，下次执行时间: {next_run}")
            else:
                logger.warning(f"找不到任务 {self.job_id}")
                
        except Exception as e:
            logger.exception(f"重新安排任务失败: {e}")


# 全局调度器实例
_global_scheduler: Optional[OfflineTaskScheduler] = None


def get_scheduler() -> OfflineTaskScheduler:
    """获取全局调度器实例"""
    global _global_scheduler
    if _global_scheduler is None:
        _global_scheduler = OfflineTaskScheduler()
    return _global_scheduler


def start_scheduler():
    """启动全局调度器"""
    scheduler = get_scheduler()
    scheduler.start()


def stop_scheduler():
    """停止全局调度器"""
    global _global_scheduler
    if _global_scheduler:
        _global_scheduler.stop()
        _global_scheduler = None


def get_status() -> Dict:
    """获取调度器状态"""
    scheduler = get_scheduler()
    return scheduler.get_scheduler_status()


def trigger_immediate_job():
    """立即触发一次推荐任务"""
    return get_scheduler().trigger_immediate_job()


def reschedule_job(hours: int = 24, minute: int = 0):
    """重新安排任务执行间隔"""
    return get_scheduler().reschedule_job(hours=hours, minute=minute)