"""
部门Top10常问清单周度分析调度器

基于APScheduler实现的周度定时任务管理，每周自动分析上周的部门聊天数据
"""

from datetime import datetime, timedelta, date
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.triggers.date import DateTrigger
from typing import Optional, Dict, Any

from src.services.dashboard.department_top_questions_analysis_service import DepartmentTopQuestionsAnalysisService
from src.utils.logger import logger


class WeeklyTopQuestionsScheduler:
    """
    部门Top10常问清单周度分析调度器
    
    每周一凌晨2点自动执行上周数据分析，支持手动触发和补偿机制
    """
    
    def __init__(self, analysis_service: Optional[DepartmentTopQuestionsAnalysisService] = None):
        """
        初始化周度分析调度器
        
        Args:
            analysis_service: 分析服务实例，如果为None则创建新实例
        """
        self.scheduler = BackgroundScheduler()
        self.analysis_service = analysis_service or DepartmentTopQuestionsAnalysisService()
        self.job_id = "weekly_top_questions_analysis"
        self._is_running = False
        
    def start(self):
        """启动调度器"""
        try:
            if self._is_running:
                logger.warning("周度分析调度器已在运行中")
                return
                
            self.scheduler.start()
            self._is_running = True
            logger.info("部门Top10常问清单周度分析调度器已启动")
            
            # 安排周度任务
            self.schedule_weekly_job()
            
        except Exception as e:
            logger.exception(f"启动周度分析调度器失败: {e}")
            self._is_running = False
            raise
    
    def stop(self):
        """停止调度器"""
        try:
            if not self._is_running:
                logger.warning("周度分析调度器未在运行")
                return
                
            self.scheduler.shutdown()
            self._is_running = False
            logger.info("部门Top10常问清单周度分析调度器已停止")
            
        except Exception as e:
            logger.exception(f"停止周度分析调度器失败: {e}")
    
    def schedule_weekly_job(self):
        """安排周度分析任务"""
        try:
            # 移除已有的任务
            if self.scheduler.get_job(self.job_id):
                self.scheduler.remove_job(self.job_id)
            
            # 添加新的周度任务：每周一凌晨2点执行
            self.scheduler.add_job(
                func=self._run_weekly_analysis,
                trigger=CronTrigger(
                    day_of_week='mon',  # 周一
                    hour=2,             # 凌晨2点
                    minute=0,           # 0分
                    second=0            # 0秒
                ),
                id=self.job_id,
                name="部门Top10常问清单周度分析任务",
                replace_existing=True,
                max_instances=1,  # 避免任务重叠执行
                misfire_grace_time=3600  # 如果错过执行时间，1小时内仍可执行
            )
            
            next_run = self.scheduler.get_job(self.job_id).next_run_time
            logger.info(f"周度分析任务已安排，下次执行时间: {next_run}")
            
        except Exception as e:
            logger.exception(f"安排周度分析任务失败: {e}")
    
    def _run_weekly_analysis(self):
        """
        执行周度分析任务
        
        分析上周（上周一到上周日）的所有部门数据
        """
        try:
            logger.info("开始执行部门Top10常问清单周度分析任务")
            
            # 获取上周一的日期
            last_monday = self.analysis_service.get_last_monday()
            
            logger.info(f"分析目标周期: {last_monday} 至 {last_monday + timedelta(days=6)}")
            
            # 执行所有部门的分析
            results = self.analysis_service.analyze_all_departments_for_week(last_monday)
            
            # 统计结果
            total_departments = len(results)
            success_count = sum(1 for success in results.values() if success)
            failed_departments = [dept for dept, success in results.items() if not success]
            
            logger.info(f"周度分析任务执行完成:")
            logger.info(f"  总部门数: {total_departments}")
            logger.info(f"  成功分析: {success_count}")
            logger.info(f"  失败分析: {total_departments - success_count}")
            
            if failed_departments:
                logger.warning(f"分析失败的部门: {failed_departments}")
            
            # 可以在这里添加监控告警或通知机制
            if success_count == 0 and total_departments > 0:
                logger.exception("所有部门分析都失败，可能需要人工介入")
            elif success_count < total_departments * 0.8:  # 成功率低于80%
                logger.warning(f"分析成功率较低 ({success_count}/{total_departments})")
            
        except Exception as e:
            logger.exception(f"执行周度分析任务时发生错误: {e}", exc_info=True)
            # 这里可以发送监控告警
            raise
    
    def trigger_immediate_analysis(self, target_week_start: Optional[date] = None) -> str:
        """
        立即触发一次分析任务（手动触发/补偿机制）
        
        Args:
            target_week_start: 目标周的开始日期（周一），如果为None则分析上周
            
        Returns:
            str: 任务ID
        """
        try:
            if target_week_start is None:
                target_week_start = self.analysis_service.get_last_monday()
            
            # 验证是否为周一
            if target_week_start.weekday() != 0:
                raise ValueError(f"目标日期 {target_week_start} 不是周一")
            
            # 生成唯一的任务ID
            task_id = f"manual_weekly_analysis_{target_week_start.strftime('%Y%m%d')}_{datetime.now().strftime('%H%M%S')}"
            
            # 添加一次性任务立即执行
            job = self.scheduler.add_job(
                func=self._run_manual_analysis,
                trigger=DateTrigger(run_date=datetime.now()),
                args=[target_week_start],
                id=task_id,
                name=f"手动触发的周度分析任务 - {target_week_start}",
                max_instances=1
            )
            
            logger.info(f"已触发手动周度分析任务: {task_id}, 目标周期: {target_week_start}")
            return task_id
            
        except Exception as e:
            logger.exception(f"触发手动分析任务失败: {e}")
            raise
    
    def _run_manual_analysis(self, target_week_start: date):
        """
        执行手动触发的分析任务
        
        Args:
            target_week_start: 目标周的开始日期
        """
        try:
            logger.info(f"开始执行手动触发的周度分析: {target_week_start}")
            
            # 执行所有部门的分析
            results = self.analysis_service.analyze_all_departments_for_week(target_week_start)
            
            # 统计结果
            total_departments = len(results)
            success_count = sum(1 for success in results.values() if success)
            
            logger.info(f"手动周度分析完成: 成功 {success_count}/{total_departments} 个部门")
            
        except Exception as e:
            logger.exception(f"执行手动周度分析时发生错误: {e}", exc_info=True)
            raise
    
    def trigger_department_analysis(self, department_name: str, target_week_start: Optional[date] = None) -> str:
        """
        触发单个部门的分析任务
        
        Args:
            department_name: 部门名称
            target_week_start: 目标周的开始日期，如果为None则分析上周
            
        Returns:
            str: 任务ID
        """
        try:
            if target_week_start is None:
                target_week_start = self.analysis_service.get_last_monday()
            
            # 验证是否为周一
            if target_week_start.weekday() != 0:
                raise ValueError(f"目标日期 {target_week_start} 不是周一")
            
            # 生成唯一的任务ID
            task_id = f"manual_dept_analysis_{department_name}_{target_week_start.strftime('%Y%m%d')}_{datetime.now().strftime('%H%M%S')}"
            
            # 添加一次性任务立即执行
            job = self.scheduler.add_job(
                func=self._run_department_analysis,
                trigger=DateTrigger(run_date=datetime.now()),
                args=[department_name, target_week_start],
                id=task_id,
                name=f"手动触发的部门分析任务 - {department_name} - {target_week_start}",
                max_instances=1
            )
            
            logger.info(f"已触发部门分析任务: {task_id}, 部门: {department_name}, 周期: {target_week_start}")
            return task_id
            
        except Exception as e:
            logger.exception(f"触发部门分析任务失败: {e}")
            raise
    
    def _run_department_analysis(self, department_name: str, target_week_start: date):
        """
        执行单个部门的分析任务
        
        Args:
            department_name: 部门名称
            target_week_start: 目标周的开始日期
        """
        try:
            logger.info(f"开始执行部门分析: {department_name}, 周期: {target_week_start}")
            
            success = self.analysis_service.analyze_department_week(department_name, target_week_start)
            
            if success:
                logger.info(f"部门 {department_name} 分析成功")
            else:
                logger.exception(f"部门 {department_name} 分析失败")
            
        except Exception as e:
            logger.exception(f"执行部门分析时发生错误: {e}", exc_info=True)
            raise
    
    def get_scheduler_status(self) -> Dict[str, Any]:
        """
        获取调度器状态信息
        
        Returns:
            Dict[str, Any]: 状态信息
        """
        try:
            status = {
                "is_running": self._is_running,
                "scheduler_state": self.scheduler.state if hasattr(self.scheduler, 'state') else 'unknown',
                "jobs": []
            }
            
            if self._is_running:
                jobs = self.scheduler.get_jobs()
                for job in jobs:
                    job_info = {
                        "id": job.id,
                        "name": job.name,
                        "next_run_time": job.next_run_time.isoformat() if job.next_run_time else None,
                        "trigger": str(job.trigger)
                    }
                    status["jobs"].append(job_info)
            
            return status
            
        except Exception as e:
            logger.exception(f"获取调度器状态失败: {e}")
            return {"is_running": False, "error": str(e)}
    
    def reschedule_weekly_job(self, hour: int = 2, minute: int = 0):
        """
        重新安排周度任务的执行时间
        
        Args:
            hour: 执行小时（0-23）
            minute: 执行分钟（0-59）
        """
        try:
            if not (0 <= hour <= 23):
                raise ValueError("小时必须在0-23之间")
            if not (0 <= minute <= 59):
                raise ValueError("分钟必须在0-59之间")
            
            # 重新安排任务
            if self.scheduler.get_job(self.job_id):
                self.scheduler.reschedule_job(
                    self.job_id,
                    trigger=CronTrigger(
                        day_of_week='mon',
                        hour=hour,
                        minute=minute,
                        second=0
                    )
                )
                
                next_run = self.scheduler.get_job(self.job_id).next_run_time
                logger.info(f"周度任务已重新安排为每周一 {hour:02d}:{minute:02d}，下次执行: {next_run}")
            else:
                logger.warning("未找到周度任务，无法重新安排")
                
        except Exception as e:
            logger.exception(f"重新安排周度任务失败: {e}")
            raise


# 全局调度器实例管理
_global_weekly_scheduler: Optional[WeeklyTopQuestionsScheduler] = None


def get_weekly_scheduler() -> WeeklyTopQuestionsScheduler:
    """
    获取全局周度分析调度器实例（单例模式）

    Returns:
        WeeklyTopQuestionsScheduler: 全局调度器实例
    """
    global _global_weekly_scheduler
    if _global_weekly_scheduler is None:
        _global_weekly_scheduler = WeeklyTopQuestionsScheduler()
    return _global_weekly_scheduler


def start_weekly_scheduler():
    """启动全局周度分析调度器"""
    try:
        scheduler = get_weekly_scheduler()
        scheduler.start()
        logger.info("全局周度分析调度器启动成功")
    except Exception as e:
        logger.exception(f"启动全局周度分析调度器失败: {e}")
        raise


def stop_weekly_scheduler():
    """停止全局周度分析调度器"""
    try:
        global _global_weekly_scheduler
        if _global_weekly_scheduler and _global_weekly_scheduler._is_running:
            _global_weekly_scheduler.stop()
            logger.info("全局周度分析调度器停止成功")
        else:
            logger.info("周度分析调度器未在运行或不存在")
    except Exception as e:
        logger.exception(f"停止全局周度分析调度器失败: {e}")


def trigger_manual_weekly_analysis(target_week_start: Optional[date] = None) -> str:
    """
    手动触发周度分析（全局接口）

    Args:
        target_week_start: 目标周的开始日期，如果为None则分析上周

    Returns:
        str: 任务ID
    """
    try:
        scheduler = get_weekly_scheduler()
        return scheduler.trigger_immediate_analysis(target_week_start)
    except Exception as e:
        logger.exception(f"手动触发周度分析失败: {e}")
        raise


def trigger_manual_department_analysis(department_name: str, target_week_start: Optional[date] = None) -> str:
    """
    手动触发单个部门分析（全局接口）

    Args:
        department_name: 部门名称
        target_week_start: 目标周的开始日期，如果为None则分析上周

    Returns:
        str: 任务ID
    """
    try:
        scheduler = get_weekly_scheduler()
        return scheduler.trigger_department_analysis(department_name, target_week_start)
    except Exception as e:
        logger.exception(f"手动触发部门分析失败: {e}")
        raise


def get_weekly_scheduler_status() -> Dict[str, Any]:
    """
    获取周度分析调度器状态（全局接口）

    Returns:
        Dict[str, Any]: 状态信息
    """
    try:
        global _global_weekly_scheduler
        if _global_weekly_scheduler:
            return _global_weekly_scheduler.get_scheduler_status()
        else:
            return {"is_running": False, "message": "调度器未初始化"}
    except Exception as e:
        logger.exception(f"获取调度器状态失败: {e}")
        return {"is_running": False, "error": str(e)}
