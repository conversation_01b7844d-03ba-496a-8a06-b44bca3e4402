"""
配置验证工具

验证Agent配置文件的完整性和正确性，确保Agent as Tool架构的工具描述清晰准确。
"""
import yaml
from typing import Dict, List, Any, Optional
from pathlib import Path

from src.utils.logger import logger
from src.utils.resource_manager import list_resources, load_resource


class AgentConfigValidator:
    """Agent配置验证器"""
    
    def __init__(self):
        self.required_fields = {
            'agent_name': str,
            'agent_description': str,
            'tools': list,
            'agent_tables': list
        }
        self.optional_fields = {
            'model': str,
            'model_settings': dict,
            'need_system_prompt': bool
        }
        
    def validate_all_configs(self) -> Dict[str, Any]:
        """验证所有Agent配置文件"""
        validation_results = {
            "total_configs": 0,
            "valid_configs": 0,
            "invalid_configs": 0,
            "config_details": [],
            "summary": {}
        }
        
        try:
            # 获取所有配置文件
            config_files = list_resources('data_fetcher_bot_config', '.yml')
            validation_results["total_configs"] = len(config_files)
            
            for config_file in config_files:
                result = self.validate_single_config(config_file)
                validation_results["config_details"].append(result)
                
                if result["is_valid"]:
                    validation_results["valid_configs"] += 1
                else:
                    validation_results["invalid_configs"] += 1
            
            # 生成摘要
            validation_results["summary"] = self._generate_summary(validation_results["config_details"])
            
        except Exception as e:
            logger.exception(f"验证配置文件时出错: {e}")
            validation_results["error"] = str(e)
        
        return validation_results
    
    def validate_single_config(self, config_file: str) -> Dict[str, Any]:
        """验证单个配置文件"""
        result = {
            "config_file": config_file,
            "is_valid": False,
            "errors": [],
            "warnings": [],
            "agent_info": {}
        }
        
        try:
            # 加载配置文件
            config_path = Path("resources/data_fetcher_bot_config") / config_file
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            if not isinstance(config, dict):
                result["errors"].append("配置文件格式错误，应为YAML字典格式")
                return result
            
            # 验证必需字段
            self._validate_required_fields(config, result)
            
            # 验证可选字段
            self._validate_optional_fields(config, result)
            
            # 验证Agent描述质量
            self._validate_agent_description(config, result)
            
            # 验证工具配置
            self._validate_tools_config(config, result)
            
            # 验证表配置
            self._validate_tables_config(config, result)
            
            # 提取Agent信息
            result["agent_info"] = {
                "agent_name": config.get("agent_name"),
                "description_length": len(config.get("agent_description", "")),
                "tools_count": len(config.get("tools", [])),
                "tables_count": len(config.get("agent_tables", [])),
                "has_model_config": "model" in config,
                "has_model_settings": "model_settings" in config
            }
            
            # 判断是否有效
            result["is_valid"] = len(result["errors"]) == 0
            
        except Exception as e:
            result["errors"].append(f"加载配置文件失败: {str(e)}")
            logger.exception(f"验证配置文件 {config_file} 时出错: {e}")
        
        return result
    
    def _validate_required_fields(self, config: Dict[str, Any], result: Dict[str, Any]):
        """验证必需字段"""
        for field, expected_type in self.required_fields.items():
            if field not in config:
                result["errors"].append(f"缺少必需字段: {field}")
            elif not isinstance(config[field], expected_type):
                result["errors"].append(f"字段 {field} 类型错误，期望 {expected_type.__name__}")
    
    def _validate_optional_fields(self, config: Dict[str, Any], result: Dict[str, Any]):
        """验证可选字段"""
        for field, expected_type in self.optional_fields.items():
            if field in config and not isinstance(config[field], expected_type):
                result["warnings"].append(f"字段 {field} 类型错误，期望 {expected_type.__name__}")
    
    def _validate_agent_description(self, config: Dict[str, Any], result: Dict[str, Any]):
        """验证Agent描述质量"""
        description = config.get("agent_description", "")
        
        if len(description) < 50:
            result["warnings"].append("Agent描述过短，建议提供更详细的描述")
        
        # 检查是否包含关键信息
        key_sections = ["核心业务", "背景知识", "典型SQL", "关键表"]
        missing_sections = []
        for section in key_sections:
            if section not in description:
                missing_sections.append(section)
        
        if missing_sections:
            result["warnings"].append(f"Agent描述缺少关键章节: {', '.join(missing_sections)}")
    
    def _validate_tools_config(self, config: Dict[str, Any], result: Dict[str, Any]):
        """验证工具配置"""
        tools = config.get("tools", [])
        
        if not tools:
            result["errors"].append("Agent必须配置至少一个工具")
            return
        
        # 验证工具格式
        for i, tool in enumerate(tools):
            if not isinstance(tool, dict):
                result["errors"].append(f"工具配置 {i} 格式错误，应为字典格式")
                continue
            
            if "name" not in tool:
                result["errors"].append(f"工具配置 {i} 缺少name字段")
    
    def _validate_tables_config(self, config: Dict[str, Any], result: Dict[str, Any]):
        """验证表配置"""
        tables = config.get("agent_tables", [])
        
        if not tables:
            result["warnings"].append("Agent未配置相关数据表")
            return
        
        # 验证表格式
        for i, table in enumerate(tables):
            if isinstance(table, dict):
                if "name" not in table:
                    result["warnings"].append(f"表配置 {i} 缺少name字段")
            elif isinstance(table, str):
                # 简单字符串格式也是可以的
                pass
            else:
                result["warnings"].append(f"表配置 {i} 格式错误")
    
    def _generate_summary(self, config_details: List[Dict[str, Any]]) -> Dict[str, Any]:
        """生成验证摘要"""
        summary = {
            "agents_by_domain": {},
            "common_tools": {},
            "common_tables": {},
            "model_usage": {},
            "description_quality": {
                "detailed": 0,
                "basic": 0,
                "minimal": 0
            }
        }
        
        for config in config_details:
            if not config["is_valid"]:
                continue
                
            agent_info = config["agent_info"]
            agent_name = agent_info.get("agent_name", "unknown")
            
            # 按领域分类
            if "sales" in agent_name or "order" in agent_name:
                domain = "销售分析"
            elif "warehouse" in agent_name or "fulfillment" in agent_name:
                domain = "仓储物流"
            elif "general" in agent_name or "chat" in agent_name:
                domain = "知识问答"
            else:
                domain = "其他"
            
            if domain not in summary["agents_by_domain"]:
                summary["agents_by_domain"][domain] = []
            summary["agents_by_domain"][domain].append(agent_name)
            
            # 描述质量分析
            desc_length = agent_info.get("description_length", 0)
            if desc_length > 1000:
                summary["description_quality"]["detailed"] += 1
            elif desc_length > 200:
                summary["description_quality"]["basic"] += 1
            else:
                summary["description_quality"]["minimal"] += 1
        
        return summary
    
    def generate_report(self, validation_results: Dict[str, Any]) -> str:
        """生成验证报告"""
        report = []
        report.append("# Agent配置验证报告\n")
        
        # 总体统计
        report.append("## 总体统计")
        report.append(f"- 总配置文件数: {validation_results['total_configs']}")
        report.append(f"- 有效配置: {validation_results['valid_configs']}")
        report.append(f"- 无效配置: {validation_results['invalid_configs']}")
        report.append("")
        
        # 按领域分类
        summary = validation_results.get("summary", {})
        agents_by_domain = summary.get("agents_by_domain", {})
        if agents_by_domain:
            report.append("## 按领域分类")
            for domain, agents in agents_by_domain.items():
                report.append(f"- **{domain}**: {', '.join(agents)}")
            report.append("")
        
        # 详细结果
        report.append("## 详细验证结果")
        for config in validation_results["config_details"]:
            status = "✅" if config["is_valid"] else "❌"
            report.append(f"### {status} {config['config_file']}")
            
            if config["agent_info"]:
                info = config["agent_info"]
                report.append(f"- Agent名称: {info.get('agent_name', 'N/A')}")
                report.append(f"- 描述长度: {info.get('description_length', 0)} 字符")
                report.append(f"- 工具数量: {info.get('tools_count', 0)}")
                report.append(f"- 表数量: {info.get('tables_count', 0)}")
            
            if config["errors"]:
                report.append("**错误:**")
                for error in config["errors"]:
                    report.append(f"- {error}")
            
            if config["warnings"]:
                report.append("**警告:**")
                for warning in config["warnings"]:
                    report.append(f"- {warning}")
            
            report.append("")
        
        return "\n".join(report)


def validate_agent_configs() -> str:
    """验证所有Agent配置并返回报告"""
    validator = AgentConfigValidator()
    results = validator.validate_all_configs()
    return validator.generate_report(results)
