"""订单批次和供应商查询工具"""

from typing import Tuple, List
from agents import RunContextWrapper
from src.models.user_info_class import UserInfo
from src.services.agent.tools.feishu_tools import upload_sql_result_to_feishu_if_needed
from src.services.xianmudb.query_service import execute_business_query
from src.models.query_result import SQLQueryResult
from src.utils.logger import logger
from src.services.agent.tools.tool_manager import tool_manager


async def query_order_item_trace_info(
    wrapper: RunContextWrapper[UserInfo],
    order_id: str,
    product_name: str = None,
    sku_codes: List[str] = None,
    upload_to_feishu: bool = False,
) -> Tuple[SQLQueryResult, str]:
    """查询指定订单的商品溯源信息

    Args:
        wrapper: 包含用户信息的上下文包装器
        order_id: 订单号，例如 "0125CLZMH70630202878"，必传
        product_name: 商品名称，可选，用于过滤特定产品，例如 "西瓜", 与sku_codes二选一
        sku_codes: SKU编码列表，可选，用于精确匹配多个SKU，例如 ["SKU001", "SKU002"]，与product_name二选一，两者都有优先使用sku_codes参数
        upload_to_feishu: 是否需要将查询结果上传到飞书，默认为False

    Returns:
        Tuple[SQLQueryResult, str]: 查询结果和对查询的描述
        SQLQueryResult.data 包含以下字段：
        - 订单号: 订单的唯一标识编号
        - 配送门店名称: 配送门店的名称
        - sku编码: 商品的唯一标识编码
        - sku名称: 商品的名称
        - sku规格: 商品的规格
        - 配送扫描溯源码: 配送时扫描的溯源码
        - 溯源批次: 商品的当前批次
        - 原始采购批次: 商品的原始采购批次
        - 供应商: 商品的供应商
        - 收货方式: 商品入库的货检收货方式
        - 货检任务链接: 商品入库的货检任务链接
        - 配送门店抬头照片: 配送门店的抬头照片链接
        - 配送单照片: 配送单的照片链接
        - 配送货物照片: 配送货物的照片链接
        - 配送备注: 配送备注
        - 实际配送时间: 实际配送时间
        - 计划配送日期: 计划配送日期
    """
    
    logger.info(f"查询订单商品溯源工具被调用，订单号: {order_id}, 产品名称: {product_name}, SKU编码: {sku_codes}")
    
    if wrapper is None:
        logger.exception("用户上下文不存在")
        return SQLQueryResult(success=False, error="用户上下文不存在"), "查询失败"
    
    if order_id is None or order_id == '':
        logger.exception("订单号不能为空")
        return SQLQueryResult(success=False, error="订单号不能为空"), "查询失败"
    
    user_info = wrapper.context
    logger.info(f"用户上下文: {user_info}")
    
    # 构建SQL查询语句
    sql_query = f"""
    select 
        tdo.`outer_order_id` as '订单号',
        tdo.`outer_client_name` as '配送门店名称',
        tdsi.`out_item_id` as 'sku编码',
        tdsi.`out_item_name` as 'sku名称',
        sku.weight as 'sku规格',
        tdsic.`only_code` as '配送扫描溯源码',
        sbc.purchase_no as '溯源批次',
        (select 
            ifnull(GROUP_CONCAT(distinct bro.origin_purchase_no), sbc.purchase_no)
          from warehouse_cost_batch wcb
          left join `batch_relation_origin` bro on wcb.id = bro.`current_batch_id` 
          where wcb.`sku` = sbc.sku
          and wcb.`purchase_no`  = sbc.purchase_no
        ) as '原始采购批次',
        (select 
            ifnull(GROUP_CONCAT(distinct s.`name`), GROUP_CONCAT(distinct s2.`name`))
          from warehouse_cost_batch wcb
          left join `supplier` s2 on s2.`id` = wcb.`supplier_id`  
          left join `batch_relation_origin` bro on wcb.id = bro.`current_batch_id` 
          left join `supplier` s on s.`id` = bro.`origin_supplier_id` 
          where wcb.`sku` = sbc.sku
          and wcb.`purchase_no`  = sbc.purchase_no
        ) as '供应商',
        (select 
            CONCAT(GROUP_CONCAT(distinct (case qit.`receive_way` when 0 then '正常入库'
                                when 1 then '挑选入库'
                                when 2 then '让步接收'
                                when 3 then '特批入库'
                                when 4 then '拒收'
                                when 5 then '换货入库'
                                ELSE '' end
            )), '_',GROUP_CONCAT(distinct (case qit.`insp_state` when 10 then '待分配'
                                when 20 then '待执行'
                                when 30 then '执行中'
                                when 40 then '执行中'
                                when 50 then '已取消'
                                when 60 then '已完成'
                                when 70 then '异常完成'
                                ELSE '' end
            )))
            from warehouse_cost_batch wcb
            left join `supplier` s2 on s2.`id` = wcb.`supplier_id`  
            left join `batch_relation_origin` bro on wcb.id = bro.`current_batch_id` 
            left join `qms_inspection_task` qit on qit.`source_type`  = 11 and qit.`warehouse_no`  = if(bro.origin_purchase_no is null, wcb.warehouse_no, bro.`origin_warehouse_no`) 
                and qit.sku = if(bro.origin_purchase_no is null, `wcb`.`sku`, bro.`origin_sku` )  and qit.`batch_no`  = ifnull(bro.origin_purchase_no, wcb.purchase_no)
            where wcb.`sku` = sbc.sku
            and wcb.`purchase_no`  = sbc.purchase_no
        ) as '收货方式',
        (select 
            GROUP_CONCAT(distinct(CONCAT('https://admin.summerfarm.net/summerfarm-fe/wms_store/inspection/detail?id=', qit.id)))
            from warehouse_cost_batch wcb
            left join `supplier` s2 on s2.`id` = wcb.`supplier_id`  
            left join `batch_relation_origin` bro on wcb.id = bro.`current_batch_id` 
            left join `qms_inspection_task` qit on qit.`source_type`  = 11 and qit.`warehouse_no`  = if(bro.origin_purchase_no is null, wcb.warehouse_no, bro.`origin_warehouse_no`) 
                and qit.sku = if(bro.origin_purchase_no is null, `wcb`.`sku`, bro.`origin_sku` )  and qit.`batch_no`  = ifnull(bro.origin_purchase_no, wcb.purchase_no)
            where wcb.`sku` = sbc.sku
            and wcb.`purchase_no`  = sbc.purchase_no
        ) as '货检任务链接',
          CASE
           WHEN tds.`sign_in_pics` IS NULL OR tds.`sign_in_pics` = '' THEN ''
           ELSE CONCAT('https://azure.summerfarm.net/',
                       REPLACE(tds.`sign_in_pics`, ',', ',https://azure.summerfarm.net/'))
           END as '配送门店抬头照片',
       CASE
           WHEN tds.`sign_in_sign_pic` IS NULL OR tds.`sign_in_sign_pic` = '' THEN ''
           ELSE CONCAT('https://azure.summerfarm.net/',
                       REPLACE(tds.`sign_in_sign_pic`, ',', ',https://azure.summerfarm.net/'))
           END as '配送单照片',
       CASE
           WHEN tds.`sign_in_product_pic` IS NULL OR tds.`sign_in_product_pic` = '' THEN ''
           ELSE CONCAT('https://azure.summerfarm.net/',
                       REPLACE(tds.`sign_in_product_pic`, ',', ',https://azure.summerfarm.net/'))
           END as '配送货物照片',
        concat(tds.sign_in_remark, '', tds.out_reason) as '配送备注',           
        tds.sign_in_time as '实际配送时间',
        tds.plan_arrive_time as '计划配送日期'
        from `tms_dist_order` tdo
        inner join `tms_delivery_order` tdo2 on tdo2.`dist_order_id` = tdo.`id` 
        inner join `tms_delivery_site` tds on tds.`delivery_batch_id` = tdo2.`batch_id` 
          and tds.`site_id`  = tdo.`end_site_id` 
        INNER JOIN `tms_delivery_site_item` tdsi on tdsi.`delivery_site_id`  = tds.id
          and tdsi.`type` = 0
        inner join `inventory` sku on tdsi.`out_item_id` = sku.`sku` 
        left join `tms_delivery_site_item_code` tdsic on tdsic.`delivery_site_item_id` = tdsi.id
        LEFT JOIN `sku_batch_code` sbc on sbc.`sku_batch_only_code` = SUBSTRING(tdsic.`only_code`, 1, INSTR(tdsic.`only_code`, 'S'))
          and sbc.`sku`  = tdsi.`out_item_id` 
        where tdo.`outer_order_id` = '{order_id}'"""
    
    # 添加过滤条件 - sku_codes优先使用
    if sku_codes:
        sku_codes_str = "', '".join(sku_codes)
        sql_query += f" and tdsi.`out_item_id` in ('{sku_codes_str}')"
    elif product_name:
        sql_query += f" and tdsi.`out_item_name` like '%{product_name}%'"
    
    # 添加排序
    sql_query += " order by tdsi.`out_item_name`, sbc.purchase_no"
    
    description = f"查询订单{order_id}的批次和供应商信息"
    if product_name:
        description += f"（产品：{product_name}）"
    if sku_codes:
        description += f"（SKU：{', '.join(sku_codes)}）"
    
    try:
        # 执行查询
        result = execute_business_query(sql_query)
        
        if result and result.success:
            logger.info(f"查询成功，返回{len(result.data) if result.data else 0}条记录")
            
            # 如果需要上传到飞书
            if upload_to_feishu:
                final_result = await upload_sql_result_to_feishu_if_needed(
                    sql_result=result,
                    sql_description=description,
                    user_info=user_info,
                    upload_to_feishu=upload_to_feishu,
                )
                return final_result, description
            
            return result, description
        else:
            error_msg = result.error if result else "查询失败"
            logger.exception(f"查询失败: {error_msg}")
            return SQLQueryResult(success=False, error=error_msg), description
            
    except Exception as e:
        error_msg = f"查询过程中发生错误: {sql_query} {params}"
        logger.exception(error_msg)
        return SQLQueryResult(success=False, error=error_msg), description


async def query_warehouse_in_transit_inventory(
    wrapper: RunContextWrapper[UserInfo],
    warehouse_name: str = None,
    area_name: str = None,
    product_name: str = None,
    sku_codes: List[str] = None,
    upload_to_feishu: bool = False,
) -> Tuple[SQLQueryResult, str]:
    """查询指定仓库或地区的在途库存和仓库库存信息（包括采购在途和调拨在途）
    场景：询问库存在途的问题示例：什么时间有货、什么时间到货、什么时间上架、有货情况、到货情况、在途情况等

    Args:
        wrapper: 包含用户信息的上下文包装器
        warehouse_name: 仓库名称，可选，例如 "嘉兴总仓"、"东莞总仓"，与area_name二选一
        area_name: 地区名称，可选，例如 "佛山"，与warehouse_name二选一
        product_name: 商品名称，可选，用于过滤特定产品，例如 "安佳淡奶油", 与sku_codes二选一
        sku_codes: SKU编码列表，可选，用于精确匹配多个SKU，例如 ["SKU001", "SKU002"]，与product_name二选一，两者都有优先使用sku_codes参数
        upload_to_feishu: 是否需要将查询结果上传到飞书，默认为False

    Returns:
        Tuple[SQLQueryResult, str]: 查询结果和对查询的描述
        SQLQueryResult.data 是一个结构化字典，包含两个部分：
        
        "在途库存": 列表，每个元素包含以下字段：
        - 类型: 采购在途/调拨在途
        - 单号: 采购单/调拨单编号
        - 预计到货日期: 预计到货时间
        - 仓库名称: 仓库名称
        - SKU编码: 商品的唯一标识编码
        - 商品名称: 商品的名称
        - 规格: 商品的规格
        - 数量: 预计到货数量
        
        "仓库库存": 列表，每个元素包含以下字段：
        - 类型: 可售库存/仓库库存/锁定库存
        - 仓库名称: 仓库名称
        - SKU编码: 商品的唯一标识编码
        - 商品名称: 商品的名称
        - 规格: 商品的规格
        - 数量: 库存数量
    """
    try:
        logger.info(f"查询仓库在途库存工具被调用，仓库名称: {warehouse_name}, 地区名称: {area_name}, 产品名称: {product_name}, SKU编码: {sku_codes}")
        
        if wrapper is None:
            logger.exception("用户上下文不存在")
            return SQLQueryResult(success=False, error="用户上下文不存在"), "查询失败"
        
        if not warehouse_name and not area_name:
            logger.exception("仓库名称和地区名称至少需要提供一个")
            return SQLQueryResult(success=False, error="仓库名称和地区名称至少需要提供一个"), "查询失败"
        
        if not product_name and not sku_codes:
            logger.exception("商品名称和SKU编码至少需要提供一个")
            return SQLQueryResult(success=False, error="商品名称和SKU编码至少需要提供一个"), "查询失败"
        
        # 去掉仓库名称中的仓关键字
        if warehouse_name:
            warehouse_name = warehouse_name.replace('仓库', '')
            warehouse_name = warehouse_name.replace('仓', '')
        
        user_info = wrapper.context
        logger.info(f"用户上下文: {user_info}")
        
        # 如果提供了地区名称但没有仓库名称，先查询供货仓库
        target_warehouse_names = []
        if area_name and not warehouse_name:
            logger.info(f"通过地区 '{area_name}' 查询供货仓库")
            supply_result, _ = await query_product_supply_warehouse(
                wrapper=wrapper,
                product_name=product_name,
                area_name=area_name,
                sku_codes=sku_codes,
                upload_to_feishu=upload_to_feishu
            )
            
            if supply_result.success and supply_result.data:
                # SQLQueryResult.data 是 List[List[Any]] 格式，需要结合 columns 来提取数据
                warehouse_name_index = supply_result.columns.index('仓库名称') if '仓库名称' in supply_result.columns else -1
                if warehouse_name_index >= 0:
                    target_warehouse_names = [row[warehouse_name_index] for row in supply_result.data]
                    logger.info(f"找到供货仓库: {target_warehouse_names}")
                else:
                    logger.exception("查询结果中未找到'仓库名称'字段")
                    return SQLQueryResult(success=False, error="查询结果中未找到'仓库名称'字段"), "查询失败"
            else:
                logger.warning(f"未找到地区 '{area_name}' 的供货仓库")
                return SQLQueryResult(success=False, error=f"未找到地区 '{area_name}' 的供货仓库"), "查询失败"
        elif warehouse_name:
            target_warehouse_names = [warehouse_name]
        
        # 构建产品过滤条件 - sku_codes优先使用
        product_filter = ""
        if sku_codes:
            sku_codes_str = "', '".join(sku_codes)
            product_filter += f" AND sai.sku in ('{sku_codes_str}')"
        elif product_name:
            product_filter += f" AND p.`pd_name` like '%{product_name}%'"
        
        # 收集在途库存和仓库库存结果
        in_transit_data = []
        warehouse_data = []
        
        # 对每个仓库查询在途库存和仓库库存
        for wh_name in target_warehouse_names:
            # 查询在途库存
            in_transit_sql = f"""
            -- 采购在途数据
            SELECT
            '采购在途' as 类型,
            sa.purchase_no as 单号,
            sa.arrange_time AS 预计到货日期,
            w.warehouse_name AS 仓库名称,
            sai.sku AS SKU编码,
            p.pd_name AS 商品名称,
            i.weight AS 规格,
            (sai.arrival_quantity - sai.actual_quantity) AS 数量
            FROM
            stock_arrange sa
            JOIN stock_arrange_item sai ON sa.id = sai.stock_arrange_id
            JOIN warehouse_storage_center w ON sa.warehouse_no = w.warehouse_no
                AND w.`warehouse_name` like '%{wh_name}%'
                and w.`warehouse_name` not like '%测试%'
                and w.status = 1
            JOIN inventory i ON sai.sku = i.sku
            JOIN products p ON i.pd_id = p.pd_id
            WHERE sa.state = 0
            and sa.arrange_time >= current_date()
            and (sai.arrival_quantity - sai.actual_quantity) > 0
            {product_filter}
            
            UNION ALL
            
            -- 调拨在途数据
            select
            '调拨在途' as 类型,
            al.`list_no` as 单号,
            st.`expect_time` as 预计到货日期,
            max(w.`warehouse_name`) 仓库名称,
            ai.sku as SKU编码,
            max(p.`pd_name`) 商品名称,
            max(i.`weight`) 规格,
            sum(ai.`out_quantity` - ai.`actual_in_quantity`) 数量
            from
            `stock_allocation_list` al
            join `warehouse_storage_center` w on al.`in_store` = w.`warehouse_no`
                and w.`warehouse_name` like '%{wh_name}%'
                and w.`warehouse_name` not like '%测试%'
                and w.status = 1
            join `stock_allocation_item` ai on al.`list_no` = ai.`list_no`
            JOIN `inventory` i on ai.`sku` = i.`sku`
            JOIN `products` p on i.`pd_id` = p.`pd_id`
            left join `stock_task` st on al.`list_no` = st.`task_no`
                and st.`type` = 10
            where
            al.`status` = 5
            and st.`expect_time` >= current_date()
            {product_filter.replace('sai.sku', 'ai.sku')}
            GROUP BY
            al.`list_no`,
            st.`expect_time`,
            ai.sku
            having sum(ai.`out_quantity` - ai.`actual_in_quantity`) != 0
            """
            
            # 执行在途库存查询
            in_transit_result = execute_business_query(in_transit_sql)
            if in_transit_result.success and in_transit_result.data:
                # in_transit_result.data 是 List[List[Any]] 格式，需要转换为字典格式
                columns = in_transit_result.columns
                for row in in_transit_result.data:
                    # 根据列索引获取数据
                    类型_idx = columns.index('类型') if '类型' in columns else -1
                    单号_idx = columns.index('单号') if '单号' in columns else -1
                    预计到货日期_idx = columns.index('预计到货日期') if '预计到货日期' in columns else -1
                    仓库名称_idx = columns.index('仓库名称') if '仓库名称' in columns else -1
                    SKU编码_idx = columns.index('SKU编码') if 'SKU编码' in columns else -1
                    商品名称_idx = columns.index('商品名称') if '商品名称' in columns else -1
                    规格_idx = columns.index('规格') if '规格' in columns else -1
                    数量_idx = columns.index('数量') if '数量' in columns else -1
                    
                    in_transit_data.append({
                        '类型': row[类型_idx] if 类型_idx >= 0 else '',
                        '单号': row[单号_idx] if 单号_idx >= 0 else '',
                        '预计到货日期': row[预计到货日期_idx] if 预计到货日期_idx >= 0 else '',
                        '仓库名称': row[仓库名称_idx] if 仓库名称_idx >= 0 else '',
                        'SKU编码': row[SKU编码_idx] if SKU编码_idx >= 0 else '',
                        '商品名称': row[商品名称_idx] if 商品名称_idx >= 0 else '',
                        '规格': row[规格_idx] if 规格_idx >= 0 else '',
                        '数量': row[数量_idx] if 数量_idx >= 0 else 0
                    })
            
            # 查询仓库库存
            inventory_result, _ = await query_product_warehouse_inventory(
                wrapper=wrapper,
                product_name=product_name,
                warehouse_name=wh_name,
                sku_codes=sku_codes,
                upload_to_feishu=upload_to_feishu
            )
            if inventory_result.success and inventory_result.data:
                # 获取列索引
                columns = inventory_result.columns
                warehouse_name_idx = columns.index('仓库名称') if '仓库名称' in columns else -1
                sku_idx = columns.index('SKU') if 'SKU' in columns else -1
                product_name_idx = columns.index('商品名称') if '商品名称' in columns else -1
                spec_idx = columns.index('规格') if '规格' in columns else -1
                available_stock_idx = columns.index('可售库存') if '可售库存' in columns else -1
                warehouse_stock_idx = columns.index('仓库库存') if '仓库库存' in columns else -1
                locked_stock_idx = columns.index('锁定库存') if '锁定库存' in columns else -1
                
                # 转换仓库库存数据格式
                for row in inventory_result.data:
                    warehouse_data.append({
                        '仓库名称': row[warehouse_name_idx] if warehouse_name_idx >= 0 and len(row) > warehouse_name_idx else '',
                        'SKU编码': row[sku_idx] if sku_idx >= 0 and len(row) > sku_idx else '',
                        '商品名称': row[product_name_idx] if product_name_idx >= 0 and len(row) > product_name_idx else '',
                        '规格': row[spec_idx] if spec_idx >= 0 and len(row) > spec_idx else '',
                        '可售库存': row[available_stock_idx] if available_stock_idx >= 0 and len(row) > available_stock_idx else 0,
                        '仓库库存': row[warehouse_stock_idx] if warehouse_stock_idx >= 0 and len(row) > warehouse_stock_idx else 0,
                        '锁定库存': row[locked_stock_idx] if locked_stock_idx >= 0 and len(row) > locked_stock_idx else 0
                    })
        
        # 构建描述信息
        if area_name:
            description = f"查询地区 '{area_name}' 的在途库存和仓库库存信息"
        else:
            description = f"查询仓库 '{warehouse_name}' 的在途库存和仓库库存信息"
        
        if product_name:
            description += f"（产品：{product_name}）"
        if sku_codes:
            description += f"（SKU：{', '.join(sku_codes)}）"
        

        # 创建结构化的最终结果
        structured_result = {
            "在途库存": in_transit_data,
            "仓库库存": warehouse_data
        }
        
        final_result = SQLQueryResult(success=True, data=structured_result)
        
        logger.info(f"查询成功，返回在途库存{len(in_transit_data)}条记录，仓库库存{len(warehouse_data)}条记录")
        logger.info(f"在途库存返回结果 {final_result}")
        
        # 如果需要上传到飞书
        if upload_to_feishu:
            final_result = await upload_sql_result_to_feishu_if_needed(
                sql_result=final_result,
                sql_description=description,
                user_info=user_info,
                upload_to_feishu=upload_to_feishu,
            )
            return final_result, description
        
        return final_result, description
            
    except Exception as e:
        error_msg = f"查询过程中发生错误: {str(e)}"
        logger.exception(error_msg)
        return SQLQueryResult(success=False, error=error_msg), description


async def query_product_supply_warehouse(
    wrapper: RunContextWrapper[UserInfo],
    product_name: str = None,
    area_name: str = None,
    sku_codes: List[str] = None,
    upload_to_feishu: bool = False,
) -> Tuple[SQLQueryResult, str]:
    """查询商品在指定地区的供货仓库信息

    Args:
        wrapper: 包含用户信息的上下文包装器
        product_name: 商品名称，例如 "埃及红西柚"，可选, 与sku_codes二选一
        area_name: 地区名称，例如 "南京"，可选
        sku_codes: SKU编码列表，可选，用于精确匹配特定SKU，例如 ["SKU001", "SKU002"]，与product_name二选一，两者都有优先使用sku_codes参数
        upload_to_feishu: 是否需要将查询结果上传到飞书，默认为False

    Returns:
        Tuple[SQLQueryResult, str]: 查询结果和对查询的描述
        SQLQueryResult.data 包含以下字段：
        - 仓库编码: 仓库的唯一标识编码
        - 仓库名称: 仓库的名称
    """
    
    logger.info(f"查询商品供货仓库工具被调用，商品名称: {product_name}, 地区名称: {area_name}, SKU编码: {sku_codes}")
    
    if wrapper is None:
        logger.exception("用户上下文不存在")
        return SQLQueryResult(success=False, error="用户上下文不存在"), "查询失败"
    
    if not product_name and not sku_codes:
        logger.error("商品名称和SKU编码至少需要提供一个")
        return SQLQueryResult(success=False, error="商品名称和SKU编码至少需要提供一个"), "查询失败"
    
    if not area_name:
        logger.exception("地区名称不能为空")
        return SQLQueryResult(success=False, error="地区名称不能为空"), "查询失败"
    
    user_info = wrapper.context
    logger.info(f"用户上下文: {user_info}")
    
    # 构建SQL查询语句
    sql_query = f"""
    select 
      distinct 
      wsc.warehouse_no as '仓库编码',
      wsc.warehouse_name as '仓库名称'
    from area area1
    inner join `fence` f on area1.area_no = f.area_no and f.`status` = 0 
    inner join `warehouse_inventory_mapping` wim on  f.`store_no`  = wim.`store_no` 
    inner join `warehouse_storage_center` wsc on wsc.`warehouse_no` = wim.`warehouse_no` and wsc.`status` = 1 
    inner JOIN `inventory` sku on sku.`sku`  = wim.`sku` and sku.outdated = 0 
    inner join `products` spu on spu.pd_id = sku.pd_id
    where area1.`area_name` like '%{area_name}%'"""
    
    # 添加产品过滤条件 - sku_codes优先使用
    if sku_codes:
        sku_list = "', '".join(sku_codes)
        sql_query += f" and sku.sku IN ('{sku_list}')"
    elif product_name:
        sql_query += f" and spu.pd_name like '%{product_name}%'"
    
    try:
        # 执行查询
        result = execute_business_query(sql_query)
        
        if result.success:
            logger.info(f"查询成功，返回 {len(result.data)} 条记录")
            
            # 构建描述信息
            description = f"查询地区 '{area_name}' 的供货仓库信息"
            if product_name:
                description = f"查询商品 '{product_name}' 在地区 '{area_name}' 的供货仓库信息"
            if sku_codes:
                description += f"（SKU：{', '.join(sku_codes)}）"
            
            # 如果需要上传到飞书
            if upload_to_feishu:
                result = await upload_sql_result_to_feishu_if_needed(
                    result, description, wrapper.context, upload_to_feishu
                )
            
            return result, description
        else:
            logger.exception(f"查询失败: {result.error}")
            return result, "查询失败"
            
    except Exception as e:
        logger.exception(f"查询商品供货仓库时发生异常: {str(e)}")
        return SQLQueryResult(success=False, error=str(e)), "查询异常"


async def query_product_certificate_reports(
    wrapper: RunContextWrapper[UserInfo],
    product_name: str = None,
    warehouse_name: str = None,
    area_name: str = None,
    production_date: str = None,
    sku_codes: List[str] = None,
    upload_to_feishu: bool = False,
) -> Tuple[SQLQueryResult, str]:
    """查询商品证件报告信息（质检报告、报关证明、核酸检测、消毒证明等）

    Args:
        wrapper: 包含用户信息的上下文包装器
        product_name: 商品名称，例如 "安佳淡奶油"，可选, 与sku_codes二选一
        warehouse_name: 仓库名称，可选，例如 "嘉兴总仓"，与area_name二选一
        area_name: 地区名称，可选，例如 "佛山"，与warehouse_name二选一
        production_date: 生产日期，可选，格式为 "YYYY-MM-DD"，例如 "2025-01-06"
        sku_codes: SKU编码列表，可选，用于精确匹配特定SKU，例如 ["SKU001", "SKU002"]，与product_name二选一，两者都有优先使用sku_codes参数
        upload_to_feishu: 是否需要将查询结果上传到飞书，默认为False

    Returns:
        Tuple[SQLQueryResult, str]: 查询结果和对查询的描述
        SQLQueryResult.data 包含以下字段：
        - SKU: 商品的唯一标识编码
        - 商品名称: 商品的名称
        - 规格: 商品的规格
        - 批次: 商品批次号
        - 生产日期: 商品生产日期
        - 保质期: 商品保质期
        - 仓库名称: 商品所在仓库的名称
        - 质检报告链接: 质检报告的完整URL链接
        - 报关证明链接: 报关证明的完整URL链接
        - 核酸检测链接: 核酸检测报告的完整URL链接
        - 消毒证明链接: 消毒证明的完整URL链接
        - 监管仓证明链接: 监管仓证明的完整URL链接
        - 农药残留报告图片链接: 农药残留报告图片的完整URL链接
    """
    limit = 10
    logger.info(f"查询商品证件报告工具被调用，商品名称: {product_name}, 仓库名称: {warehouse_name}, 地区名称: {area_name}, 生产日期: {production_date}, SKU编码: {sku_codes}, 限制条数: {limit}")
    
    if wrapper is None:
        logger.exception("用户上下文不存在")
        return SQLQueryResult(success=False, error="用户上下文不存在"), "查询失败"
    
    if not product_name and not sku_codes:
        logger.exception("商品名称和SKU编码至少需要提供一个")
        return SQLQueryResult(success=False, error="商品名称和SKU编码至少需要提供一个"), "查询失败"
    
    # 去掉仓库名称中的仓关键字
    if warehouse_name:
        warehouse_name = warehouse_name.replace('仓库', '')
        warehouse_name = warehouse_name.replace('仓', '')
    
    user_info = wrapper.context
    logger.info(f"用户上下文: {user_info}")
    
    # 如果提供了地区名称但没有仓库名称，先查询供货仓库
    target_warehouse_names = []
    if area_name and not warehouse_name:
        logger.info(f"通过地区 '{area_name}' 查询供货仓库")
        supply_result, _ = await query_product_supply_warehouse(
            wrapper=wrapper,
            product_name=product_name,
            area_name=area_name,
            sku_codes=sku_codes,
            upload_to_feishu=False
        )
        
        if supply_result.success and supply_result.data:
            # SQLQueryResult.data 是 List[List[Any]] 格式，需要结合 columns 来提取数据
            warehouse_name_index = supply_result.columns.index('仓库名称') if '仓库名称' in supply_result.columns else -1
            if warehouse_name_index >= 0:
                target_warehouse_names = [row[warehouse_name_index] for row in supply_result.data]
                logger.info(f"找到供货仓库: {target_warehouse_names}")
            else:
                logger.exception("查询结果中未找到'仓库名称'字段")
                return SQLQueryResult(success=False, error="查询结果中未找到'仓库名称'字段"), "查询失败"
        else:
            logger.warning(f"未找到地区 '{area_name}' 的供货仓库")
            return SQLQueryResult(success=False, error=f"未找到地区 '{area_name}' 的供货仓库"), "查询失败"
    elif warehouse_name:
        target_warehouse_names = [warehouse_name]
    # 如果既没有提供仓库名称也没有提供地区名称，则不进行仓库过滤，查询所有证件信息
    
    # 构建仓库过滤条件
    warehouse_filter = ""
    if target_warehouse_names:
        # 构建多个仓库的过滤条件
        warehouse_conditions = []
        for wh_name in target_warehouse_names:
            warehouse_conditions.append(f"wsc.warehouse_name LIKE '%{wh_name}%'")
        warehouse_filter = f"AND ({' OR '.join(warehouse_conditions)})"
    
    # 构建生产日期过滤条件
    date_filter = ""
    if production_date:
        date_filter = f"and (wbpr.production_date = '{production_date}' or wbpr.quality_date = '{production_date}')"
    
    # 构建SQL查询语句
    sql_query = f"""
    select distinct 
      wbpr.sku AS SKU,
      spu.pd_name as 商品名称,
      sku.weight as 规格,
      wbpr.batch AS 批次,
      wbpr.production_date as 生产日期,
      wbpr.quality_date as 保质期,
      COALESCE(wsc.warehouse_name, '未知仓库') AS 仓库名称,
      concat('', (CASE 
        when wbpr.quality_inspection_report like 'https://azure.summerfarm.net%' then wbpr.quality_inspection_report
        WHEN wbpr.quality_inspection_report IS NOT NULL AND wbpr.quality_inspection_report != '' 
            THEN CONCAT('https://azure.summerfarm.net/', REPLACE(wbpr.quality_inspection_report, ',', concat('\n',' . https://azure.summerfarm.net/')))
          ELSE NULL END)) AS 质检报告链接,
      concat('', (CASE 
        when wbpr.customs_declaration_certificate like 'https://azure.summerfarm.net%' then wbpr.customs_declaration_certificate
        WHEN wbpr.customs_declaration_certificate IS NOT NULL AND wbpr.customs_declaration_certificate != '' 
            THEN CONCAT('https://azure.summerfarm.net/', REPLACE(wbpr.customs_declaration_certificate, ',', concat('\n',' . https://azure.summerfarm.net/')))
          ELSE NULL END)) AS 报关证明链接,
      concat('', (CASE 
        when wbpr.nucleic_acid_detection like 'https://azure.summerfarm.net%' then wbpr.nucleic_acid_detection
        WHEN wbpr.nucleic_acid_detection IS NOT NULL AND wbpr.nucleic_acid_detection != '' 
            THEN CONCAT('https://azure.summerfarm.net/', REPLACE(wbpr.nucleic_acid_detection, ',', concat('\n',' . https://azure.summerfarm.net/')))
          ELSE NULL END)) AS 核酸检测链接,
      concat('', (CASE 
        when wbpr.disinfection_certificate like 'https://azure.summerfarm.net%' then wbpr.disinfection_certificate
        WHEN wbpr.disinfection_certificate IS NOT NULL AND wbpr.disinfection_certificate != '' 
            THEN CONCAT('https://azure.summerfarm.net/', REPLACE(wbpr.disinfection_certificate, ',', concat('\n',' . https://azure.summerfarm.net/')))
          ELSE NULL END)) AS 消毒证明链接,
      concat('', (CASE 
        when wbpr.supervision_warehouse_certificate like 'https://azure.summerfarm.net%' then wbpr.supervision_warehouse_certificate
        WHEN wbpr.supervision_warehouse_certificate IS NOT NULL AND wbpr.supervision_warehouse_certificate != '' 
            THEN CONCAT('https://azure.summerfarm.net/', REPLACE(wbpr.supervision_warehouse_certificate, ',', concat('\n',' . https://azure.summerfarm.net/')))
          ELSE NULL END)) AS 监管仓证明,
      concat('', (CASE 
        when wbpr.pesticide_residue_pictures like 'https://azure.summerfarm.net%' then wbpr.pesticide_residue_pictures
        WHEN wbpr.pesticide_residue_pictures IS NOT NULL AND wbpr.pesticide_residue_pictures != '' 
            THEN CONCAT('https://azure.summerfarm.net/', REPLACE(wbpr.pesticide_residue_pictures, ',', concat('\n',' . https://azure.summerfarm.net/')))
          ELSE NULL END)) AS 农药残留报告图片
    from 
      warehouse_batch_prove_record wbpr
      join `inventory` sku on sku.`sku` = wbpr.`sku` and sku.`tenant_id` = 1
      JOIN products spu on spu.`pd_id` = sku.`pd_id`
      inner JOIN stock_arrange sa on sa.purchase_no = wbpr.`batch`
      inner JOIN warehouse_storage_center wsc ON wsc.warehouse_no = sa.warehouse_no"""
    
    # 添加仓库过滤条件
    if warehouse_filter:
        sql_query += f" {warehouse_filter}"
    
    # 添加产品过滤条件 - sku_codes优先使用
    product_filter = ""
    if sku_codes:
        sku_list = "', '".join(sku_codes)
        product_filter += f" and sku.sku IN ('{sku_list}')"
    elif product_name:
        product_filter += f" and spu.pd_name like '%{product_name}%'"
    
    sql_query += f"""
    WHERE 1=1 and wbpr.batch like '20%' 
      {product_filter}
      {date_filter}
    ORDER BY wbpr.batch DESC
    LIMIT {limit}
    """
    
    description = "查询商品证件报告信息"
    if product_name:
        description = f"查询商品 '{product_name}' 的证件报告信息"
    if sku_codes:
        description += f"（SKU：{', '.join(sku_codes)}）"
    if area_name:
        description += f"（地区：{area_name}）"
    elif warehouse_name:
        description += f"（仓库：{warehouse_name}）"
    elif not area_name and not warehouse_name:
        description += "（全部仓库）"
    if production_date:
        description += f"（生产日期：{production_date}）"
    
    try:
        # 执行查询
        result = execute_business_query(sql_query)
        
        if result and result.success:
            logger.info(f"查询成功，返回{len(result.data) if result.data else 0}条记录")
            
            # 如果需要上传到飞书
            if upload_to_feishu:
                final_result = await upload_sql_result_to_feishu_if_needed(
                    sql_result=result,
                    sql_description=description,
                    user_info=user_info,
                    upload_to_feishu=upload_to_feishu,
                )
                return final_result, description
            
            return result, description
        else:
            error_msg = result.error if result else "查询失败"
            logger.exception(f"查询失败: {error_msg}")
            return SQLQueryResult(success=False, error=error_msg), description
            
    except Exception as e:
        error_msg = f"查询过程中发生错误: {str(e)}"
        logger.exception(error_msg)
        return SQLQueryResult(success=False, error=error_msg), description


async def query_product_warehouse_inventory(
    wrapper: RunContextWrapper[UserInfo],
    product_name: str = None,
    warehouse_name: str = None,
    area_name: str = None,
    sku_codes: List[str] = None,
    upload_to_feishu: bool = False,
) -> Tuple[SQLQueryResult, str]:
    """查询商品在指定仓库或地区的库存信息

    Args:
        wrapper: 包含用户信息的上下文包装器
        product_name: 商品名称，例如 "安佳淡奶油"，可选，和sku_codes二选一
        warehouse_name: 仓库名称，可选，例如 "嘉兴总仓"，与area_name二选一
        area_name: 地区名称，可选，例如 "佛山"，与warehouse_name二选一
        sku_codes: SKU编码列表，可选，用于精确匹配特定SKU，例如 ["SKU001", "SKU002"]，与product_name二选一，两者都有优先使用sku_codes参数
        upload_to_feishu: 是否需要将查询结果上传到飞书，默认为False

    Returns:
        Tuple[SQLQueryResult, str]: 查询结果和对查询的描述
        SQLQueryResult.data 包含以下字段：
        - 商品名称: 商品的名称
        - SKU: 商品的唯一标识编码
        - 规格: 商品的规格
        - 仓库名称: 仓库名称
        - 可售库存: 可售库存数量
        - 仓库库存: 仓库总库存数量（可选，仅按仓库查询时显示）
        - 锁定库存: 锁定库存数量（可选，仅按仓库查询时显示）
    """
    
    logger.info(f"查询商品仓库库存工具被调用，商品名称: {product_name}, 仓库名称: {warehouse_name}, 地区名称: {area_name}, SKU编码: {sku_codes}")
    
    if wrapper is None:
        logger.exception("用户上下文不存在")
        return SQLQueryResult(success=False, error="用户上下文不存在"), "查询失败"
    
    if not product_name and not sku_codes:
        logger.exception("商品名称和SKU编码至少需要提供一个")
        return SQLQueryResult(success=False, error="商品名称和SKU编码至少需要提供一个"), "查询失败"
    
    if not warehouse_name and not area_name:
        logger.exception("仓库名称和地区名称至少需要提供一个")
        return SQLQueryResult(success=False, error="仓库名称和地区名称至少需要提供一个"), "查询失败"
    
    # 去掉仓库名称中的仓关键字
    if warehouse_name:
        warehouse_name = warehouse_name.replace('仓库', '')
        warehouse_name = warehouse_name.replace('仓', '')
    
    user_info = wrapper.context
    logger.info(f"用户上下文: {user_info}")
    
    # 根据查询类型构建不同的SQL
    if warehouse_name:
        # 按仓库查询库存
        # 构建产品过滤条件 - sku_codes优先使用
        product_filter = ""
        if sku_codes:
            sku_list = "', '".join(sku_codes)
            product_filter += f" and i.sku IN ('{sku_list}')"
        elif product_name:
            product_filter += f" and p.pd_name like '%{product_name}%'"
        
        sql_query = f"""
        select 
          p.pd_name as '商品名称',
          i.sku as 'SKU',
          i.weight as '规格',
          wsc.warehouse_name as '仓库名称',
          ars.online_quantity as '可售库存',
          ars.quantity as '仓库库存',
          ars.lock_quantity as '锁定库存'
        from
          warehouse_storage_center wsc
          join area_store ars on wsc.warehouse_no = ars.area_no
          join inventory i on ars.sku = i.sku and i.outdated = 0
          join products p on i.pd_id = p.pd_id
        where
          wsc.warehouse_name like '%{warehouse_name}%'
          and wsc.status = 1
          and wsc.warehouse_name not like '%测试%'
          {product_filter}
        order by p.pd_name, i.sku
        """
        description = "查询商品在仓库的库存信息"
        if product_name:
            description = f"查询商品 '{product_name}' 在仓库 '{warehouse_name}' 的库存信息"
        elif sku_codes:
            description = f"查询SKU '{', '.join(sku_codes)}' 在仓库 '{warehouse_name}' 的库存信息"
        else:
            description = f"查询仓库 '{warehouse_name}' 的库存信息"
    else:
        # 按地区查询库存
        # 构建产品过滤条件 - sku_codes优先使用
        product_filter = ""
        if sku_codes:
            sku_list = "', '".join(sku_codes)
            product_filter += f" and i.sku IN ('{sku_list}')"
        elif product_name:
            product_filter += f" and p.pd_name like '%{product_name}%'"
        
        sql_query = f"""
        select DISTINCT
          p.pd_name as '商品名称',
          i.sku as 'SKU',
          i.weight as '规格',
          wsc.warehouse_name as '仓库名称',
          ars.online_quantity as '可售库存'
        from
          area a
          join fence f on a.area_no = f.area_no and f.status = 0
          join warehouse_inventory_mapping wim on f.store_no = wim.store_no
          join warehouse_storage_center wsc on wim.warehouse_no = wsc.warehouse_no and wsc.status = 1
          join area_store ars on wsc.warehouse_no = ars.area_no and ars.sku = wim.sku
          join inventory i on ars.sku = i.sku and i.outdated = 0
          join products p on i.pd_id = p.pd_id
        where
          a.area_name like '%{area_name}%'
          {product_filter}
        order by p.pd_name, wsc.warehouse_name, i.sku
        """
        description = "查询商品在地区的库存信息"
        if product_name:
            description = f"查询商品 '{product_name}' 在地区 '{area_name}' 的库存信息"
        elif sku_codes:
            description = f"查询SKU '{', '.join(sku_codes)}' 在地区 '{area_name}' 的库存信息"
        else:
            description = f"查询地区 '{area_name}' 的库存信息"
    
    try:
        # 执行查询
        result = execute_business_query(sql_query)
        
        if result and result.success:
            logger.info(f"查询成功，返回{len(result.data) if result.data else 0}条记录")
            
            # 如果需要上传到飞书
            if upload_to_feishu:
                final_result = await upload_sql_result_to_feishu_if_needed(
                    sql_result=result,
                    sql_description=description,
                    user_info=user_info,
                    upload_to_feishu=upload_to_feishu,
                )
                return final_result, description
            
            return result, description
        else:
            error_msg = result.error if result else "查询失败"
            logger.exception(f"查询失败: {error_msg}")
            return SQLQueryResult(success=False, error=error_msg), description
            
    except Exception as e:
        error_msg = f"查询过程中发生错误: {str(e)}"
        logger.exception(error_msg)
        return SQLQueryResult(success=False, error=error_msg), description


async def query_warehouse_supply_areas(
    wrapper: RunContextWrapper[UserInfo],
    warehouse_name: str,
    product_name: str = None,
    sku_codes: List[str] = None,
    is_fruit: bool = None,
    upload_to_feishu: bool = False,
) -> Tuple[SQLQueryResult, str]:
    """查询指定仓库的可供货地区(城市)信息

    Args:
        wrapper: 包含用户信息的上下文包装器
        warehouse_name: 仓库名称，例如 "上海仓"
        product_name: 商品名称，例如 "埃及红西柚"，可选，与sku_codes二选一
        sku_codes: SKU编码列表，可选，用于精确匹配特定SKU，例如 ["SKU001", "SKU002"]，与product_name二选一，两者都有优先使用sku_codes参数
        is_fruit: 是否是水果(鲜果)，可选，如果为True则只查询水果类商品
        upload_to_feishu: 是否需要将查询结果上传到飞书，默认为False

    Returns:
        Tuple[SQLQueryResult, str]: 查询结果和对查询的描述
        SQLQueryResult.data 包含以下字段：
        - 围栏名称: 围栏的名称
        - 围栏类型: 围栏类型
        - 支持下单渠道类型: 支持的下单渠道类型（鲜沐平台客户/鲜沐大客户/Saas客户）
        - 配送周期方案: 配送周期计算方案（周计算/间隔计算）
        - 配送周期: 具体的配送周期描述
        - 首配日: 下次配送日期
        - 省: 省份名称
        - 市: 城市名称
        - 区: 区县名称
        
        当upload_to_feishu=True且上传成功时，SQLQueryResult.message字段会包含飞书文档链接，
        用户可通过该链接查看完整的查询结果数据。
    """
    
    logger.info(f"查询仓库供货地区工具被调用，仓库名称: {warehouse_name}, SKU编码列表: {sku_codes}")
    
    if wrapper is None:
        logger.exception("用户上下文不存在")
        return SQLQueryResult(success=False, error="用户上下文不存在"), "查询失败"
    
    if not warehouse_name:
        logger.exception("仓库名称不能为空")
        return SQLQueryResult(success=False, error="仓库名称不能为空"), "查询失败"
    
    if not product_name and not sku_codes:
        logger.error("商品名称和SKU编码列表不能同时为空")
        return SQLQueryResult(success=False, error="商品名称和SKU编码列表不能同时为空"), "查询失败"
    
    user_info = wrapper.context
    logger.info(f"用户上下文: {user_info}")
    
    # 构建SQL查询语句
    sql_query = f"""
    select 
      distinct 
      f.fence_name as '围栏名称',
      if(wlc.fulfillment_type = 1, '快递围栏', '城配围栏') as '围栏类型',
      replace(replace(replace(f.order_channel_type, 1000, '鲜沐平台客户'), 2000, '鲜沐大客户'), 3000, 'Saas客户') as '支持下单渠道类型',
      replace(replace(fd.frequent_method, 1, '周计算'), 2, '间隔计算') as '配送周期方案',
      if(fd.frequent_method = 1, if(fd.delivery_frequent = 0, '每天', concat('', fd.delivery_frequent)), concat('隔', fd.delivery_frequent_interval, '天')) as '配送周期',
      fd.next_delivery_date as '首配日',
      acm.province as '省',
      acm.city as '市',
      acm.area as '区',
      wlc.store_name as '城配仓名称'
    from warehouse_storage_center wsc
    inner join warehouse_inventory_mapping wim on wsc.warehouse_no = wim.warehouse_no
    inner join fence f on wim.store_no = f.store_no and f.status = 0
    inner join warehouse_logistics_center wlc on wlc.store_no = wim.store_no and wlc.status = 1
    inner join area a on a.`area_no` = f.`area_no` and a.status = 1
    inner join area_sku asku on asku.`area_no`  = a.`area_no` and asku.`sku`  = wim.sku and asku.`on_sale` = 1
    inner join fence_delivery fd on fd.fence_id = f.id and fd.delete_flag = 0
    inner join ad_code_msg acm on f.id = acm.fence_id and acm.status = 0"""
    
    # 如果提供了商品信息，需要关联商品表进行过滤
    if product_name or sku_codes or is_fruit:
        sql_query += """
    inner join inventory sku on sku.sku = wim.sku and sku.outdated = 0
    inner join products spu on spu.pd_id = sku.pd_id"""
        
        # 如果需要查询水果类商品，关联category表
        if is_fruit:
            sql_query += """
    inner join category c on spu.category_id = c.id and c.type = 4"""
    
    sql_query += f" where f.fence_name not like '%测试%' and wsc.warehouse_name like '%{warehouse_name}%' and wsc.status = 1"
    
    # 添加商品过滤条件 - sku_codes优先使用
    if sku_codes:
        sku_list = "', '".join(sku_codes)
        sql_query += f" and sku.sku in ('{sku_list}')"
    elif product_name:
        sql_query += f" and spu.pd_name like '%{product_name}%'"
    
    sql_query += " order by acm.province, acm.city"
    
    try:
        # 执行查询
        result = execute_business_query(sql_query)
        
        if result.success:
            logger.info(f"查询成功，返回 {len(result.data)} 条记录")
            
            # 构建描述信息
            description = f"查询仓库 '{warehouse_name}' 的可供货地区信息"
            if product_name:
                description = f"查询仓库 '{warehouse_name}' 中商品 '{product_name}' 的可供货地区信息"
            if sku_codes:
                description += f"（SKU：{', '.join(sku_codes)}）"
            if is_fruit:
                description += "（仅水果类商品）"
            
            # 如果需要上传到飞书
            if upload_to_feishu:
                result = await upload_sql_result_to_feishu_if_needed(
                    result, description, wrapper.context, upload_to_feishu
                )
            
            return result, description
        else:
            logger.exception(f"查询失败: {result.error}")
            return result, "查询失败"
            
    except Exception as e:
        logger.exception(f"查询仓库供货地区时发生异常: {str(e)}")
        return SQLQueryResult(success=False, error=str(e)), "查询异常"


tool_manager.register_as_function_tool(query_order_item_trace_info)
tool_manager.register_as_function_tool(query_warehouse_in_transit_inventory)
tool_manager.register_as_function_tool(query_product_supply_warehouse)
tool_manager.register_as_function_tool(query_product_certificate_reports)
tool_manager.register_as_function_tool(query_warehouse_supply_areas)
# tool_manager.register_as_function_tool(query_product_warehouse_inventory)