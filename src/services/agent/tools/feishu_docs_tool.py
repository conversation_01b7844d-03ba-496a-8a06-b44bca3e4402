"""
飞书文档搜索相关工具。
"""

import os
import uuid
import aiohttp
import asyncio
from typing import List, Dict, Any, Optional, Tuple
from agents import RunContextWrapper
from src.models.user_info_class import UserInfo
from src.utils.logger import logger
from src.utils.retry_decorator import retryable
from src.services.agent.tools.tool_manager import tool_manager
from src.services.agent.tools.feishu_group_chat_content_tool import (
    search_feishu_group_chat_content,
)
from src.services.agent.tools.feishu_docx_content_tool import FeishuDocxContentParser

FILE_CONTENT_LIMIT = int(os.getenv("FILE_CONTENT_LIMIT", 2000))

# 并发控制：最多3个并发请求
CONCURRENT_LIMIT = 3

# 飞书文档类型表：https://open.feishu.cn/document/server-docs/docs/wiki-v2/search_wiki
FEISHU_DOC_TYPE_SET = set([1, 5, 6, 7, 8])  # 取doc/docx/wiki/slide/file


def _truncate_empty_rows(values: List[List[Any]]) -> List[List[Any]]:
    """
    智能截断电子表格数据：当连续出现3行全null数据时停止处理
    
    Args:
        values: 原始电子表格数据行列表
        
    Returns:
        List[List[Any]]: 截断后的数据行列表
        
    示例:
        输入: [["数据1"], ["数据2"], [null, null], [null, null], [null, null], [null, null]]
        输出: [["数据1"], ["数据2"]]  # 在连续3行全null后截断
    """
    if not values:
        return values
    
    consecutive_empty_count = 0
    result = []
    
    for i, row in enumerate(values):
        # 检查当前行是否为全null或全空
        is_empty_row = all(cell is None or (isinstance(cell, str) and not cell.strip()) for cell in row)
        
        if is_empty_row:
            consecutive_empty_count += 1
            # 如果连续3行都是空行，则截断（回退到连续空行开始前的位置）
            if consecutive_empty_count >= 3:
                logger.debug(f"检测到连续{consecutive_empty_count}行空数据，执行智能截断，保留前{len(result) - consecutive_empty_count + 1}行")
                # 移除已添加的连续空行，只保留截断点之前的数据
                result = result[:-consecutive_empty_count + 1]
                break
        else:
            # 遇到非空行，重置计数器
            consecutive_empty_count = 0
        
        # 添加当前行到结果中
        result.append(row)
    
    return result


def _generate_search_keywords(query: str) -> List[str]:
    """根据空格分词，生成多个关键词组合用于并行搜索。

    Args:
        query: 原始查询字符串

    Returns:
        List[str]: 生成的关键词列表，按搜索priority排序

    示例:
        "收入证明 开具流程 申请" -> ["收入证明", "收入证明 开具流程", "收入证明 开具流程 申请"]
        "如何使用系统" -> ["如何使用系统"]  # 无空格，直接返回
    """
    if not query.strip():
        return [query]

    # 如果查询不包含空格，直接返回原查询
    if " " not in query.strip():
        return [query.strip()]

    # 按空格分割，保留空格和原始格式
    terms = query.split()
    terms = [term.strip() for term in terms if term.strip()]

    # 如果只有一个词，返回原查询
    if len(terms) <= 1:
        return [query.strip()]

    # 生成逐步增加的关键词组合，保留空格
    keywords = []
    for i in range(1, len(terms) + 1):
        keyword = " ".join(terms[:i])
        if keyword and keyword not in keywords:
            keywords.append(keyword)

    # 限制最多生成5个关键词，避免过多搜索
    return keywords[:5]


async def search_feishu_docs(
    wrapper: RunContextWrapper[UserInfo],
    query: str,
    page_size: int = 10,
    get_content: bool = True,
) -> Tuple[List[Dict[str, Any]], str]:
    """搜索飞书文档并获取内容，同时并行搜索群聊消息。

    该工具用于在飞书知识库中搜索相关文档，可以帮助AI获取公司内部的业务知识和规则定义。
    现在会同时搜索飞书文档和群聊消息，为AI提供更丰富的知识来源。

    Args:
        wrapper: 包含用户信息的上下文包装器。
        query: 搜索关键词，建议使用具体的业务术语或问题描述。
        page_size: 返回结果数量，默认为10，最大不超过50。
        get_content: 是否获取文档详细内容，默认为True。如果为True，会进一步调用详情接口获取每个文档的具体内容。您可以根据需要来决定是否调用此工具。

    Returns:
        Tuple[List[Dict[str, Any]], str]: 搜索结果列表和描述信息。
        搜索结果包含：title（标题）、url（链接）、obj_token（文档标识）、content（内容，仅当get_content=True时）、source（数据来源：'document'或群聊名称）
    """
    user_info = wrapper.context
    access_token = user_info.access_token

    if not access_token:
        error_msg = "用户未提供飞书访问令牌"
        logger.warning(error_msg)
        return [], f"搜索飞书文档失败: {error_msg}"

    search_id = str(uuid.uuid4())
    logger.info(
        f"用户开始搜索飞书文档和群聊，搜索ID: {search_id}, 关键词: {query}, access_token: {access_token[:20]}..."
    )

    # 生成多个关键词进行并行搜索
    search_keywords = _generate_search_keywords(query)
    logger.info(
        f"原始查询: {query}, 拆分为 {len(search_keywords)} 个关键词: {search_keywords}"
    )

    # 并行调用所有关键词的文档搜索和群聊搜索
    try:
        # 创建所有并行任务
        all_tasks = []

        # 为每个关键词创建文档搜索任务
        for keyword in search_keywords:
            doc_task = _search_documents_only(wrapper, keyword, page_size, get_content)
            all_tasks.append(doc_task)

        # 为每个关键词创建群聊搜索任务
        for keyword in search_keywords:
            chat_task = _search_group_chats_safe(wrapper, keyword)
            all_tasks.append(chat_task)

        # 并行执行所有搜索任务
        all_results = await asyncio.gather(*all_tasks, return_exceptions=True)

        # 分离文档搜索和群聊搜索结果
        num_keywords = len(search_keywords)
        doc_results_list = all_results[:num_keywords]
        chat_results_list = all_results[num_keywords:]

        # 合并所有文档搜索结果
        doc_items = []
        for result in doc_results_list:
            if isinstance(result, Exception):
                logger.exception(f"某个文档搜索任务失败: {result}")
                continue
            else:
                items, _ = result
                # 为文档结果添加来源标识
                for item in items:
                    item["source"] = "document"
                doc_items.extend(items)

        # 合并所有群聊搜索结果
        chat_items = []
        for result in chat_results_list:
            if isinstance(result, Exception):
                logger.exception(f"某个群聊搜索任务失败: {result}")
                continue
            else:
                items, _ = result
                # 为群聊结果添加来源标识和统一格式
                for item in items:
                    # 使用群聊名称作为数据来源标识
                    chat_name = item.get("chat_name", "未知群聊")
                    item["source"] = chat_name
                    # 统一字段格式，将群聊消息格式转换为类似文档的格式
                    if "content" in item and "title" not in item:
                        # 从消息内容中提取前50个字符作为标题，并包含群聊名称
                        content_preview = (
                            item["content"][:50] + "..."
                            if len(item.get("content", "")) > 50
                            else item.get("content", "")
                        )
                        item["title"] = f"[{chat_name}] {content_preview}"
                    if "message_id" in item and "obj_token" not in item:
                        item["obj_token"] = item[
                            "message_id"
                        ]  # 使用message_id作为标识符
                    if "url" not in item:
                        item["url"] = f"飞书群聊消息 (群聊: {chat_name})"
                chat_items.extend(items)

        # 去重合并：使用obj_token作为唯一标识符去重
        seen_tokens = set()
        unique_doc_items = []
        for item in doc_items:
            token = item.get("obj_token")
            if token and token not in seen_tokens:
                seen_tokens.add(token)
                unique_doc_items.append(item)

        unique_chat_items = []
        for item in chat_items:
            token = item.get("obj_token")
            if token and token not in seen_tokens:
                seen_tokens.add(token)
                unique_chat_items.append(item)

        # 合并最终结果，限制总数量
        all_items = unique_doc_items + unique_chat_items
        # 按相关性排序（飞书搜索已排序）并限制总数
        all_items = all_items[: page_size * 2]  # 允许稍微多一些，因为合并了多个搜索

        # 生成综合描述信息
        total_docs = len(unique_doc_items)
        total_chats = len(unique_chat_items)
        total_items = len(all_items)

        if total_items == 0:
            return (
                [],
                f"使用 {len(search_keywords)} 个关键词组合['{'|'.join(search_keywords)}']，未找到与'{query}' 相关的文档或群聊消息",
            )

        description_parts = []
        if total_docs > 0:
            description_parts.append(f"{total_docs} 个文档")
        if total_chats > 0:
            description_parts.append(f"{total_chats} 条群聊消息")

        keyword_desc = (
            f"使用关键词组合{'、'.join([f'『{k}』' for k in search_keywords])}"
        )
        description = f"{keyword_desc}，成功搜索到 {' 和 '.join(description_parts)}，共 {total_items} 条结果"
        if get_content:
            description += "并获取了内容"

        logger.info(f"多关键词并行搜索完成: {description}")
        return all_items, description

    except Exception as e:
        error_msg = f"搜索过程中发生异常: {str(e)}"
        logger.exception(error_msg)
        return [], f"搜索失败: {error_msg}"


async def _search_documents_only(
    wrapper: RunContextWrapper[UserInfo],
    query: str,
    page_size: int = 10,
    get_content: bool = True,
) -> Tuple[List[Dict[str, Any]], str]:
    """仅搜索飞书文档的内部函数"""
    user_info = wrapper.context
    access_token = user_info.access_token

    try:
        # 调用搜索接口
        search_url = f"https://open.feishu.cn/open-apis/wiki/v1/nodes/search?page_size={page_size}"
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {access_token}",
        }
        search_data = {"query": query}

        # 设置超时时间为30秒，避免长时间等待
        timeout = aiohttp.ClientTimeout(total=30)
        async with aiohttp.ClientSession(timeout=timeout) as session:
            async with session.post(
                search_url, headers=headers, json=search_data
            ) as response:
                if response.status != 200:
                    error_msg = f"搜索接口调用失败，状态码: {response.status}, 响应内容: {await response.text()}"
                    logger.exception(error_msg)
                    return [], f"搜索飞书文档失败: {error_msg}"

                search_result = await response.json()

                if search_result.get("code") != 0:
                    error_msg = (
                        f"搜索接口返回错误: {search_result.get('msg', '未知错误')}"
                    )
                    logger.exception(error_msg)
                    return [], f"搜索飞书文档失败: {error_msg}"

                items = search_result.get("data", {}).get("items", [])
                filtered_items = []
                for item in items:
                    logger.info(
                        f"搜索到文档: {item['title']}, 链接: {item['url']}, obj_type: {item['obj_type']}"
                    )
                    if item["obj_type"] in FEISHU_DOC_TYPE_SET:
                        filtered_items.append(item)
                    else:
                        logger.info(
                            f"跳过文档: {item['title']}, 链接: {item['url']}, obj_type: {item['obj_type']}"
                        )
                items = filtered_items

                if not items:
                    return [], f"未找到与关键词 '{query}' 相关的文档"

                # 如果不需要获取内容，直接返回搜索结果
                if not get_content:
                    return items, f"成功搜索到 {len(items)} 个相关文档"

                # 并发获取每个文档的详细内容
                enriched_items = await _get_documents_content_concurrently(
                    access_token, items, get_all=False
                )

                return (
                    enriched_items,
                    f"成功搜索到 {len(enriched_items)} 个相关文档并获取了内容",
                )

    except Exception as e:
        error_msg = f"搜索飞书文档时发生异常: {str(e)}"
        logger.exception(error_msg)
        return [], f"搜索飞书文档失败: {error_msg}"


async def _search_group_chats_safe(
    wrapper: RunContextWrapper[UserInfo],
    query: str,
    page_size: int = 30,
    get_content: bool = True,
) -> Tuple[List[Dict[str, Any]], str]:
    """安全地搜索群聊消息的包装函数，确保错误不会影响文档搜索"""
    try:
        logger.info(f"开始并行搜索群聊消息，关键词: {query}")
        return await search_feishu_group_chat_content(
            wrapper, query, page_size, get_content
        )
    except Exception as e:
        logger.warning(f"群聊搜索出现异常，但不影响文档搜索: {str(e)}")
        return [], f"群聊搜索失败: {str(e)}"


async def get_feishu_doc_content_tool(
    wrapper: RunContextWrapper[UserInfo], obj_token: str
) -> Tuple[Optional[str], str]:
    """获取指定飞书文档的纯文本内容。

    Args:
        wrapper: 包含用户信息的上下文包装器。
        obj_token: 文档的obj_token标识符。

    Returns:
        Tuple[Optional[str], str]: 文档内容和描述信息。
    """
    user_info = wrapper.context
    access_token = user_info.access_token

    if not access_token:
        error_msg = "用户未提供飞书访问令牌"
        logger.warning(error_msg)
        return None, f"获取文档内容失败: {error_msg}"

    logger.info(f"开始获取飞书文档内容，obj_token: {obj_token}")

    content = await _get_feishu_doc_content(access_token, obj_token, get_all=True)

    if content:
        return content, f"成功获取文档内容，长度: {len(content)} 字符"
    else:
        return None, f"获取文档内容失败，obj_token: {obj_token}"


async def _get_documents_content_concurrently(
    access_token: str, items: List[Dict[str, Any]], get_all: bool = False
) -> List[Dict[str, Any]]:
    """并发获取多个文档的内容。

    现在使用新的get_feishu_document_blocks_with_content()函数来获取完整的文档内容，
    包括嵌入的电子表格数据，并解析为统一的Markdown格式。

    Args:
        access_token: 飞书访问令牌。
        items: 文档列表，每个文档包含obj_token。
        get_all: 是否获取全部内容，默认为False。

    Returns:
        List[Dict[str, Any]]: 包含内容的文档列表。
    """
    # 创建信号量来控制并发数量
    semaphore = asyncio.Semaphore(CONCURRENT_LIMIT)

    async def get_single_doc_content(item: Dict[str, Any]) -> Dict[str, Any]:
        """获取单个文档内容的包装函数"""
        async with semaphore:
            obj_token = item.get("obj_token")
            if obj_token:
                try:
                    # 优先使用新的完整内容获取方法（包含电子表格）
                    content, description = (
                        await get_feishu_document_blocks_with_content(
                            access_token, obj_token
                        )
                    )

                    if content:
                        # 如果不需要获取全部内容，进行截断处理
                        if not get_all and len(content) > FILE_CONTENT_LIMIT:
                            original_length = len(content)
                            content = (
                                content[:FILE_CONTENT_LIMIT]
                                + f"\n\n(内容已被截断, 总长度{original_length}, 截断到{FILE_CONTENT_LIMIT}，如果有需要，请使用get_feishu_doc_content_tool工具获取全部内容)"
                            )

                        item["content"] = content
                        logger.info(
                            f"成功获取文档完整内容，obj_token: {obj_token}, {description}"
                        )
                    else:
                        # 如果新方法失败，回退到旧的方法
                        logger.warning(
                            f"新方法获取文档内容失败，回退到旧方法，obj_token: {obj_token}"
                        )
                        content = await _get_feishu_doc_content(
                            access_token, obj_token, get_all
                        )
                        item["content"] = content

                except Exception as e:
                    logger.warning(
                        f"使用新方法获取文档内容时发生异常，回退到旧方法，obj_token: {obj_token}, 异常: {str(e)}"
                    )
                    # 回退到旧的获取方法
                    content = await _get_feishu_doc_content(
                        access_token, obj_token, get_all
                    )
                    item["content"] = content
            else:
                item["content"] = None
            return item

    # 创建并发任务
    tasks = [get_single_doc_content(item.copy()) for item in items]

    # 并发执行所有任务
    logger.info(
        f"开始并发获取 {len(items)} 个文档的完整内容（包含电子表格），并发数: {CONCURRENT_LIMIT}"
    )
    enriched_items = await asyncio.gather(*tasks, return_exceptions=True)

    # 处理异常结果
    result_items = []
    for i, result in enumerate(enriched_items):
        if isinstance(result, Exception):
            logger.exception(f"获取第 {i+1} 个文档内容时发生异常: {result}")
            # 保留原始项目，但标记内容获取失败
            original_item = items[i].copy()
            original_item["content"] = f"获取内容失败: {str(result)}"
            result_items.append(original_item)
        else:
            result_items.append(result)

    successful_count = len(
        [
            r
            for r in result_items
            if r.get("content") and not r["content"].startswith("获取内容失败")
        ]
    )
    logger.info(f"并发获取文档完整内容完成，成功: {successful_count}/{len(items)}")
    return result_items


@retryable(max_times=3, min_wait_seconds=1.0, max_wait_seconds=2.0)
async def _get_feishu_doc_content(
    access_token: str, obj_token: str, get_all: bool = False
) -> Optional[str]:
    """内部函数：获取飞书文档的纯文本内容。

    Args:
        access_token: 飞书访问令牌。
        obj_token: 文档的obj_token标识符。
        get_all: 是否获取全部内容，默认为False。

    Returns:
        Optional[str]: 文档的纯文本内容，获取失败时返回None。
    """
    logger.debug(
        f"正在获取飞书文档markdown内容，obj_token: {obj_token}, get_all: {get_all}"
    )
    content_url = f"https://open.feishu.cn/open-apis/docs/v1/content?content_type=markdown&doc_token={obj_token}&doc_type=docx"
    headers = {"Authorization": f"Bearer {access_token}"}

    # 设置超时时间为30秒，避免长时间等待
    timeout = aiohttp.ClientTimeout(total=30)
    async with aiohttp.ClientSession(timeout=timeout) as session:
        async with session.get(content_url, headers=headers) as response:
            response_text = await response.text()

            if response.status != 200:
                # 检查是否是限流错误，如果是则抛出异常让装饰器处理重试
                if "request trigger frequency limit" in response_text.lower():
                    raise Exception(f"request trigger frequency limit: {response_text}")
                else:
                    logger.warning(
                        f"获取文档内容失败，状态码: {response.status}, obj_token: {obj_token}, 响应内容: {response_text}"
                    )
                    return None

            try:
                content_result = await response.json()
            except Exception as json_error:
                logger.error(
                    f"解析响应JSON失败: {json_error}, obj_token: {obj_token}, 响应内容: {response_text}"
                )
                return None

            if content_result.get("code") != 0:
                logger.warning(
                    f"获取文档内容接口返回错误: {content_result.get('msg', '未知错误')}, obj_token: {obj_token}"
                )
                return None

            content = content_result.get("data", {}).get("content", "").strip()
            logger.debug(
                f"获取到文档内容，obj_token: {obj_token}, 内容长度: {len(content)} 字符"
            )

            if get_all:
                return content

            if len(content) > FILE_CONTENT_LIMIT:
                original_length = len(content)
                content = (
                    content[:FILE_CONTENT_LIMIT]
                    + f"\n\n(内容已被截断, 总长度{original_length}, 截断到{FILE_CONTENT_LIMIT}，如果有需要，请使用get_feishu_doc_content_tool工具获取全部内容)"
                )

            return content


@retryable(max_times=3, min_wait_seconds=1.0, max_wait_seconds=2.0)
async def get_feishu_spreadsheet_data(
    access_token: str,
    node_id: str,
    sheet_id: str = "0Liffk",
    data_range: str = "A1:H100",
) -> Tuple[Optional[Dict[str, Any]], str]:
    """获取飞书电子表格的数据内容。

    Args:
        access_token: 飞书访问令牌
        node_id: 电子表格的节点ID（如：UR3nse8vlhCkibtTHi9cL3EmnYe）
        sheet_id: 工作表ID，默认为 "0Liffk"
        data_range: 数据范围，默认为 "A1:H100"，格式与Excel相同

    Returns:
        Tuple[Optional[Dict[str, Any]], str]: 电子表格数据和描述信息

    示例:
        data, msg = await get_feishu_spreadsheet_data(
            access_token,
            "UR3nse8vlhCkibtTHi9cL3EmnYe",
            "0Liffk",
            "A1:I200"
        )
    """
    if not access_token:
        error_msg = "访问令牌不能为空"
        logger.warning(error_msg)
        return None, f"获取电子表格数据失败: {error_msg}"

    if not node_id:
        error_msg = "电子表格节点ID不能为空"
        logger.warning(error_msg)
        return None, f"获取电子表格数据失败: {error_msg}"

    # 构建API URL
    api_url = f"https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/{node_id}/values/{sheet_id}!{data_range}"

    # 添加查询参数
    params = {
        "valueRenderOption": "ToString",
        "dateTimeRenderOption": "FormattedString",
    }

    headers = {"Authorization": f"Bearer {access_token}"}

    logger.info(
        f"开始获取飞书电子表格数据，node_id: {node_id}, sheet_id: {sheet_id}, range: {data_range}"
    )

    # 设置超时时间为30秒
    timeout = aiohttp.ClientTimeout(total=30)
    async with aiohttp.ClientSession(timeout=timeout) as session:
        async with session.get(
            api_url, headers=headers, params=params
        ) as response:
            response_text = await response.text()

            if response.status != 200:
                # 检查是否是限流错误，如果是则抛出异常让装饰器处理重试
                if "request trigger frequency limit" in response_text.lower():
                    raise Exception(f"request trigger frequency limit: {response_text}")
                else:
                    error_msg = f"获取电子表格数据失败，状态码: {response.status}, 响应内容: {response_text}"
                    logger.warning(error_msg)
                    return None, error_msg

            try:
                result_data = await response.json()
            except Exception as json_error:
                logger.error(
                    f"解析响应JSON失败: {json_error}, node_id: {node_id}, 响应内容: {response_text}"
                )
                return None, f"解析响应数据失败: {str(json_error)}"

            if result_data.get("code") != 0:
                error_msg = f"获取电子表格数据接口返回错误: {result_data.get('msg', '未知错误')}"
                logger.warning(f"{error_msg}, node_id: {node_id}")
                return None, error_msg

            # 提取数据
            data = result_data.get("data", {})
            # 修复bug：数据在 valueRange.values 中，不是直接在 data.values 中
            value_range = data.get("valueRange", {})
            raw_values = value_range.get("values", [])

            # 智能截断：连续3行全null数据时停止处理
            values = _truncate_empty_rows(raw_values)
            
            # 统计信息
            original_row_count = len(raw_values)
            row_count = len(values)
            col_count = max(len(row) for row in values) if values else 0
            
            # 如果进行了截断，记录日志
            if row_count < original_row_count:
                truncated_count = original_row_count - row_count
                logger.info(
                    f"智能截断：原始{original_row_count}行，截断{truncated_count}行无效数据，保留{row_count}行有效数据，node_id: {node_id}"
                )

            logger.info(
                f"成功获取电子表格数据，node_id: {node_id}, 行数: {row_count}, 列数: {col_count}"
            )

            success_msg = f"成功获取电子表格数据，范围: {sheet_id}!{data_range}，共 {row_count} 行 {col_count} 列"
            return values, success_msg


async def get_feishu_document_blocks_with_content(
    access_token: str,
    document_token: str,
    document_revision_id: str = "-1",
    page_size: int = 500,
) -> Tuple[Optional[str], str]:
    """获取飞书文档的所有块内容，并解析为完整的Markdown格式。

    该函数会：
    1. 获取文档的所有块结构
    2. 使用FeishuDocxContentParser解析为Markdown
    3. 提取电子表格token列表
    4. 批量获取电子表格数据
    5. 替换Markdown中的电子表格占位符为实际内容

    Args:
        access_token: 飞书访问令牌
        document_token: 飞书文档的document token（如：GSWQdj40eogM4ixA84Nc2DbCnbg）
        document_revision_id: 文档版本ID，默认为"-1"表示最新版本
        page_size: 每页获取的块数量，默认500

    Returns:
        Tuple[Optional[str], str]: 完整的Markdown内容和描述信息

    示例:
        content, msg = await get_feishu_document_blocks_with_content(
            access_token,
            "GSWQdj40eogM4ixA84Nc2DbCnbg"
        )
    """
    if not access_token:
        error_msg = "访问令牌不能为空"
        logger.warning(error_msg)
        return None, f"获取文档块内容失败: {error_msg}"

    if not document_token:
        error_msg = "文档token不能为空"
        logger.warning(error_msg)
        return None, f"获取文档块内容失败: {error_msg}"

    logger.info(f"开始获取飞书文档所有块内容，document_token: {document_token}")

    try:
        # 第一步：获取文档的所有块
        blocks_data = await _get_feishu_document_blocks(
            access_token, document_token, document_revision_id, page_size
        )

        if not blocks_data:
            return None, "获取文档块数据失败"

        # 第二步：使用解析器解析为Markdown并提取电子表格token
        parser = FeishuDocxContentParser()
        markdown_content, sheet_tokens = parser.parse_feishu_docx_content(blocks_data)

        if not markdown_content:
            return None, "解析文档内容为Markdown失败"

        logger.info(
            f"成功解析文档，Markdown长度: {len(markdown_content)}, 电子表格数量: {len(sheet_tokens)}"
        )

        # 第三步：如果有电子表格，批量获取电子表格内容并替换占位符
        if sheet_tokens:
            logger.info(f"开始批量获取 {len(sheet_tokens)} 个电子表格的内容")

            # 并发获取所有电子表格数据
            sheet_data_tasks = []
            for sheet_token in sheet_tokens:
                logger.info(f"开始获取电子表格数据，sheet_token: {sheet_token}")
                tokens = sheet_token.split("_")
                spreadsheet_id = tokens[0]
                sheet_id = tokens[1]
                task = get_feishu_spreadsheet_data(
                    access_token, node_id=spreadsheet_id, sheet_id=sheet_id
                )
                sheet_data_tasks.append(task)

            # 执行所有电子表格获取任务
            sheet_results = await asyncio.gather(
                *sheet_data_tasks, return_exceptions=True
            )

            # 替换Markdown中的电子表格占位符
            for i, (sheet_token, result) in enumerate(zip(sheet_tokens, sheet_results)):
                placeholder = f"[电子表格: {sheet_token}]"

                if isinstance(result, Exception):
                    logger.warning(f"获取电子表格 {sheet_token} 失败: {result}")
                    replacement = (
                        f"\n**电子表格 {sheet_token} (获取失败: {str(result)})**\n"
                    )
                else:
                    sheet_data, sheet_msg = result
                    if sheet_data:
                        replacement = _format_spreadsheet_as_markdown(
                            sheet_data, sheet_token
                        )
                        logger.info(f"成功获取并格式化电子表格 {sheet_token}")
                    else:
                        logger.warning(
                            f"电子表格 {sheet_token} 数据为空或获取失败: {sheet_msg}"
                        )
                        replacement = f"\n**电子表格 {sheet_token} (数据为空)**\n"

                # 替换占位符
                markdown_content = markdown_content.replace(placeholder, replacement)

        # 生成最终描述信息
        content_length = len(markdown_content)
        description_parts = [f"Markdown内容长度: {content_length} 字符"]

        if sheet_tokens:
            successful_sheets = len(
                [r for r in sheet_results if not isinstance(r, Exception) and r[0]]
            )
            description_parts.append(
                f"成功获取 {successful_sheets}/{len(sheet_tokens)} 个电子表格内容"
            )

        description = f"成功获取并解析飞书文档完整内容，{', '.join(description_parts)}"

        logger.info(f"文档内容获取完成: {description}")
        return markdown_content, description

    except Exception as e:
        error_msg = f"获取文档块内容时发生异常: {str(e)}"
        logger.exception(error_msg)
        return None, error_msg


@retryable(max_times=3, min_wait_seconds=1.0, max_wait_seconds=2.0)
async def _get_feishu_document_blocks(
    access_token: str,
    document_token: str,
    document_revision_id: str = "-1",
    page_size: int = 500,
) -> Optional[Dict[str, Any]]:
    """内部函数：获取飞书文档的所有块数据。

    Args:
        access_token: 飞书访问令牌
        document_token: 文档token
        document_revision_id: 文档版本ID
        page_size: 每页块数量

    Returns:
        Optional[Dict[str, Any]]: 文档块数据，失败时返回None
    """
    # 构建API URL
    api_url = (
        f"https://open.feishu.cn/open-apis/docx/v1/documents/{document_token}/blocks"
    )

    params = {"document_revision_id": document_revision_id, "page_size": page_size}

    headers = {"Authorization": f"Bearer {access_token}"}

    # 设置超时时间为30秒
    timeout = aiohttp.ClientTimeout(total=30)
    async with aiohttp.ClientSession(timeout=timeout) as session:
        async with session.get(
            api_url, headers=headers, params=params
        ) as response:
            response_text = await response.text()

            if response.status != 200:
                # 检查是否是限流错误，如果是则抛出异常让装饰器处理重试
                if "request trigger frequency limit" in response_text.lower():
                    raise Exception(f"request trigger frequency limit: {response_text}")
                else:
                    error_msg = f"获取文档块失败，状态码: {response.status}, 响应内容: {response_text}"
                    logger.warning(error_msg)
                    return None

            try:
                result_data = await response.json()
            except Exception as json_error:
                logger.error(
                    f"解析响应JSON失败: {json_error}, document_token: {document_token}, 响应内容: {response_text}"
                )
                return None

            if result_data.get("code") != 0:
                error_msg = f"获取文档块接口返回错误: {result_data.get('msg', '未知错误')}"
                logger.warning(f"{error_msg}, document_token: {document_token}")
                return None

            # 返回完整的响应数据
            logger.info(
                f"成功获取文档块数据，document_token: {document_token}, 块数量: {len(result_data.get('data', {}).get('items', []))}"
            )
            return result_data


def _format_spreadsheet_as_markdown(
    sheet_data: List[List[Any]], sheet_token: str
) -> str:
    """将电子表格数据格式化为Markdown表格。

    Args:
        sheet_data: 电子表格数据
        sheet_token: 电子表格token

    Returns:
        str: 格式化后的Markdown表格
    """
    values = sheet_data
    if not values:
        return f"\n**电子表格 {sheet_token} (无数据)**\n"

    try:
        # 构建Markdown表格
        markdown_table = f"\n**电子表格: {sheet_token}**\n\n"

        # 处理表格数据
        max_cols = max(len(row) for row in values) if values else 0

        if max_cols == 0:
            return f"\n**电子表格 {sheet_token} (无数据)**\n"

        # 构建表格头部和分隔线
        for row_idx, row in enumerate(values):
            # 补齐列数
            padded_row = row + [""] * (max_cols - len(row))

            # 构建表格行
            row_content = (
                "| "
                + " | ".join(
                    str(cell).replace("|", "\\|").replace("\n", " ")
                    for cell in padded_row
                )
                + " |"
            )
            markdown_table += row_content + "\n"

            # 在第一行后添加分隔线
            if row_idx == 0:
                separator = "| " + " | ".join(["---"] * max_cols) + " |"
                markdown_table += separator + "\n"

        markdown_table += "\n"
        return markdown_table

    except Exception as e:
        logger.exception(f"格式化电子表格为Markdown时发生异常: {str(e)}")
        return f"\n**电子表格 {sheet_token} (格式化失败: {str(e)})**\n"


# 注册工具
tool_manager.register_as_function_tool(search_feishu_docs)
tool_manager.register_as_function_tool(get_feishu_doc_content_tool)
