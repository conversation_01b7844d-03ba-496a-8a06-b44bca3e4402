"""
飞书文档内容解析工具 - 将飞书文档JSON结构转换为Markdown格式。
"""

from typing import List, Dict, Any, Optional, Tuple
from agents import RunContextWrapper
from src.models.user_info_class import UserInfo
from src.utils.logger import logger

class FeishuDocxContentParser:
    """飞书文档内容解析器 - 负责将JSON结构转换为Markdown"""
    
    def __init__(self):
        # 飞书文档块类型映射表
        self.block_type_mapping = {
            1: 'page',           # 页面标题
            2: 'text',           # 普通文本
            3: 'heading1',       # 一级标题
            4: 'heading2',       # 二级标题
            5: 'heading3',       # 三级标题
            6: 'heading4',       # 四级标题
            7: 'heading5',       # 五级标题
            8: 'heading6',       # 六级标题
            12: 'bullet',        # 无序列表
            13: 'ordered',       # 有序列表
            19: 'callout',       # 高亮块
            22: 'divider',       # 分割线
            24: 'grid',          # 分栏容器
            25: 'grid_column',   # 分栏列
            30: 'sheet',         # 电子表格
            31: 'table',         # 表格
            32: 'table_cell',    # 表格单元格
            34: 'quote_container' # 引用容器
        }
    
    def parse_feishu_docx_content(self, json_data: Dict[str, Any]) -> Tuple[str, List[str]]:
        """
        解析飞书文档JSON内容为Markdown格式
        
        Args:
            json_data: 飞书文档的JSON数据结构
            
        Returns:
            Tuple[str, List[str]]: (markdown内容, 电子表格token列表)
        """
        try:
            # 提取数据部分
            data = json_data.get('data', {})
            items = data.get('items', [])
            
            if not items:
                logger.warning("飞书文档JSON数据中没有找到items")
                return "", []
            
            # 构建块ID到块对象的映射，便于查找
            blocks_map = {item['block_id']: item for item in items}
            
            # 收集电子表格token
            sheet_tokens = []
            
            # 找到根节点（通常是第一个节点或parent_id为空的节点）
            root_blocks = [item for item in items if not item.get('parent_id')]
            if not root_blocks:
                # 如果没有找到根节点，使用第一个节点
                root_blocks = [items[0]]
            
            markdown_content = ""
            
            # 处理每个根节点
            for root_block in root_blocks:
                content, tokens = self._process_block(root_block, blocks_map, level=0)
                markdown_content += content
                sheet_tokens.extend(tokens)
            
            # 清理多余的空行
            markdown_content = self._clean_markdown(markdown_content)
            
            logger.info(f"成功解析飞书文档，生成Markdown内容长度: {len(markdown_content)}, 电子表格数量: {len(sheet_tokens)}")
            return markdown_content, sheet_tokens
            
        except Exception as e:
            logger.exception(f"解析飞书文档JSON内容时发生异常: {str(e)}")
            return f"解析失败: {str(e)}", []
    
    def _process_block(self, block: Dict[str, Any], blocks_map: Dict[str, Any], level: int = 0) -> Tuple[str, List[str]]:
        """
        递归处理单个块及其子块
        
        Args:
            block: 当前处理的块
            blocks_map: 所有块的映射表
            level: 当前嵌套层级
            
        Returns:
            Tuple[str, List[str]]: (markdown内容, 电子表格token列表)
        """
        block_type = block.get('block_type')
        block_id = block.get('block_id', '')
        content = ""
        sheet_tokens = []
        
        # 处理当前块的内容
        if block_type == 1:  # 页面标题
            content += self._process_page_title(block)
        elif block_type == 2:  # 普通文本
            content += self._process_text(block)
        elif block_type in [3, 4, 5, 6, 7, 8]:  # 各级标题
            content += self._process_heading(block, block_type)
        elif block_type == 12:  # 无序列表
            content += self._process_bullet_list(block, level)
        elif block_type == 13:  # 有序列表
            content += self._process_ordered_list(block, level)
        elif block_type == 19:  # 高亮块
            content += self._process_callout(block)
        elif block_type == 22:  # 分割线
            content += self._process_divider()
        elif block_type == 24:  # 分栏容器
            content += self._process_grid_container(block, blocks_map)
        elif block_type == 25:  # 分栏列
            content += self._process_grid_column(block, blocks_map)
        elif block_type == 30:  # 电子表格
            sheet_token = self._process_sheet(block)
            if sheet_token:
                sheet_tokens.append(sheet_token)
                content += f"\n[电子表格: {sheet_token}]\n\n"
        elif block_type == 31:  # 表格
            content += self._process_table(block, blocks_map)
        elif block_type == 32:  # 表格单元格
            # 表格单元格由表格处理，这里不单独处理
            pass
        elif block_type == 34:  # 引用容器
            content += self._process_quote_container(block, blocks_map)
        else:
            logger.debug(f"未处理的块类型: {block_type}, block_id: {block_id}")
        
        # 递归处理子块
        children = block.get('children', [])
        if children and block_type not in [24, 25, 31, 34]:  # 这些类型有特殊的子块处理逻辑
            for child_id in children:
                if child_id in blocks_map:
                    child_content, child_tokens = self._process_block(blocks_map[child_id], blocks_map, level + 1)
                    content += child_content
                    sheet_tokens.extend(child_tokens)
        
        return content, sheet_tokens
    
    def _process_page_title(self, block: Dict[str, Any]) -> str:
        """处理页面标题"""
        page_data = block.get('page', {})
        elements = page_data.get('elements', [])
        title_text = self._extract_text_from_elements(elements)
        if title_text:
            return f"# {title_text}\n\n"
        return ""
    
    def _process_text(self, block: Dict[str, Any]) -> str:
        """处理普通文本"""
        text_data = block.get('text', {})
        elements = text_data.get('elements', [])
        text_content = self._extract_text_from_elements(elements)
        if text_content:
            return f"{text_content}\n\n"
        return ""
    
    def _process_heading(self, block: Dict[str, Any], block_type: int) -> str:
        """处理各级标题"""
        # 根据block_type确定标题级别
        heading_levels = {3: 1, 4: 2, 5: 3, 6: 4, 7: 5, 8: 6}
        level = heading_levels.get(block_type, 1)
        
        # 获取标题内容
        heading_key = f'heading{level}'
        heading_data = block.get(heading_key, {})
        elements = heading_data.get('elements', [])
        heading_text = self._extract_text_from_elements(elements)
        
        if heading_text:
            markdown_prefix = '#' * level
            return f"{markdown_prefix} {heading_text}\n\n"
        return ""
    
    def _process_bullet_list(self, block: Dict[str, Any], level: int) -> str:
        """处理无序列表"""
        bullet_data = block.get('bullet', {})
        elements = bullet_data.get('elements', [])
        list_text = self._extract_text_from_elements(elements)
        
        if list_text:
            indent = "  " * level  # 根据层级缩进
            return f"{indent}- {list_text}\n"
        return ""
    
    def _process_ordered_list(self, block: Dict[str, Any], level: int) -> str:
        """处理有序列表"""
        ordered_data = block.get('ordered', {})
        elements = ordered_data.get('elements', [])
        list_text = self._extract_text_from_elements(elements)
        
        if list_text:
            indent = "  " * level  # 根据层级缩进
            sequence = ordered_data.get('style', {}).get('sequence', '1')
            if sequence == 'auto':
                sequence = '1'  # 自动编号默认为1
            return f"{indent}{sequence}. {list_text}\n"
        return ""
    
    def _process_callout(self, block: Dict[str, Any]) -> str:
        """处理高亮块"""
        callout_data = block.get('callout', {})
        emoji_id = callout_data.get('emoji_id', '')
        
        # 简单的emoji映射
        emoji_map = {
            'chestnut': '🌰',
            'warning': '⚠️',
            'info': 'ℹ️',
            'success': '✅',
            'error': '❌'
        }
        emoji = emoji_map.get(emoji_id, '💡')
        
        return f"\n> {emoji} "  # 引用格式表示高亮块，子内容会在后面处理
    
    def _process_divider(self) -> str:
        """处理分割线"""
        return "\n---\n\n"
    
    def _process_grid_container(self, block: Dict[str, Any], blocks_map: Dict[str, Any]) -> str:
        """处理分栏容器"""
        content = "\n<!-- 分栏开始 -->\n"
        
        # 处理分栏的子列
        children = block.get('children', [])
        for child_id in children:
            if child_id in blocks_map:
                child_block = blocks_map[child_id]
                if child_block.get('block_type') == 25:  # 分栏列
                    child_content, _ = self._process_block(child_block, blocks_map)
                    content += child_content
        
        content += "<!-- 分栏结束 -->\n\n"
        return content
    
    def _process_grid_column(self, block: Dict[str, Any], blocks_map: Dict[str, Any]) -> str:
        """处理分栏列"""
        grid_column_data = block.get('grid_column', {})
        width_ratio = grid_column_data.get('width_ratio', 50)
        
        content = f"\n<!-- 分栏列 (宽度: {width_ratio}%) -->\n"
        
        # 处理分栏列的子内容
        children = block.get('children', [])
        for child_id in children:
            if child_id in blocks_map:
                child_content, _ = self._process_block(blocks_map[child_id], blocks_map)
                content += child_content
        
        return content
    
    def _process_sheet(self, block: Dict[str, Any]) -> Optional[str]:
        """处理电子表格，返回token"""
        sheet_data = block.get('sheet', {})
        token = sheet_data.get('token')
        if token:
            logger.info(f"发现电子表格token: {token}")
            return token
        return None
    
    def _process_table(self, block: Dict[str, Any], blocks_map: Dict[str, Any]) -> str:
        """处理表格"""
        table_data = block.get('table', {})
        property_data = table_data.get('property', {})
        row_size = property_data.get('row_size', 0)
        column_size = property_data.get('column_size', 0)
        cells = table_data.get('cells', [])
        
        if not cells or row_size == 0 or column_size == 0:
            return ""
        
        content = "\n"
        
        # 构建表格
        for row in range(row_size):
            row_content = "|"
            for col in range(column_size):
                cell_index = row * column_size + col
                if cell_index < len(cells):
                    cell_id = cells[cell_index]
                    if cell_id in blocks_map:
                        cell_block = blocks_map[cell_id]
                        cell_content = self._process_table_cell(cell_block, blocks_map)
                        row_content += f" {cell_content} |"
                    else:
                        row_content += " |"
                else:
                    row_content += " |"
            
            content += row_content + "\n"
            
            # 添加表格分隔线（仅在第一行后）
            if row == 0:
                separator = "|"
                for _ in range(column_size):
                    separator += " --- |"
                content += separator + "\n"
        
        content += "\n"
        return content
    
    def _process_table_cell(self, block: Dict[str, Any], blocks_map: Dict[str, Any]) -> str:
        """处理表格单元格"""
        # 处理单元格的子内容
        children = block.get('children', [])
        cell_content = ""
        
        for child_id in children:
            if child_id in blocks_map:
                child_block = blocks_map[child_id]
                if child_block.get('block_type') == 2:  # 文本块
                    text_data = child_block.get('text', {})
                    elements = text_data.get('elements', [])
                    text = self._extract_text_from_elements(elements)
                    cell_content += text
        
        return cell_content.strip()
    
    def _process_quote_container(self, block: Dict[str, Any], blocks_map: Dict[str, Any]) -> str:
        """处理引用容器"""
        content = "\n> "
        
        # 处理引用的子内容
        children = block.get('children', [])
        for child_id in children:
            if child_id in blocks_map:
                child_block = blocks_map[child_id]
                if child_block.get('block_type') == 2:  # 文本块
                    text_data = child_block.get('text', {})
                    elements = text_data.get('elements', [])
                    text = self._extract_text_from_elements(elements)
                    content += text
        
        content += "\n\n"
        return content
    
    def _extract_text_from_elements(self, elements: List[Dict[str, Any]]) -> str:
        """从元素列表中提取文本内容"""
        text_parts = []
        
        for element in elements:
            text_run = element.get('text_run', {})
            content = text_run.get('content', '')
            style = text_run.get('text_element_style', {})
            
            # 应用文本样式
            if style.get('bold'):
                content = f"**{content}**"
            if style.get('italic'):
                content = f"*{content}*"
            if style.get('inline_code'):
                content = f"`{content}`"
            if style.get('strikethrough'):
                content = f"~~{content}~~"
            if style.get('underline'):
                content = f"<u>{content}</u>"
            
            text_parts.append(content)
        
        return ''.join(text_parts)
    
    def _clean_markdown(self, content: str) -> str:
        """清理Markdown内容，移除多余的空行"""
        lines = content.split('\n')
        cleaned_lines = []
        prev_empty = False
        
        for line in lines:
            is_empty = not line.strip()
            
            # 避免连续的空行
            if is_empty and prev_empty:
                continue
            
            cleaned_lines.append(line)
            prev_empty = is_empty
        
        # 移除开头和结尾的空行
        while cleaned_lines and not cleaned_lines[0].strip():
            cleaned_lines.pop(0)
        while cleaned_lines and not cleaned_lines[-1].strip():
            cleaned_lines.pop()
        
        return '\n'.join(cleaned_lines)