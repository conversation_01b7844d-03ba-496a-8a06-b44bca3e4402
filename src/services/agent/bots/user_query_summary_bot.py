"""
用户查询总结机器人模块 - 基于LLM的重构版
使用LLM分析用户历史对话记录，智能总结用户常问问题的TOP N模式
"""

from typing import Dict, Any, List, Optional
import json
import re
from datetime import datetime

from agents import Agent, Model, Runner, ModelSettings
from src.models.user_info_class import UserInfo
from src.services.agent.bots.base_bot import BaseBot
from src.services.agent.utils.model_provider import get_fast_model
from src.utils.logger import logger


class UserQuerySummaryBot(BaseBot):
    """
    用户查询总结机器人。
    该机器人使用LLM分析用户历史对话记录，智能总结用户常问问题的TOP N模式，
    识别用户的关注重点和查询习惯，提供个性化的查询模式总结。
    """

    def __init__(self, user_info: Dict[str, Any], count: int = 5, message_limit: int = 200):
        """
        初始化用户查询总结机器人。

        Args:
            user_info (Dict[str, Any]): 用户信息字典，包含user_id等关键信息
            count (int): 返回的常见问题数量，默认为5个
            message_limit (int): 分析的历史消息数量限制，默认为200条
        """
        super().__init__(user_info)
        self.count = count
        self.message_limit = message_limit
        self.user_id = user_info.get('user_id')
        

    def get_description(self) -> str:
        """
        获取机器人的描述信息。

        Returns:
            str: 机器人的描述。
        """
        return "我是一个智能查询总结机器人，基于LLM分析您的历史对话记录，为您识别最常关注的业务问题模式和查询习惯，提供更精准的个性化数据洞察。"

    def _create_summary_prompt(self, user_messages: List[str]) -> str:
        """
        创建用于用户查询总结的LLM提示文本。
        
        让LLM分析用户的历史消息，识别常见问题和关注点。

        Args:
            user_messages: 用户历史消息列表
            
        Returns:
            str: LLM提示文本
        """
        messages_str = "\n".join([f"{i+1}. {msg}" for i, msg in enumerate(user_messages)])
        
        prompt = f"""
你是一个专业的用户行为分析专家，需要基于用户的聊天历史，识别和总结用户最常询问的业务问题模式。

## 分析任务
你需要仔细分析下面用户的历史提问，提取用户最常关注的业务问题类型，并总结成{self.count}个精准的top问题模式。

## 用户历史消息（共{len(user_messages)}条）:
{messages_str}

## 输出要求
1. 基于真实的历史消息，不要生成新的问题
2. 识别重复出现的问题pattern，用简洁的短语总结
3. 提供每个问题模式的具体出现次数和占比
4. 总结用户最核心的5个关注点
5. 每个问题描述要具体、清晰，体现业务场景

## 输出格式
请严格按照以下JSON格式输出：
```json
{{
  "summary": [
    {{
      "rank": 1,
      "pattern": "具体的问题模式描述",
      "frequency": 出现次数,
      "percentage": "25.5%",
      "description": "用户在此场景下的具体关注重点"
    }}
  ],
  "total_messages": {len(user_messages)},
  "analyzed_messages": 实际用词分析的消息数量,
  "key_insights": "对用户需求的专业洞察总结"
}}
```

## 问题模式示例
- 客户购买行为分析：针对特定客户群体的商品购买频次和趋势
- 销售业绩监控：针对BD或团队的销售额、转化率数据
- 库存状态查询：特定商品在不同仓库的库存水平
- 订单履约分析：订单完成率、配送效率等关键指标
- 新增客户追踪：新注册门店或新客户的业务表现

请基于用户真实的历史提问，提供精准的总结和分析。
"""
        return prompt.strip()

    def create_agent(self, model: Model = None) -> Agent:
        """
        创建用于用户查询总结的LLM Agent实例。

        Args:
            model (Model, optional): Agent使用的语言模型。默认为快速模型。

        Returns:
            Agent: 配置好的LLM Agent实例。
        """
        if model is None:
            model = get_fast_model()

        # 创建动态agent，指令会在调用时动态设置
        agent = Agent[UserInfo](
            name="用户查询总结助手",
            instructions="你是一个专业的用户行为分析专家，擅长识别用户查询模式并提供精准的业务洞察。",
            model=model,
            model_settings=ModelSettings(
                extra_body={
                    "provider": {
                        "sort": "latency",
                    }
                }
            ),
        )
        return agent

    async def summarize_user_queries(self, user_messages: List[str]) -> Dict[str, Any]:
        """
        使用LLM分析用户历史消息，返回常见问题总结。
        
        基于用户的最近200条消息，调用LLM识别最常询问的业务问题模式。

        Args:
            user_messages (List[str]): 用户历史消息列表，应包含最近200条消息

        Returns:
            Dict[str, Any]: 包含常见问题总结和分析结果的字典
        """
        if not user_messages:
            logger.warning("No user messages provided for LLM analysis")
            return {
                "summary": [],
                "total_messages": 0,
                "analyzed_messages": 0,
                "status": "no_data",
                "key_insights": "没有可供分析的历史消息"
            }

        # 限制消息数量并过滤无效消息
        messages_to_analyze = user_messages[-self.message_limit:]
        valid_messages = [msg for msg in messages_to_analyze if msg and len(msg.strip()) >= 5]
        total_messages = len(messages_to_analyze)
        valid_count = len(valid_messages)
        
        if valid_count == 0:
            logger.warning("No valid messages found for analysis")
            return {
                "summary": [],
                "total_messages": total_messages,
                "analyzed_messages": valid_count,
                "status": "no_valid_data",
                "key_insights": "没有有效的提问消息可供分析"
            }

        logger.info(f"LLM analyzing {valid_count} valid messages for user {self.user_id}")

        try:
            agent = self.create_agent()
            prompt = self._create_summary_prompt(valid_messages)
            
            # 调用LLM进行分析
            response = await Runner.run(agent, prompt)
            
            # 解析LLM返回的JSON格式响应
            response_text = str(response.final_output).strip()
            
            # 提取JSON部分
            json_match = re.search(r'```json\s*\n(.*?)\n\s*```', response_text, re.DOTALL)
            if json_match:
                json_text = json_match.group(1)
            else:
                json_text = response_text
            
            result = {
                "llm_response": json_text,
                "total_messages": total_messages,
                "analyzed_messages": valid_count,
                "status": "llm_analysis",
                "generated_at": datetime.now().isoformat(),
                "user_id": self.user_id
            }
            
            try:
                llm_result = json.loads(json_text)
                # 合并LLM结果
                result.update(llm_result)
                logger.info(f"LLM generated summary for user {self.user_id}: {len(result.get('summary', []))} patterns found")
            except json.JSONDecodeError as e:
                logger.warning(f"Failed to parse LLM JSON response: {e}")
                # 回退到文本模式
                result.update({
                    "summary": [],
                    "key_insights": response_text,
                    "status": "text_fallback"
                })
                
            return result
            
        except Exception as e:
            logger.exception(f"Error during LLM analysis: {e}", exc_info=True)
            return {
                "summary": [],
                "total_messages": total_messages,
                "analyzed_messages": valid_count,
                "status": "error",
                "error": str(e),
                "key_insights": "分析过程中出现错误"
            }

    async def get_summary_json_string(self, user_messages: List[str]) -> str:
        """
        获取用户查询总结的JSON字符串表示，使用LLM分析结果。
        
        Args:
            user_messages (List[str]): 用户历史消息列表
            
        Returns:
            str: JSON格式的字符串，使用ensure_ascii=False保证中文正常展示
        """
        summary_data = await self.summarize_user_queries(user_messages)
        # 使用ensure_ascii=False确保中文正常显示，适用于数据库存储
        return json.dumps(summary_data, ensure_ascii=False, indent=2)
