"""
Data fetcher bot implementation.
"""

from agents.agent import StopAtTools
import yaml
import textwrap
import json  # Added import
from typing import Optional, Dict, Any

from agents import Agent, Model, ModelSettings

from src.models.user_info_class import UserInfo
from src.services.agent.bots.base_bot import BaseBot
from src.services.agent.utils.model_provider import (
    get_claude_model,
    get_model_for_name,
)
from src.services.agent.tools.tool_manager import tool_manager
from src.utils.resource_manager import load_resource
from src.utils.logger import logger


def _load_config(config_file: str) -> Dict[str, Any]:
    """
    Load the agent configuration from a YAML file.

    Args:
        config_file: Name of the YAML configuration file

    Returns:
        Dict containing the configuration
    """
    # 使用 resource_manager 加载配置文件
    yaml_content = load_resource("data_fetcher_bot_config", config_file)

    if yaml_content:
        try:
            return yaml.safe_load(yaml_content)
        except Exception as e:
            # 如果 YAML 解析失败，记录错误并返回默认配置
            logger.exception(f"Error parsing YAML config file {config_file}: {e}")
    else:
        logger.exception(
            f"Config file {config_file} not found in data_fetcher_bot_config directory"
        )

    # 如果加载或解析失败，返回默认配置
    return {
        "agent_name": "sales_order_analytics",
        "agent_description": "销售订单分析专家",
        "agent_tables": [],
    }


class DataFetcherBot(BaseBot):
    """
    Data fetcher bot for retrieving data via SQL queries.

    This bot is specialized in:
    - Fetching DDL information for specific tables
    - Writing and executing SQL queries
    - Retrieving sample data from tables

    The bot can be configured with different YAML files to specialize in different domains.
    """

    def __init__(
        self, user_info: Dict[str, Any], config_file: str = "sales_order_analytics.yml"
    ):
        super().__init__(user_info)
        self.config_file = config_file
        self.config = _load_config(config_file)
        if len(self.config.get("agent_tables", [])) == 0:
            logger.warning(f"Agent {self.config.get('agent_name')}没有配置相关数据表")
            self.table_with_desc = None
        else:
            self.table_with_desc = [
                f"- {t.get('name', 'N/A')}: {t.get('desc', 'N/A')}"
                for t in self.config.get("agent_tables", [])
            ]
            self.table_with_desc = "\n".join(self.table_with_desc)
        self.need_system_prompt = self.config.get("need_system_prompt", True)
        self.agent_name = self.config.get("agent_name")
        self.agent_description = self._load_agent_description()

    def get_agent_as_tool_description(self) -> str:
        return self.config.get("agent_as_tool_description", "")

    def get_model_settings(self) -> Dict[str, Any]:
        return self.config.get("model_settings", {})

    def _load_agent_description(self) -> str:
        """
        加载Agent描述内容，支持从外部文件加载，默认为 {agent_name}.md，如果没有找到，则使用默认描述

        优先级：
        1. agent_description配置项指定的外部文件
        2. 默认使用{agent_name}.md文件

        Returns:
            str: Agent描述内容
        """
        agent_name = self.agent_name
        agent_description_file = f"{agent_name}.md"
        # 检查是否配置了agent_description
        if self.config.get("agent_description"):
            # 使用指定的外部文件
            agent_description_file = self.config.get("agent_description")

        logger.info(f"使用外部文件加载Agent描述: {agent_description_file}")

        content = load_resource("prompt", agent_description_file)
        if content:
            return content

        # 如果都没有找到，返回默认描述，默认描述为 {agent_name}专家助手
        logger.warning(f"未找到Agent描述内容，使用的文件是:{agent_description_file}，使用默认描述:{agent_name}专家助手")
        return f"{agent_name}专家助手"

    def get_description(self) -> str:
        agent_description = self.agent_description
        if self.table_with_desc is None:
            return agent_description
        return f"{agent_description}\n我可以处理以下相关数据表:\n{self.table_with_desc}"

    def create_agent(
        self, model: Optional[Model] = None, model_settings_str: Optional[str] = None, acting_as_standard_agent: Optional[bool] = False
    ) -> Agent:
        agent_name = self.agent_name
        tools = self.config.get("tools", [])

        # 从配置文件读取model配置，如果没有则使用传入的参数或默认值
        config_model = self.config.get("model")
        config_model_settings = self.get_model_settings()

        tool_list = tool_manager.get_tool_list([tool["name"] for tool in tools])

        # 获取描述内容
        agent_description = self.agent_description

        if self.need_system_prompt:
            system_instruction = load_resource("prompt", "data_fetcher_instruction.md")
        else:
            system_instruction = ""

        domain_instruction = textwrap.dedent(
            f"""
            {agent_description}
            ===
            相关数据表:
            {self.table_with_desc}
            ===
        """
        ).strip()

        # Create realtime instruction with user context
        realtime_instruction = self.get_user_realtime_instruction()

        instruction = (
            f"{system_instruction}\n"
            f"{domain_instruction}\n"
            f"{realtime_instruction}\n"
        )

        # 确定使用的模型（带异常处理）
        final_model = self._safe_get_model(config_model, model)

        # 处理model_settings（带异常处理）
        model_settings = self._safe_get_model_settings(
            config_model_settings, model_settings_str
        )

        # 从配置文件读取 stop_at_tool_names，如果没有配置则使用默认值
        stop_at_tool_names = self.config.get("stop_at_tool_names", ["fetch_mysql_sql_result", "fetch_odps_sql_result"])

        # 根据是否作为标准agent和配置决定tool_use_behavior
        if acting_as_standard_agent:
            # 当作为标准agent使用时，不使用StopAtTools
            tool_use_behavior = "run_llm_again"
            logger.info(f"{agent_name} 作为标准agent使用，不使用StopAtTools")
        elif stop_at_tool_names and len(stop_at_tool_names) > 0:
            # 当有配置的stop_at_tool_names时，使用StopAtTools
            tool_use_behavior = StopAtTools(stop_at_tool_names=stop_at_tool_names)
            logger.info(f"{agent_name} 使用StopAtTools，stop_at_tool_names: {stop_at_tool_names}")
        else:
            # 当stop_at_tool_names为空时，使用默认行为
            tool_use_behavior = "run_llm_again"
            logger.info(f"{agent_name} 使用默认行为，stop_at_tool_names为空")

        # 创建Agent
        agent_kwargs = {
            "name": f"{agent_name}",
            "instructions": instruction,
            "model": final_model,
            "tools": tool_list,
            "tool_use_behavior": tool_use_behavior,
        }

        if model_settings:
            agent_kwargs["model_settings"] = model_settings

        return Agent[UserInfo](**agent_kwargs)

    def _safe_get_model(
        self, config_model: str, fallback_model: Optional[Model]
    ) -> Model:
        """安全获取模型实例，优先使用配置文件中的模型"""
        if config_model:
            try:
                # 从配置中获取model_provider，如果没有则使用默认provider
                config_provider = self.config.get("model_provider")
                model_instance = get_model_for_name(config_model, config_provider)
                logger.info(f"{self.agent_name}: 使用配置文件中的模型: {config_model}, provider: {config_provider}")
                return model_instance
            except Exception as e:
                logger.warning(f"{self.agent_name}: 配置模型 {config_model} 创建失败: {e}，使用默认模型")

        return fallback_model or get_claude_model()

    def _safe_get_model_settings(
        self, config_settings: Any, fallback_settings: Optional[str]
    ) -> Optional[ModelSettings]:
        """安全获取模型设置，优先使用配置文件中的设置"""
        settings_source = config_settings or fallback_settings

        if not settings_source or settings_source == "" or settings_source == "{}":
            return None

        try:
            # 处理字典格式的配置
            if isinstance(settings_source, dict):
                logger.info(f"{self.agent_name}: 使用配置文件中的model_settings: {settings_source}")
                return ModelSettings(**settings_source)

            # 处理JSON字符串格式
            settings_dict = json.loads(settings_source)
            logger.info(f"{self.agent_name}: 解析model_settings成功: {settings_dict}")
            return ModelSettings(**settings_dict)
        except (json.JSONDecodeError, TypeError) as e:
            logger.warning(f"{self.agent_name}: 解析model_settings失败: {e}，跳过设置")
            return None
