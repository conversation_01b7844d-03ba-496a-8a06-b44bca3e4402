"""
Agent使用情况分析服务

在Agent as Tool架构下，分析和统计agent的使用情况，
提供对agent使用模式的洞察。
"""

from typing import Dict, List, Any, Optional
from collections import Counter
from datetime import datetime, timedelta

from src.db.connection import execute_db_query
from src.utils.logger import logger


def get_agent_usage_statistics(
    start_time: Optional[int] = None,
    end_time: Optional[int] = None,
    username: Optional[str] = None,
    email: Optional[str] = None,
    limit: int = 100
) -> Dict[str, Any]:
    """
    获取agent使用统计信息
    
    Args:
        start_time: 开始时间戳（毫秒）
        end_time: 结束时间戳（毫秒）
        username: 用户名过滤
        email: 邮箱过滤
        limit: 返回结果数量限制
        
    Returns:
        Dict: 包含各种统计信息的字典
    """
    try:
        # 构建查询条件
        where_clauses = ["ch.role = 'assistant'", "ch.agent IS NOT NULL", "ch.agent != ''"]
        params = []
        
        if start_time:
            where_clauses.append("ch.timestamp >= %s")
            params.append(start_time)
        
        if end_time:
            where_clauses.append("ch.timestamp <= %s")
            params.append(end_time)
        
        if username:
            where_clauses.append("ch.username = %s")
            params.append(username)
        
        if email:
            where_clauses.append("ch.email = %s")
            params.append(email)
        
        where_clause = " AND ".join(where_clauses)
        
        # 基础统计查询
        basic_stats_sql = f"""
            SELECT 
                COUNT(*) as total_queries,
                COUNT(DISTINCT ch.conversation_id) as total_conversations,
                COUNT(DISTINCT ch.username) as total_users,
                COUNT(DISTINCT ch.email) as total_unique_emails
            FROM chat_history ch
            WHERE {where_clause}
        """
        
        basic_stats = execute_db_query(basic_stats_sql, tuple(params), fetch='one')
        
        # Agent使用频率统计
        agent_freq_sql = f"""
            SELECT 
                ch.agent,
                COUNT(*) as usage_count,
                COUNT(DISTINCT ch.conversation_id) as conversation_count,
                COUNT(DISTINCT ch.username) as user_count
            FROM chat_history ch
            WHERE {where_clause}
            GROUP BY ch.agent
            ORDER BY usage_count DESC
            LIMIT %s
        """
        
        agent_freq_params = tuple(params) + (limit,)
        agent_freq_result = execute_db_query(agent_freq_sql, agent_freq_params, fetch='all')
        
        # 解析agent组合使用情况
        agent_combinations = analyze_agent_combinations(agent_freq_result)
        
        # 时间趋势分析（按天）
        time_trend_sql = f"""
            SELECT 
                DATE(FROM_UNIXTIME(ch.timestamp / 1000)) as usage_date,
                COUNT(*) as query_count,
                COUNT(DISTINCT ch.agent) as unique_agents_count
            FROM chat_history ch
            WHERE {where_clause}
            GROUP BY usage_date
            ORDER BY usage_date DESC
            LIMIT 30
        """
        
        time_trend_result = execute_db_query(time_trend_sql, tuple(params), fetch='all')
        
        return {
            "basic_stats": dict(basic_stats) if basic_stats else {},
            "agent_frequency": [dict(row) for row in agent_freq_result] if agent_freq_result else [],
            "agent_combinations": agent_combinations,
            "time_trend": [dict(row) for row in time_trend_result] if time_trend_result else [],
            "query_info": {
                "start_time": start_time,
                "end_time": end_time,
                "username": username,
                "email": email,
                "limit": limit
            }
        }
        
    except Exception as e:
        logger.exception(f"获取agent使用统计失败: {e}", exc_info=True)
        return {
            "basic_stats": {},
            "agent_frequency": [],
            "agent_combinations": {},
            "time_trend": [],
            "error": str(e)
        }


def analyze_agent_combinations(agent_freq_result: List[Dict]) -> Dict[str, Any]:
    """
    分析agent组合使用情况
    
    Args:
        agent_freq_result: agent频率查询结果
        
    Returns:
        Dict: agent组合分析结果
    """
    try:
        single_agents = []
        multi_agents = []
        
        for row in agent_freq_result:
            agent_str = row.get('agent', '')
            if not agent_str:
                continue
                
            # 检查是否包含多个agent（逗号分隔）
            if ',' in agent_str:
                agents = [a.strip() for a in agent_str.split(',') if a.strip()]
                multi_agents.append({
                    'agents': agents,
                    'agent_count': len(agents),
                    'usage_count': row.get('usage_count', 0),
                    'conversation_count': row.get('conversation_count', 0)
                })
            else:
                single_agents.append({
                    'agent': agent_str.strip(),
                    'usage_count': row.get('usage_count', 0),
                    'conversation_count': row.get('conversation_count', 0)
                })
        
        # 统计多agent查询的agent组合模式
        combination_patterns = Counter()
        for combo in multi_agents:
            # 排序后的agent列表作为模式
            pattern = ','.join(sorted(combo['agents']))
            combination_patterns[pattern] += combo['usage_count']
        
        return {
            "single_agent_queries": len(single_agents),
            "multi_agent_queries": len(multi_agents),
            "top_single_agents": sorted(single_agents, key=lambda x: x['usage_count'], reverse=True)[:10],
            "top_combinations": [
                {"pattern": pattern, "count": count} 
                for pattern, count in combination_patterns.most_common(10)
            ],
            "multi_agent_stats": {
                "total_multi_queries": sum(combo['usage_count'] for combo in multi_agents),
                "avg_agents_per_multi_query": sum(combo['agent_count'] * combo['usage_count'] for combo in multi_agents) / max(sum(combo['usage_count'] for combo in multi_agents), 1)
            }
        }
        
    except Exception as e:
        logger.exception(f"分析agent组合失败: {e}", exc_info=True)
        return {}


def get_user_agent_usage_pattern(username: str, email: str, days: int = 30) -> Dict[str, Any]:
    """
    获取特定用户的agent使用模式
    
    Args:
        username: 用户名
        email: 用户邮箱
        days: 分析的天数
        
    Returns:
        Dict: 用户agent使用模式
    """
    try:
        # 计算时间范围
        end_time = int(datetime.now().timestamp() * 1000)
        start_time = int((datetime.now() - timedelta(days=days)).timestamp() * 1000)
        
        # 获取用户的agent使用统计
        user_stats = get_agent_usage_statistics(
            start_time=start_time,
            end_time=end_time,
            username=username,
            email=email
        )
        
        # 获取用户最常用的agent组合
        sql = """
            SELECT 
                ch.agent,
                COUNT(*) as usage_count,
                COUNT(DISTINCT ch.conversation_id) as conversation_count,
                MIN(ch.timestamp) as first_used,
                MAX(ch.timestamp) as last_used
            FROM chat_history ch
            WHERE ch.username = %s AND ch.email = %s 
            AND ch.role = 'assistant' AND ch.agent IS NOT NULL AND ch.agent != ''
            AND ch.timestamp >= %s
            GROUP BY ch.agent
            ORDER BY usage_count DESC
            LIMIT 20
        """
        
        user_agent_result = execute_db_query(sql, (username, email, start_time), fetch='all')
        
        return {
            "user_info": {"username": username, "email": email, "analysis_days": days},
            "overall_stats": user_stats["basic_stats"],
            "agent_usage": [dict(row) for row in user_agent_result] if user_agent_result else [],
            "combinations": user_stats["agent_combinations"],
            "time_trend": user_stats["time_trend"]
        }
        
    except Exception as e:
        logger.exception(f"获取用户agent使用模式失败: {e}", exc_info=True)
        return {"error": str(e)}


def get_agent_performance_metrics(agent_name: str, days: int = 30) -> Dict[str, Any]:
    """
    获取特定agent的性能指标
    
    Args:
        agent_name: agent名称
        days: 分析的天数
        
    Returns:
        Dict: agent性能指标
    """
    try:
        # 计算时间范围
        end_time = int(datetime.now().timestamp() * 1000)
        start_time = int((datetime.now() - timedelta(days=days)).timestamp() * 1000)
        
        # 查询包含该agent的记录
        sql = """
            SELECT 
                COUNT(*) as total_usage,
                COUNT(DISTINCT ch.conversation_id) as conversation_count,
                COUNT(DISTINCT ch.username) as user_count,
                MIN(ch.timestamp) as first_used,
                MAX(ch.timestamp) as last_used
            FROM chat_history ch
            WHERE (ch.agent = %s OR ch.agent LIKE %s OR ch.agent LIKE %s OR ch.agent LIKE %s)
            AND ch.role = 'assistant' AND ch.timestamp >= %s
        """
        
        # 构建LIKE模式来匹配agent名称
        like_patterns = [
            f"{agent_name},%",  # 开头
            f"%,{agent_name},%",  # 中间
            f"%,{agent_name}"  # 结尾
        ]
        
        params = (agent_name,) + tuple(like_patterns) + (start_time,)
        result = execute_db_query(sql, params, fetch='one')
        
        return {
            "agent_name": agent_name,
            "analysis_days": days,
            "metrics": dict(result) if result else {}
        }
        
    except Exception as e:
        logger.exception(f"获取agent性能指标失败: {e}", exc_info=True)
        return {"error": str(e)}
