"""
用户查询历史总结服务。
负责定时为最近30天活跃用户的查询历史进行总结，提取top N常用问题。
继续使用现有的user_recommendation_offline表结构存储数据。
"""

import json
import re
from datetime import datetime, timedelta
from typing import List, Dict, Any
from collections import Counter

from src.services.chatbot.history_service import get_user_latest_queries
from src.db.connection import get_db_connection, Database
from src.utils.logger import logger
from src.repositories.chatbi.recommendation import ChatbiRecommendationRepository
from src.models.recommendation import UserRecommendation, RecommendationStatus


class QuerySummarizer:
    """查询历史总结器"""
    
    def __init__(self):
        """
        初始化查询总结器
        使用项目环境配置
        """
        from src.services.agent.utils.model_provider import get_claude_model
        from agents import ModelSettings
        
        self.model = get_claude_model()
        self.model_settings = ModelSettings(
            temperature=0.3,
            max_tokens=2000
        )
    
    def preprocess_queries(self, queries: List[str]) -> List[str]:
        """
        预处理查询数据，去重和清理
        
        Args:
            queries: 原始查询列表
            
        Returns:
            清理后的查询列表
        """
        cleaned_queries = []
        seen = set()
        
        for query in queries:
            # 基本清理
            query = query.strip()
            if not query or query.isdigit():
                continue
                
            # 去重（忽略标点符号差异）
            normalized = re.sub(r'[？?。！!，,；;：:]', '', query.lower())
            if normalized not in seen:
                seen.add(normalized)
                cleaned_queries.append(query)
                
        return cleaned_queries
    
    def call_llm(self, prompt: str) -> str:
        """
        调用LLM进行查询总结
        
        Args:
            prompt: 提示词
            
        Returns:
            LLM的响应
        """
        try:
            from agents import Runner, RunConfig, Agent
            import asyncio
            agent = Agent(
                name="查询历史总结器",
                instructions="请根据输入内容生成常用问题列表。",
                model=self.model,
                model_settings=self.model_settings,
            )
            try:
                result = asyncio.run(
                    Runner.run(agent, prompt, run_config=RunConfig(model_settings=self.model_settings))
                )
            except RuntimeError:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    result = loop.run_until_complete(
                        Runner.run(agent, prompt, run_config=RunConfig(model_settings=self.model_settings))
                    )
                finally:
                    try:
                        loop.close()
                    finally:
                        asyncio.set_event_loop(None)
            return str(result.final_output)
        except Exception as e:
            raise Exception(f"调用LLM模型失败: {str(e)}")
    
    def summarize_queries(self, queries: List[str], n: int = 10) -> List[str]:
        """
        总结用户查询历史，生成常问问题
        
        Args:
            queries: 用户查询历史列表
            n: 需要生成的常问问题数量
            
        Returns:
            总结后的常问问题列表
        """
        # 预处理查询
        cleaned_queries = self.preprocess_queries(queries)
        
        if len(cleaned_queries) == 0:
            return ["没有找到有效的查询记录"]
        
        # 构建提示词
        queries_text = "\n".join([f"{i+1}. {query}" for i, query in enumerate(cleaned_queries)])
        
        prompt = f"""
请分析以下{len(cleaned_queries)}条用户查询历史，总结出{n}个最常问的具体问题。

用户查询历史：
{queries_text}

要求：
1. 合并相似的问题（因标点符号、顺序、表述方式不同导致的重复）
2. 提取最高频、最核心的业务查询场景
3. 输出{n}条用户可以直接复制使用的具体问题
4. 保持原有的业务术语和格式，确保中文表达准确
5. 按使用频率从高到低排序

请直接输出{n}条具体问题，每行一个问题，格式如：
1. 具体问题内容
2. 具体问题内容
...
"""

        # 调用LLM
        response = self.call_llm(prompt)
        
        # 解析响应
        lines = response.strip().split('\n')
        questions = []
        
        for line in lines:
            line = line.strip()
            if line and (line[0].isdigit() or line.startswith('-') or line.startswith('•')):
                # 提取问题内容
                question = re.sub(r'^\d+\.?\s*', '', line)  # 去掉序号
                question = re.sub(r'^[-•]\s*', '', question)  # 去掉列表符号
                if question:
                    questions.append(question)
        
        return questions[:n]  # 确保返回指定数量
    
    def analyze_query_patterns(self, queries: List[str]) -> Dict[str, Any]:
        """
        分析查询模式，提供额外的统计信息
        
        Args:
            queries: 查询历史
            
        Returns:
            分析结果字典
        """
        cleaned_queries = self.preprocess_queries(queries)
        
        # 关键词频率分析
        keywords = []
        for query in cleaned_queries:
            # 提取关键词（简单的中文分词）
            words = re.findall(r'[\u4e00-\u9fff]+|[A-Za-z0-9]+', query)
            keywords.extend(words)
        
        keyword_freq = Counter(keywords).most_common(20)
        
        # 查询类型分析
        query_types = {
            "业绩查询": len([q for q in cleaned_queries if "业绩" in q]),
            "GMV查询": len([q for q in cleaned_queries if "GMV" in q]),
            "客户查询": len([q for q in cleaned_queries if "客户" in q]),
            "商品查询": len([q for q in cleaned_queries if any(word in q for word in ["卖", "份", "SKU"])]),
            "价格查询": len([q for q in cleaned_queries if "价" in q]),
            "订单查询": len([q for q in cleaned_queries if any(word in q for word in ["订单", "单"])]),
            "库存查询": len([q for q in cleaned_queries if "库存" in q]),
        }
        
        return {
            "total_queries": len(cleaned_queries),
            "unique_queries": len(set(cleaned_queries)),
            "top_keywords": keyword_freq,
            "query_types": query_types
        }


class OfflineRecommendationService:
    """用户查询历史总结服务"""
    
    def __init__(self):
        """
        初始化查询总结服务
        """
        self.recommendations_per_user = 10
        self.expire_hours = 168
        self.query_summarizer = QuerySummarizer()
        self.repo = ChatbiRecommendationRepository()
    
    def run_daily_recommendation_task(self):
        """
        每日运行推荐生成任务（实际为查询总结）
        保持向后兼容的方法名
        
        1. 获取最近30天有聊天记录的用户
        2. 为每个用户分析查询历史
        3. 生成top N常用问题
        4. 存储到用户推荐表中
        """
        logger.info("开始执行每日查询历史总结任务")
        
        try:
            # 获取最近30天有聊天记录的用户
            active_users = self._get_active_users(days=30)
            logger.info(f"找到 {len(active_users)} 个最近30天有聊天记录的用户")
            
            if not active_users:
                logger.info("没有找到需要生成查询总结的用户")
                return
            
            # 为每个用户生成查询总结
            success_count = 0
            failed_count = 0
            
            for user_info in active_users:
                try:
                    self._generate_summary_for_user(user_info)
                    success_count += 1
                    logger.info(f"为用户 {user_info['email']} 生成查询总结完成")
                except Exception as e:
                    failed_count += 1
                    logger.exception(f"为用户 {user_info['email']} 生成查询总结失败: {e}")
            
            logger.info(f"查询历史总结任务完成: 成功 {success_count} 个用户, 失败 {failed_count} 个用户")
            
        except Exception as e:
            logger.exception(f"查询历史总结任务执行失败: {e}", exc_info=True)
            raise
    
    def _get_active_users(self, days: int = 30) -> List[Dict[str, str]]:
        """
        获取最近N天有聊天记录的用户
        """
        users = self.repo.find_active_users(days)
        return [u.get_user_info_dict() for u in users]
    
    def _generate_summary_for_user(self, user_info: Dict[str, str]):
        """
        为单个用户生成查询历史总结
        
        Args:
            user_info: 用户信息，包含 email, open_id, name
        """
        user_email = user_info['email']
        user_open_id = user_info['open_id']
        user_name = user_info['name']
        
        # 检查是否有未过期的查询总结
        if self._has_valid_summary(user_email):
            logger.info(f"用户 {user_email} 已有有效的查询总结，跳过生成")
            return
        
        # 标记为正在生成
        self._update_generation_status(user_email, user_open_id, status=1)  # 生成中
        
        try:
            # 获取用户历史查询，按时间降序排列（最近100条用于分析）
            user_messages = get_user_latest_queries(
                user_email=user_email,
                limit=100
            )
            
            if not user_messages:
                logger.info(f"用户 {user_email} 没有查询历史")
                # 使用默认常用问题
                default_summary = self._get_default_frequent_queries()
                self._store_summary(
                    user_email, user_open_id, default_summary, {}, user_name
                )
                return
            
            # 提取查询文本
            queries = [q for q in user_messages if isinstance(q, str) and q.strip()]
            
            if not queries:
                logger.info(f"用户 {user_email} 查询内容为空")
                default_summary = self._get_default_frequent_queries()
                self._store_summary(
                    user_email, user_open_id, default_summary, {}, user_name
                )
                return
            
            # 分析查询模式
            analysis_result = self.query_summarizer.analyze_query_patterns(queries)
            
            # 生成常用问题总结
            frequent_queries = self.query_summarizer.summarize_queries(
                queries, 
                n=self.recommendations_per_user
            )
            
            # 存储总结结果
            self._store_summary(
                user_email, user_open_id, frequent_queries, analysis_result, user_name
            )
            
            # 更新状态为完成
            self._update_generation_status(user_email, user_open_id, status=2)  # 完成
            
        except Exception as e:
            # 更新状态为失败
            self._update_generation_status(
                user_email, user_open_id, 
                status=3,  # 失败
                error_message=str(e)
            )
            raise
    
    def _has_valid_summary(self, user_email: str) -> bool:
        """检查用户是否有未过期的查询总结"""
        rec = self.repo.find_by_user_email(user_email)
        return rec is not None and rec.is_valid()
    
    def _store_summary(self, user_email: str, user_open_id: str, 
                      frequent_queries: List[str], analysis_result: Dict[str, Any], 
                      user_name: str):
        """
        存储查询总结结果
        """
        rec = UserRecommendation.create_new_recommendation(
            user_email=user_email,
            user_open_id=user_open_id,
            recommendations=frequent_queries,
            expire_hours=self.expire_hours
        )
        self.repo.save(rec)
        logger.info(f"成功存储用户 {user_email} 的查询总结结果，共 {len(frequent_queries)} 条常用问题")
    
    def _update_generation_status(self, user_email: str, user_open_id: str,
                                  status: int, error_message: str = None):
        """更新生成状态"""
        status_map = {
            0: RecommendationStatus.PENDING,
            1: RecommendationStatus.GENERATING,
            2: RecommendationStatus.COMPLETED,
            3: RecommendationStatus.FAILED,
        }
        target_status = status_map.get(status, RecommendationStatus.PENDING)
        self.repo.update_status(user_email, user_open_id, target_status, error_message)
    
    def _get_default_frequent_queries(self) -> List[str]:
        """获取默认常用问题（当无法分析时使用）"""
        return [
            "最近7天华东区域的销售数据如何？",
            "本月我的最佳销售商品是什么？",
            "客户回购率前10名的商品有哪些？",
            "最近一周新增客户数量统计",
            "核心客户的消费趋势分析",
            "热销商品库存预警查询",
            "销售团队本月业绩排名",
            "本季度GMV增长情况",
            "客户流失情况分析",
            "高价值客户维护建议"
        ]
    
    def get_offline_recommendations(self, user_email: str, count: int = 6) -> List[str]:
        """
        获取用户的离线推荐/常用问题列表
        """
        try:
            rec = self.repo.find_by_user_email(user_email)
            if rec and rec.is_valid():
                return rec.get_recommendations_subset(count)
            return []
        except Exception:
            return []
    
    def get_query_analytics(self, user_email: str) -> Dict[str, Any]:
        """
        获取用户的查询分析数据
        """
        try:
            queries = get_user_latest_queries(user_email=user_email, limit=100)
            analysis_data = self.query_summarizer.analyze_query_patterns(queries)
            return {**analysis_data, 'last_analyzed': datetime.now().isoformat()}
        except Exception:
            return {}

    # 向后兼容的别名方法
    def get_user_frequent_queries(self, user_email: str, count: int = 6) -> List[str]:
        """向后兼容的方法名"""
        return self.get_offline_recommendations(user_email, count)
    
    def _generate_recommendations_for_user(self, user_info: Dict[str, str]):
        """向后兼容的方法名，实际调用新的总结生成"""
        return self._generate_summary_for_user(user_info)