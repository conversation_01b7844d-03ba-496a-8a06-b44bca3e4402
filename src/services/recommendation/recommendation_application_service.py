"""
推荐应用服务

基于DDD架构的推荐服务应用层，负责协调领域服务和仓储层。
该服务作为应用层，处理外部请求并调用领域服务完成业务逻辑。
"""

from typing import List, Dict, Any, Optional

from src.services.domain.recommendation_domain_service import RecommendationDomainService
from src.repositories.chatbi.recommendation import ChatbiRecommendationRepository
from src.models.recommendation import ActiveUser, RecommendationStatus
from src.utils.logger import logger


class RecommendationApplicationService:
    """
    推荐应用服务
    
    遵循DDD架构，作为应用层协调各个组件：
    - 处理来自API层的请求
    - 调用领域服务执行业务逻辑
    - 管理事务边界
    - 处理异常和日志
    """
    
    def __init__(self):
        """初始化推荐应用服务"""
        # 依赖注入：仓储层
        self.recommendation_repository = ChatbiRecommendationRepository()
        
        # 依赖注入：领域服务
        self.recommendation_domain_service = RecommendationDomainService(
            self.recommendation_repository
        )
        
        logger.info("推荐应用服务初始化完成")
    
    def get_user_offline_recommendations(self, user_email: str, count: int = 6) -> List[str]:
        """
        获取用户的离线推荐问题
        
        Args:
            user_email: 用户邮箱
            count: 返回推荐数量，默认6个
            
        Returns:
            List[str]: 推荐问题列表
        """
        if not user_email or not user_email.strip():
            logger.warning("获取推荐时用户邮箱不能为空")
            return []
        
        if count <= 0:
            logger.warning("推荐数量必须大于0")
            return []
        
        try:
            # 验证邮箱格式
            if not self.recommendation_domain_service.validate_user_email(user_email):
                logger.warning(f"用户邮箱格式无效: {user_email}")
                return []
            
            # 调用领域服务获取推荐
            recommendations = self.recommendation_domain_service.get_user_recommendations(
                user_email.strip(), 
                count
            )
            
            logger.info(f"为用户 {user_email} 返回 {len(recommendations)} 条离线推荐")
            return recommendations
            
        except Exception as e:
            logger.exception(f"获取用户离线推荐失败: {user_email}, 错误: {e}", exc_info=True)
            return []
    
    def run_daily_recommendation_task(self, days: int = 30) -> Dict[str, Any]:
        """
        执行每日推荐生成任务
        
        Args:
            days: 活跃用户的天数范围，默认30天
            
        Returns:
            Dict[str, Any]: 任务执行结果
        """
        logger.info(f"开始执行每日离线推荐任务，活跃用户范围: 最近 {days} 天")
        
        start_time = logger.info("任务开始时间记录")
        
        try:
            # 调用领域服务执行批量生成
            result = self.recommendation_domain_service.generate_recommendations_for_all_active_users(days)
            
            # 添加任务元信息
            result.update({
                "task_type": "daily_recommendation",
                "active_days_range": days,
                "start_time": start_time,
                "end_time": logger.info("任务结束时间记录"),
                "status": "completed" if result.get("failed", 0) == 0 else "partial_success"
            })
            
            logger.info(f"每日推荐任务完成: {result}")
            return result
            
        except Exception as e:
            error_result = {
                "task_type": "daily_recommendation",
                "active_days_range": days,
                "start_time": start_time,
                "end_time": logger.info("任务异常结束时间记录"),
                "status": "failed",
                "error": str(e),
                "total": 0,
                "success": 0,
                "failed": 0
            }
            
            logger.exception(f"每日推荐任务执行失败: {e}", exc_info=True)
            return error_result
    
    def generate_recommendation_for_single_user(self, user_email: str, user_open_id: str, user_name: str) -> bool:
        """
        为单个用户生成推荐
        
        Args:
            user_email: 用户邮箱
            user_open_id: 用户open_id
            user_name: 用户姓名
            
        Returns:
            bool: 是否生成成功
        """
        if not all([user_email, user_open_id, user_name]):
            logger.warning("生成推荐时用户信息不完整")
            return False
        
        try:
            # 验证邮箱格式
            if not self.recommendation_domain_service.validate_user_email(user_email):
                logger.warning(f"用户邮箱格式无效: {user_email}")
                return False
            
            # 构建活跃用户对象
            user = ActiveUser(
                email=user_email.strip(),
                open_id=user_open_id.strip(),
                name=user_name.strip()
            )
            
            # 调用领域服务生成推荐
            success = self.recommendation_domain_service.generate_recommendations_for_user(user)
            
            if success:
                logger.info(f"为用户 {user_email} 成功生成推荐")
            else:
                logger.warning(f"为用户 {user_email} 生成推荐失败")
            
            return success
            
        except Exception as e:
            logger.exception(f"为用户 {user_email} 生成推荐时出错: {e}", exc_info=True)
            return False
    
    def get_recommendation_service_status(self) -> Dict[str, Any]:
        """
        获取推荐服务状态信息
        
        Returns:
            Dict[str, Any]: 服务状态信息
        """
        try:
            # 获取领域服务统计信息
            domain_stats = self.recommendation_domain_service.get_recommendation_statistics()
            
            # 添加应用层信息
            status = {
                "application_service": "RecommendationApplicationService",
                "domain_service": "RecommendationDomainService", 
                "repository": "ChatbiRecommendationRepository",
                "architecture": "DDD",
                **domain_stats
            }
            
            logger.info("获取推荐服务状态成功")
            return status
            
        except Exception as e:
            logger.exception(f"获取推荐服务状态失败: {e}", exc_info=True)
            return {
                "status": "error",
                "error": str(e),
                "architecture": "DDD"
            }
    
    def update_user_recommendation_status(self, user_email: str, user_open_id: str, 
                                        status: str, error_message: Optional[str] = None) -> bool:
        """
        更新用户推荐状态
        
        Args:
            user_email: 用户邮箱
            user_open_id: 用户open_id
            status: 状态字符串 ('pending', 'generating', 'completed', 'failed')
            error_message: 错误信息（可选）
            
        Returns:
            bool: 是否更新成功
        """
        if not all([user_email, user_open_id, status]):
            logger.warning("更新推荐状态时参数不完整")
            return False
        
        try:
            # 转换状态字符串为枚举
            status_mapping = {
                'pending': RecommendationStatus.PENDING,
                'generating': RecommendationStatus.GENERATING,
                'completed': RecommendationStatus.COMPLETED,
                'failed': RecommendationStatus.FAILED
            }
            
            recommendation_status = status_mapping.get(status.lower())
            if not recommendation_status:
                logger.warning(f"无效的推荐状态: {status}")
                return False
            
            # 调用仓储层更新状态
            success = self.recommendation_repository.update_status(
                user_email.strip(),
                user_open_id.strip(),
                recommendation_status,
                error_message
            )
            
            if success:
                logger.info(f"成功更新用户 {user_email} 推荐状态为: {status}")
            else:
                logger.warning(f"更新用户 {user_email} 推荐状态失败")
            
            return success
            
        except Exception as e:
            logger.exception(f"更新用户推荐状态时出错: {user_email}, 状态: {status}, 错误: {e}", exc_info=True)
            return False
    
    def get_active_users_count(self, days: int = 30) -> int:
        """
        获取活跃用户数量
        
        Args:
            days: 活跃用户的天数范围
            
        Returns:
            int: 活跃用户数量
        """
        try:
            active_users = self.recommendation_repository.find_active_users(days)
            count = len(active_users)
            logger.info(f"最近 {days} 天活跃用户数量: {count}")
            return count
            
        except Exception as e:
            logger.exception(f"获取活跃用户数量失败: {e}", exc_info=True)
            return 0
    
    def validate_recommendation_data_integrity(self) -> Dict[str, Any]:
        """
        验证推荐数据完整性（可用于健康检查）
        
        Returns:
            Dict[str, Any]: 验证结果
        """
        try:
            # 这里可以添加数据完整性检查逻辑
            # 比如检查过期推荐、状态异常的记录等
            
            result = {
                "validation_status": "passed",
                "checks_performed": [
                    "repository_connection",
                    "domain_service_initialization"
                ],
                "timestamp": logger.info("数据完整性验证时间"),
                "issues_found": []
            }
            
            logger.info("推荐数据完整性验证通过")
            return result
            
        except Exception as e:
            logger.exception(f"推荐数据完整性验证失败: {e}", exc_info=True)
            return {
                "validation_status": "failed",
                "error": str(e),
                "timestamp": logger.info("验证失败时间")
            }
