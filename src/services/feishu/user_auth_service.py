import os
from src.services.feishu.message_core import send_message_to_chat
from src.services.feishu.token_service import TokenService
from src.utils.logger import logger

class UserAuthService:
    """用户授权服务类，负责处理用户授权相关逻辑"""
    
    @staticmethod
    def check_user_token_status(user_id: str) -> bool:
        """检查用户token状态
        
        Args:
            user_id: 用户ID（这里应该是open_id）
            
        Returns:
            bool: True表示用户有有效token，False表示没有
        """
        try:
            if not user_id:
                logger.warning("检查用户token状态失败: user_id 为空")
                return False
                
            logger.debug(f"开始检查用户token状态: user_id={user_id}")
            
            # 使用TokenService获取用户访问token
            token = TokenService.get_user_access_token(user_id)
            
            if token is None:
                logger.warning(f"用户没有有效token: user_id={user_id}, token=None")
                return False
            elif token.strip() == "":
                logger.warning(f"用户token为空字符串: user_id={user_id}")
                return False
            else:
                logger.info(f"用户有有效token: user_id={user_id}, token_length={len(token)}")
                return True
                
        except Exception as e:
            logger.exception(f"检查用户token状态时出错: user_id={user_id}, error={e}", exc_info=True)
            return False
    
    @staticmethod
    def has_recent_message_within_hour(user_id: str) -> bool:
        """检查用户最近一小时内是否有消息交互
        
        Args:
            user_id: 用户ID
            
        Returns:
            bool: True表示有最近消息，False表示没有
        """
        # 暂时返回False，后续可以根据需要实现具体逻辑
        return False
    
    @staticmethod
    def send_auth_reminder(chat_id: str, username:str) -> bool:
        """发送授权提醒消息
        
        Args:
            chat_id: 聊天会话ID
            
        Returns:
            bool: True表示发送成功，False表示发送失败
        """
        try:
            import json
            
            if not chat_id:
                logger.exception("发送授权提醒失败: chat_id 为空")
                return False
            
            # 构造飞书交互式卡片消息，使用完整的飞书卡片格式（参考推荐卡片的实现）
            hostname = os.getenv("CHAT_BI_HOST_NAME", "https://chat-bi.summerfarm.net")
            logger.info(f"使用授权链接主机名: {hostname}")
            
            card_content = {
                "schema": "2.0",
                "header": {
                    "template": "blue",
                    "title": {
                        "content": "🔐 授权提醒",
                        "tag": "lark_md"
                    }
                },
                "body": {
                    "elements": [
                        {
                            "tag": "markdown",
                            "content": f"**您好，{username}，为了提供更好的服务，我们需要您授权飞书权限**\n\n授权完成后，ChatBI才能为您提供导出数据到多维表格、搜索您的文档库进行回答等高级功能。如果您不授权，您将无法查看超过20条数据。\n\n**即刻授权**: [🔗 点击此处立即授权]({hostname}/session/ensure-login)"
                        }
                    ]
                }
            }
            
            # 将卡片内容转为JSON字符串，并指定msg_type为interactive
            card_json = json.dumps(card_content)
            logger.info(f"准备发送授权提醒卡片: {card_json}")
            
            result = send_message_to_chat(chat_id, card_json, msg_type="interactive")
            if result:
                logger.info(f"成功发送授权提醒到聊天 {chat_id}")
                return True
            else:
                logger.exception(f"发送授权提醒失败，send_message_to_chat 返回 False，聊天 {chat_id}")
                return False
                
        except Exception as e:
            logger.exception(f"发送授权提醒时出错: {e}", exc_info=True)
            return False
    
    @classmethod
    def should_send_auth_reminder(cls, user_id: str) -> bool:
        """判断是否应该发送授权提醒
        
        Args:
            user_id: 用户ID
            
        Returns:
            bool: True表示需要发送授权提醒，False表示不需要
        """
        try:
            # 检查用户token状态
            has_valid_token = cls.check_user_token_status(user_id)
            
            # 如果用户没有有效token，直接返回True，表示需要发送授权提醒
            if not has_valid_token:
                logger.info(f"用户 {user_id} 没有有效token，需要发送授权提醒")
                return True
            
            # 如果用户有有效token，则不需要发送授权提醒
            logger.info(f"用户 {user_id} 有有效token，无需发送授权提醒")
            return False
            
        except Exception as e:
            logger.exception(f"判断是否发送授权提醒时出错: {e}")
            return False