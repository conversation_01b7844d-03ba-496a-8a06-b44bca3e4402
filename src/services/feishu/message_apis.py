"""
飞书消息API统一入口模块
重构后的模块化设计，提供所有飞书消息相关功能的统一接口
"""

# 从各个子模块导入功能
from .message_content import MessageContent, get_message_content, get_message_image_content
from .message_core import (
    send_feishu_message,
    reply_simple_text_message,
    reply_interactive_message,
    send_message_to_chat,
    lark_client  # 导入lark_client以保持向后兼容
)
from .card_operations import (
    get_update_footnote,
    get_markdown_post_object,
    initial_card_message,
    send_updates_to_card,
    after_badcase_mark,
    after_goodcase_mark,
    send_finished_message_to_card,
    THINKING_ELEMENT_ID,
    FINAL_ELEMENT_ID,
    FOOTNOTE_ELEMENT_ID,
    FEEDBACK_ELEMENT_ID,
    GOOD_CASE_ELEMENT_ID
)
from .notification_sender import (
    send_case_notification,
    send_bad_case_notification,
    send_bad_case_unmark_notification,
    send_good_case_notification,
    send_good_case_unmark_notification,
    send_recommendation_card
)

# 为了向后兼容，保留一些旧的别名
_reply_interactive_message = reply_interactive_message

# 导出所有公共接口
__all__ = [
    # 消息内容相关
    'MessageContent',
    'get_message_content',
    'get_message_image_content',

    # 核心消息发送
    'send_feishu_message',
    'reply_simple_text_message',
    'reply_interactive_message',
    'send_message_to_chat',
    'lark_client',  # 向后兼容的lark客户端

    # 卡片操作
    'get_update_footnote',
    'get_markdown_post_object',
    'initial_card_message',
    'send_updates_to_card',
    'after_badcase_mark',
    'after_goodcase_mark',
    'send_finished_message_to_card',

    # 卡片元素ID常量
    'THINKING_ELEMENT_ID',
    'FINAL_ELEMENT_ID',
    'FOOTNOTE_ELEMENT_ID',
    'FEEDBACK_ELEMENT_ID',
    'GOOD_CASE_ELEMENT_ID',

    # 通知发送
    'send_case_notification',
    'send_bad_case_notification',
    'send_bad_case_unmark_notification',
    'send_good_case_notification',
    'send_good_case_unmark_notification',
    'send_recommendation_card',

    # 向后兼容别名
    '_reply_interactive_message'
]