"""
飞书客户端主模块
负责启动飞书WebSocket连接和事件分发
"""
import lark_oapi as lark

from src.utils.logger import logger
from src.services.auth.user_login_with_feishu import APP_ID, APP_SECRET

# Import refactored modules
from .event_handlers import create_event_handler

def start_feishu_client():
    """启动飞书客户端WebSocket连接"""
    logger.info(f"启动飞书客户端WebSocket连接:{APP_ID}")

    # 创建事件处理器
    event_handler = create_event_handler()

    # 创建并启动客户端
    cli = lark.ws.Client(
        APP_ID, APP_SECRET, event_handler=event_handler, log_level=lark.LogLevel.INFO
    )
    cli.start()


if __name__ == "__main__":
    start_feishu_client()
