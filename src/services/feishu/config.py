"""
飞书服务配置管理模块
负责管理飞书相关的配置常量和环境变量
"""
import os
class FeishuConfig:
    """飞书服务配置类"""

    # 主机名配置
    HOST_NAME = os.getenv("CHAT_BI_HOST_NAME", "https://chat-bi.summerfarm.net")

    # 消息处理配置
    MESSAGE_STEPS = int(os.getenv("MESSAGE_STEPS", 15))  # 每次发送消息到飞书时的步长(字符数)

    # 流式响应超时配置
    STREAM_TIMEOUT_MINUTES = float(os.getenv("STREAM_TIMEOUT_MINUTES", 10))

    # 消息去重配置
    MESSAGE_CLEANUP_THRESHOLD = int(os.getenv("MESSAGE_CLEANUP_THRESHOLD", 1000))  # 消息去重清理阈值
    MESSAGE_CLEANUP_CUTOFF_HOURS = int(os.getenv("MESSAGE_CLEANUP_CUTOFF_HOURS", 1))  # 消息去重清理时间（小时）

    # 消息验证配置
    MIN_QUERY_LENGTH = int(os.getenv("MIN_QUERY_LENGTH", 5))  # 最小查询长度

    # Bad Case通知配置
    BAD_CASE_NOTIFICATION_CHAT_ID = os.getenv("BAD_CASE_NOTIFICATION_CHAT_ID")

    # 机器人配置
    BOT_NAME = os.getenv("FEISHU_BOT_NAME", "ChatBI测试")  # 机器人名称，用于群聊@检测

    @classmethod
    def get_host_name(cls) -> str:
        """获取主机名"""
        return cls.HOST_NAME

    @classmethod
    def get_message_steps(cls) -> int:
        """获取消息步长"""
        return cls.MESSAGE_STEPS

    @classmethod
    def get_stream_timeout_minutes(cls) -> float:
        """获取流式响应超时时间（分钟）"""
        return cls.STREAM_TIMEOUT_MINUTES

    @classmethod
    def get_cleanup_config(cls) -> tuple[int, int]:
        """获取清理配置 (清理阈值, 清理时间)"""
        return cls.MESSAGE_CLEANUP_THRESHOLD, cls.MESSAGE_CLEANUP_CUTOFF_HOURS

    @classmethod
    def get_min_query_length(cls) -> int:
        """获取最小查询长度"""
        return cls.MIN_QUERY_LENGTH

    @classmethod
    def get_bad_case_notification_chat_id(cls) -> str:
        """获取Bad Case通知群聊ID"""
        return cls.BAD_CASE_NOTIFICATION_CHAT_ID

    @classmethod
    def get_bot_name(cls) -> str:
        """获取机器人名称"""
        return cls.BOT_NAME
