"""
XianmuDB query execution service module.

This module provides business logic for executing queries against the XianmuDB business database.
"""
import asyncio
import concurrent.futures
from typing import Union
from src.db.query import execute_sql_query
from src.utils.logger import logger
from src.db.database_enum import Database
from src.models.query_result import SQLQueryResult
from src.config.concurrency_config import ConcurrencyConfig

# 为数据库查询创建专用线程池，避免阻塞主事件循环
_DB_QUERY_EXECUTOR = concurrent.futures.ThreadPoolExecutor(
    max_workers=ConcurrencyConfig.DB_QUERY_THREAD_POOL_SIZE,
    thread_name_prefix="db_query_worker"
)

def execute_business_query(sql_query: str) -> SQLQueryResult:
    """
    执行XianmuDB业务数据库的SQL查询。

    该函数专用于查询业务数据库（xianmudb），
    并针对业务查询增加了日志和错误处理。
    会自动禁止 delete/update/insert/truncate 等写操作，仅允许只读查询。

    参数:
        sql_query (str): 要执行的SQL查询语句

    返回:
        SQLQueryResult: 查询结果对象
    """

    # 检查是否包含危险操作，仅禁止 delete/update/insert/truncate 等写操作
    # 只匹配以这些关键字开头的语句，避免误伤如 'select ... where col like "delete%"'
    sql_lower = sql_query.lower().lstrip()
    forbidden_keywords = ['delete', 'update', 'insert', 'truncate']
    if any(sql_lower.startswith(kw) for kw in forbidden_keywords):
        error_msg = "禁止执行 delete/update/insert/truncate 等写操作"
        logger.warning(f"{error_msg}: {sql_query}")
        return SQLQueryResult(
            columns=None,
            data=None,
            error=error_msg,
            success=False
        )

    try:
        # 执行业务数据库查询
        result = execute_sql_query(sql_query, database=Database.BUSINESS)

        # 记录查询结果
        if result.success:
            row_count = len(result.data) if result.data else 0
            logger.info(f"业务查询执行成功，返回 {row_count} 行")
        else:
            logger.warning(f"业务查询失败: {result.error}")

        return result

    except Exception as e:
        error_msg = f"执行业务查询出错: {str(e)}"
        logger.exception(error_msg, exc_info=True)
        return SQLQueryResult(
            columns=None,
            data=None,
            error=error_msg,
            success=False
        )


def execute_database_query(sql_query: str, database: Union[str, Database] = Database.BUSINESS) -> SQLQueryResult:
    """
    执行指定数据库的SQL查询。

    该函数支持查询不同的数据库（business、logical_dw、chatbi），
    并针对查询增加了日志和错误处理。
    会自动禁止 delete/update/insert/truncate 等写操作，仅允许只读查询。

    参数:
        sql_query (str): 要执行的SQL查询语句
        database (Union[str, Database]): 要查询的数据库，可以是字符串或Database枚举

    返回:
        SQLQueryResult: 查询结果对象
    """
    # 转换字符串到枚举
    if isinstance(database, str):
        database_map = {
            "business": Database.BUSINESS,
            "logical_dw": Database.LOGICAL_DW,
            "chatbi": Database.CHATBI
        }
        if database.lower() in database_map:
            database = database_map[database.lower()]
        else:
            logger.warning(f"未知的数据库类型: {database}, 使用默认的 BUSINESS 数据库")
            database = Database.BUSINESS

    # 检查是否包含危险操作，仅禁止 delete/update/insert/truncate 等写操作
    sql_lower = sql_query.lower().lstrip()
    forbidden_keywords = ['delete', 'update', 'insert', 'truncate']
    if any(sql_lower.startswith(kw) for kw in forbidden_keywords):
        error_msg = "禁止执行 delete/update/insert/truncate 等写操作"
        logger.warning(f"{error_msg}: {sql_query}")
        return SQLQueryResult(
            columns=None,
            data=None,
            error=error_msg,
            success=False
        )

    try:
        # 执行指定数据库查询
        return execute_sql_query(sql_query, database=database)

    except Exception as e:
        error_msg = f"执行 {database} 数据库查询出错: {str(e)}"
        logger.exception(error_msg, exc_info=True)
        return SQLQueryResult(
            columns=None,
            data=None,
            error=error_msg,
            success=False
        )


async def execute_database_query_async(sql_query: str, database: Union[str, Database] = Database.BUSINESS) -> SQLQueryResult:
    """
    异步执行指定数据库查询，避免阻塞事件循环。

    这个函数将同步的数据库查询操作放在专用线程池中执行，
    从而避免阻塞主事件循环，支持多用户并发查询。

    Args:
        sql_query (str): 要执行的SQL查询语句
        database (Union[str, Database]): 要查询的数据库

    Returns:
        SQLQueryResult: 查询结果
    """
    logger.debug(f"异步执行 {database} 数据库查询: {sql_query}")

    try:
        # 在专用线程池中执行同步查询，避免阻塞事件循环
        loop = asyncio.get_event_loop()
        result = await loop.run_in_executor(
            _DB_QUERY_EXECUTOR,
            execute_database_query,
            sql_query,
            database
        )

        return result

    except Exception as e:
        error_msg = f"SQL查询出错: {str(e)}\n\nSQL: {sql_query}"
        logger.exception(error_msg)
        return SQLQueryResult(
            columns=None,
            data=None,
            error=error_msg,
            success=False
        )


async def execute_business_query_async(sql_query: str) -> SQLQueryResult:
    """
    异步执行业务数据库查询，避免阻塞事件循环。

    这个函数是为了向后兼容而保留的，内部调用通用的 execute_database_query_async。

    Args:
        sql_query (str): 要执行的SQL查询语句

    Returns:
        SQLQueryResult: 查询结果
    """
    return await execute_database_query_async(sql_query, Database.BUSINESS)
