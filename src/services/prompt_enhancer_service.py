"""
智能提示词增强服务

使用 OpenAI API 智能优化用户的查询提示词，让 ChatBI 更准确地理解用户意图。
"""

import os
import yaml
from typing import Dict, Any
from datetime import datetime

from src.utils.logger import logger
from src.utils.resource_manager import load_resource
from src.services.agent.utils.model_provider import get_claude_model


class PromptEnhancerService:
    """
    智能提示词增强服务
    
    核心功能：
    1. 加载 ChatBI 系统能力信息
    2. 获取用户上下文（角色、历史查询）
    3. 使用 Claude Sonnet 4 智能优化提示词
    """
    
    def __init__(self, user_email: str, user_name: str, user_job_title: str = ""):
        """
        初始化提示词增强服务
        
        Args:
            user_email: 用户邮箱
            user_name: 用户姓名
            user_job_title: 用户职位
        """
        self.user_email = user_email
        self.user_name = user_name
        self.user_job_title = user_job_title
        
        # 初始化 OpenAI 客户端（通过 OpenRouter）
        self.model = get_claude_model()
        
        logger.info(f"初始化提示词增强服务，用户: {user_name} ({user_email})")
    
    def enhance_prompt(self, original_prompt: str) -> str:
        """
        增强用户的提示词
        
        Args:
            original_prompt: 用户的原始提示词
            
        Returns:
            str: 增强后的提示词
            
        Raises:
            Exception: 当增强失败时抛出异常
        """
        try:
            # 1. 加载 ChatBI 系统能力
            chatbi_capabilities = self._load_chatbi_capabilities()
            
            # 2. 获取用户上下文
            user_context = self._get_user_context()
            
            # 3. 构建系统提示词
            system_prompt = self._build_system_prompt(chatbi_capabilities, user_context)
            
            # 4. 调用 OpenAI API 进行增强
            enhanced_prompt = self._call_openai_api(system_prompt, original_prompt)
            
            return enhanced_prompt
            
        except Exception as e:
            logger.exception(f"提示词增强失败: {e}", exc_info=True)
            raise Exception(f"提示词增强失败: {str(e)}")
    
    def _load_chatbi_capabilities(self) -> Dict[str, Any]:
        """
        加载 ChatBI 系统能力信息
        
        Returns:
            Dict: 包含 agents、business_concepts、query_patterns 等信息
        """
        try:
            capabilities = {
                "agents": {},
                "business_concepts": {},
                "query_patterns": []
            }
            
            # 加载 Agent 配置
            config_dir = "resources/data_fetcher_bot_config"
            if os.path.exists(config_dir):
                for filename in os.listdir(config_dir):
                    if filename.endswith('.yml'):
                        config_path = os.path.join(config_dir, filename)
                        try:
                            with open(config_path, 'r', encoding='utf-8') as f:
                                config = yaml.safe_load(f)
                                agent_name = config.get('agent_name')
                                if agent_name:
                                    capabilities["agents"][agent_name] = {
                                        "model": config.get('model', ''),
                                        "tools": config.get('tools', []),
                                        "agent_tools": config.get('agent_tools', []),
                                        "description": config.get('agent_description', '')
                                    }
                        except Exception as e:
                            logger.warning(f"加载 Agent 配置失败 {filename}: {e}")
            
            # 加载 Agent 描述文件
            prompt_dir = "resources/prompt"
            if os.path.exists(prompt_dir):
                for filename in os.listdir(prompt_dir):
                    if filename.endswith('.md'):
                        agent_name = filename.replace('.md', '')
                        try:
                            content = load_resource("prompt", filename)
                            if content and agent_name in capabilities["agents"]:
                                capabilities["agents"][agent_name]["detailed_description"] = content[:2000]  # 限制长度
                        except Exception as e:
                            logger.warning(f"加载 Agent 描述失败 {filename}: {e}")
            
            # 提取业务概念
            capabilities["business_concepts"] = self._extract_business_concepts()
            
            # 提取查询模式
            capabilities["query_patterns"] = self._extract_query_patterns()
            
            logger.info(f"成功加载 ChatBI 能力信息，包含 {len(capabilities['agents'])} 个 Agent")
            return capabilities
            
        except Exception as e:
            logger.exception(f"加载 ChatBI 系统能力失败: {e}")
            return {"agents": {}, "business_concepts": {}, "query_patterns": []}
    
    def _get_user_context(self) -> Dict[str, Any]:
        """
        获取用户上下文信息

        Returns:
            Dict: 包含用户角色等信息
        """
        try:
            context = {
                "user_role": self._determine_user_role(),
                "current_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }

            return context

        except Exception as e:
            logger.warning(f"获取用户上下文失败: {e}")
            return {
                "user_role": "普通用户",
                "current_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
    
    def _determine_user_role(self) -> str:
        """
        根据用户职位确定角色类型
        
        Returns:
            str: 用户角色描述
        """
        job_title = self.user_job_title.lower()
        
        if any(keyword in job_title for keyword in ['bd', '业务', '销售']):
            return "销售代表/BD"
        elif any(keyword in job_title for keyword in ['经理', 'manager', '主管']):
            return "销售经理/主管"
        elif any(keyword in job_title for keyword in ['仓储', '物流', '库存']):
            return "仓储物流人员"
        elif any(keyword in job_title for keyword in ['分析', 'analyst', '数据']):
            return "数据分析师"
        else:
            return "普通用户"
    


    def _extract_business_concepts(self) -> str:
        """
        从 Agent 描述文件中提取核心业务概念（只提取关键术语定义）

        Returns:
            str: 格式化的业务概念描述
        """
        try:
            business_concepts = []

            # 从 prompt 目录提取核心业务概念
            prompt_dir = "resources/prompt"
            if os.path.exists(prompt_dir):
                for filename in os.listdir(prompt_dir):
                    if filename.endswith('.md') and filename != 'prompt_enhancer.md':
                        try:
                            content = load_resource("prompt", filename)
                            if content:
                                # 只提取背景知识段落中的关键定义
                                lines = content.split('\n')
                                concept_lines = []

                                for i, line in enumerate(lines):
                                    # 查找背景知识段落
                                    if '背景知识' in line or '业务概念' in line:
                                        # 提取该段落的关键定义（只要包含定义符号的行）
                                        for j in range(i + 1, min(i + 20, len(lines))):
                                            next_line = lines[j]
                                            # 如果遇到新的大标题则停止
                                            if next_line.startswith('##'):
                                                break
                                            # 只保留包含定义的行
                                            if any(keyword in next_line for keyword in ['**', '定义', '：', '指']):
                                                concept_lines.append(next_line.strip())
                                        break

                                if concept_lines:
                                    agent_name = filename.replace('.md', '')
                                    # 只保留前5个最重要的定义
                                    key_concepts = [line for line in concept_lines if len(line) > 10][:5]
                                    if key_concepts:
                                        business_concepts.append(f"**{agent_name}**：\n" + '\n'.join(key_concepts))
                        except Exception as e:
                            logger.warning(f"读取 {filename} 失败: {e}")

            return "\n\n".join(business_concepts) if business_concepts else "暂无业务概念定义"

        except Exception as e:
            logger.exception(f"提取业务概念失败: {e}")
            return "业务概念提取失败"

    def _extract_query_patterns(self) -> str:
        """
        从 Agent 描述文件中提取查询模式（参考 coordinator_bot 的做法）

        Returns:
            str: 格式化的查询模式描述
        """
        try:
            query_patterns = []

            # 从 prompt 目录提取适用场景
            prompt_dir = "resources/prompt"
            if os.path.exists(prompt_dir):
                for filename in os.listdir(prompt_dir):
                    if filename.endswith('.md') and filename != 'prompt_enhancer.md':
                        try:
                            content = load_resource("prompt", filename)
                            if content:
                                # 只提取适用场景的列表项
                                lines = content.split('\n')
                                scenario_items = []

                                for i, line in enumerate(lines):
                                    # 查找适用场景段落
                                    if '适用场景' in line:
                                        # 提取后续的列表项
                                        for j in range(i + 1, min(i + 15, len(lines))):
                                            next_line = lines[j].strip()
                                            # 如果遇到新的标题则停止
                                            if next_line.startswith('##') or next_line.startswith('**'):
                                                break
                                            # 只保留列表项
                                            if next_line.startswith('-') and len(next_line) > 5:
                                                scenario_items.append(next_line)
                                        break

                                if scenario_items:
                                    agent_name = filename.replace('.md', '')
                                    # 只保留前6个最重要的场景
                                    key_scenarios = scenario_items[:6]
                                    query_patterns.append(f"**{agent_name}**：\n" + '\n'.join(key_scenarios))
                        except Exception as e:
                            logger.warning(f"读取 {filename} 失败: {e}")

            return "\n\n".join(query_patterns) if query_patterns else "暂无查询模式示例"

        except Exception as e:
            logger.exception(f"提取查询模式失败: {e}")
            return "查询模式提取失败"

    def _build_system_prompt(self, capabilities: Dict[str, Any], user_context: Dict[str, Any]) -> str:
        """
        构建动态系统提示词

        Args:
            capabilities: ChatBI 系统能力信息
            user_context: 用户上下文信息

        Returns:
            str: 完整的系统提示词
        """
        try:
            # 从文件加载系统提示词模板
            prompt_template = load_resource("prompt", "prompt_enhancer.md")

            if not prompt_template:
                logger.warning("无法加载提示词模板，使用默认模板")
                return self._get_fallback_system_prompt(capabilities, user_context)

            # 格式化 Agent 能力描述
            agents_desc = self._format_agents_description(capabilities.get("agents", {}))

            # 获取业务概念和查询模式（已经是格式化的字符串）
            concepts_desc = capabilities.get("business_concepts", "暂无业务概念定义")
            patterns_desc = capabilities.get("query_patterns", "暂无查询模式示例")

            # 格式化用户上下文
            user_desc = f"""用户信息：
- 姓名：{self.user_name}
- 角色：{user_context.get('user_role', '普通用户')}"""

            # 格式化当前日期
            from datetime import datetime
            import locale

            try:
                # 设置中文locale（如果可用）
                locale.setlocale(locale.LC_TIME, 'zh_CN.UTF-8')
            except (locale.Error, OSError):
                pass  # 如果设置失败，使用默认locale

            now = datetime.now()
            current_date = f"""今天是 {now.strftime('%Y年%m月%d日')} {now.strftime('%A')}
当前时间：{now.strftime('%H:%M:%S')}"""

            # 替换模板中的占位符
            system_prompt = prompt_template.format(
                agents_desc=agents_desc,
                concepts_desc=concepts_desc,
                patterns_desc=patterns_desc,
                user_desc=user_desc,
                current_date=current_date
            )

            return system_prompt

        except Exception as e:
            logger.exception(f"构建系统提示词失败: {e}")
            return self._get_fallback_system_prompt(capabilities, user_context)

    def _get_fallback_system_prompt(self, capabilities: Dict[str, Any] = None, user_context: Dict[str, Any] = None) -> str:
        """
        获取备用的系统提示词（当文件加载失败时使用）

        Returns:
            str: 简单的备用系统提示词
        """
        return """# ChatBI 智能提示词增强器

你是 ChatBI 的智能提示词增强器，专门优化用户的查询以获得更准确的结果。

## 任务
分析用户的原始查询，理解其真实意图，然后生成一个优化的提示词，让 ChatBI 能够准确理解用户需求并返回正确结果。

## 优化原则
1. 补充缺失的上下文信息
2. 明确查询意图
3. 使用标准业务术语
4. 考虑用户角色特点

## 输出要求
直接输出优化后的提示词，不要解释过程。确保优化后的提示词更加具体、明确，包含必要的业务上下文。"""

    def _format_agents_description(self, agents: Dict[str, Any]) -> str:
        """
        格式化 Agent 能力描述（参考 coordinator_bot 的简洁做法）

        Args:
            agents: Agent 配置信息

        Returns:
            str: 格式化的 Agent 描述
        """
        if not agents:
            return "暂无可用的分析工具"

        descriptions = []
        for agent_name, config in agents.items():
            # 优先使用 agent_as_tool_description 的第一段作为描述
            desc = ""

            if 'agent_as_tool_description' in config and config['agent_as_tool_description']:
                tool_desc = config['agent_as_tool_description'].strip()
                # 提取第一段作为简要描述
                first_paragraph = tool_desc.split('\n\n')[0].split('\n')[0]
                if len(first_paragraph) > 20:
                    desc = first_paragraph[:120]

            # 如果没有工具描述，从详细描述中提取核心职责
            if not desc and config.get('detailed_description'):
                content = config['detailed_description']
                lines = content.split('\n')
                for line in lines[:5]:  # 只看前5行
                    if '核心职责' in line or '专注于' in line:
                        # 找到下一行的描述
                        idx = lines.index(line)
                        if idx + 1 < len(lines):
                            desc = lines[idx + 1].strip('- *#').strip()[:100]
                            break

            # 备用描述
            if not desc:
                desc = "专业分析工具"

            descriptions.append(f"**{agent_name}**：{desc}")

        return "\n".join(descriptions)



    def _call_openai_api(self, system_prompt: str, user_prompt: str) -> str:
        """
        调用 OpenAI API 进行提示词增强

        Args:
            system_prompt: 系统提示词
            user_prompt: 用户原始提示词

        Returns:
            str: 增强后的提示词

        Raises:
            Exception: 当 API 调用失败时抛出异常
        """
        try:
            import openai
            from src.services.agent.utils.model_provider import get_provider_config

            # 获取 OpenRouter 配置
            config = get_provider_config("openrouter")

            # 创建 OpenAI 客户端
            client = openai.OpenAI(
                api_key=config["api_key"],
                base_url=config["api_base"]
            )

            # 构建消息
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": f"请优化以下提示词：{user_prompt}"}
            ]

            # 调用 API
            response = client.chat.completions.create(
                model="anthropic/claude-sonnet-4",
                messages=messages,
                temperature=0.2,
                max_tokens=1000,
                timeout=30
            )

            # 提取结果
            enhanced_prompt = response.choices[0].message.content.strip()

            if not enhanced_prompt:
                raise Exception("API 返回空结果")

            return enhanced_prompt

        except Exception as e:
            logger.exception(f"OpenAI API 调用失败: {e}")
            raise Exception(f"AI 服务调用失败: {str(e)}")
