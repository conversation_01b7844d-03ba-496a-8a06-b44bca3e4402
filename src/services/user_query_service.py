"""
用户查询应用服务模块

提供通过用户姓名或邮箱查询 open_id 和相关用户信息的功能
遵循DDD架构模式：API层 → 应用服务 → 领域服务 → 仓储层 → 数据库
"""

from typing import Dict, Any, Optional, List
from src.services.domain.user_domain_service import UserDomainService
from src.repositories.chatbi.user import ChatbiUserRepository
from src.models.user_info_class import User
from src.utils.logger import logger


class UserQueryService:
    """用户查询应用服务类 - 协调领域服务和仓储层"""

    def __init__(self):
        """初始化应用服务，注入依赖"""
        self._user_repository = ChatbiUserRepository()
        self._user_domain_service = UserDomainService(self._user_repository)

    def _convert_user_to_dict(self, user: User) -> Dict[str, str]:
        """将用户实体转换为字典格式，保持向后兼容"""
        return user.to_dict()

    @staticmethod
    def get_user_info_by_username(username: str) -> Optional[Dict[str, str]]:
        """
        根据用户名从user表获取用户信息

        Args:
            username: 用户姓名

        Returns:
            Dict包含用户信息，或None表示未找到
        """
        service = UserQueryService()
        user = service._user_domain_service.find_user_by_username(username)
        return service._convert_user_to_dict(user) if user else None

    @staticmethod
    def get_user_info_by_email(email: str) -> Optional[Dict[str, str]]:
        """
        根据邮箱地址获取用户信息

        Args:
            email: 用户邮箱地址

        Returns:
            Dict包含用户信息，或None表示未找到
        """
        service = UserQueryService()
        user = service._user_domain_service.find_user_by_email(email)
        return service._convert_user_to_dict(user) if user else None

    @staticmethod
    def search_users(search_term: str, limit: int = 5) -> List[Dict[str, str]]:
        """
        模糊搜索用户信息

        Args:
            search_term: 搜索关键词（姓名或邮箱）
            limit: 限制返回结果数量

        Returns:
            List[Dict[str, str]]: 用户列表
        """
        service = UserQueryService()
        users = service._user_domain_service.search_users(search_term, limit)
        return [service._convert_user_to_dict(user) for user in users]

    @staticmethod
    def get_open_id_by_user_query(input_str: str) -> Optional[Dict[str, str]]:
        """
        根据输入字符串查询用户信息（支持姓名或邮箱）

        Args:
            input_str: 可以是姓名或邮箱地址

        Returns:
            Dict包含用户信息，或None表示未找到
        """
        service = UserQueryService()
        user = service._user_domain_service.find_user_by_query(input_str)
        return service._convert_user_to_dict(user) if user else None

    @staticmethod
    def list_all_users(limit: int = 10) -> List[Dict[str, str]]:
        """
        获取所有用户列表（用于调试）

        Args:
            limit: 限制返回数量

        Returns:
            List[Dict[str, str]]: 用户列表
        """
        service = UserQueryService()
        users = service._user_domain_service.list_users(limit)
        return [service._convert_user_to_dict(user) for user in users]