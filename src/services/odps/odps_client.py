import pandas as pd
import os
import time
from odps import ODPS
from src.utils.logger import logger
from src.models.query_result import SQLQueryResult

ALIBABA_CLOUD_ACCESS_KEY_ID = os.environ["ALIBABA_CLOUD_ACCESS_KEY_ID"]
ALIBABA_CLOUD_ACCESS_KEY_SECRET = os.environ["ALIBABA_CLOUD_ACCESS_KEY_SECRET"]

pd.set_option("display.max_rows", None)  # Set to None to display all rows
pd.set_option("display.max_columns", None)  # Set to None to display all columns
pd.set_option("display.width", None)  # Use to set the display width for wrapping
pd.set_option(
    "display.max_colwidth", None
)  # Use to set the maximum width of each column

MAX_CSV_CELL_COUNT=int(os.environ.get("MAX_CSV_CELL_COUNT", 500_0000)) # 飞书电子表格最大单元格数500万，多维表格最大行数2万

odps = ODPS(
    ALIBABA_CLOUD_ACCESS_KEY_ID,
    ALIBABA_CLOUD_ACCESS_KEY_SECRET,
    project="summerfarm_tech",
    endpoint="http://service.cn-hangzhou.maxcompute.aliyun.com/api",
)

hints = {"odps.sql.hive.compatible": True, "odps.sql.type.system.odps2": True}

def get_odps_sql_result_as_df(sql: str) -> pd.DataFrame:
    instance = odps.execute_sql(sql, hints=hints)
    instance.wait_for_success()
    pd_df = None
    with instance.open_reader(tunnel=True, limit=False) as reader:
        # type of pd_df is pandas DataFrame
        pd_df = reader.to_pandas()

    if pd_df is not None:
        logger.info(f"odps SQL query result:\n{sql}\ncolumns:{pd_df.columns}, data size:{pd_df.shape}")
        return pd_df
    return None

def get_odps_sql_result_as_sqlqueryresult(sql: str) -> SQLQueryResult:
    try:
        logger.info(f"Executing odps SQL query: {sql}")
        if "DESCRIBE" in sql.strip().upper():
            # 处理describe语句
            instance = odps.execute_sql(sql, hints=hints)
            instance.wait_for_success()
            with instance.open_reader(tunnel=True, limit=False) as reader:
                table_description = reader.raw
                return SQLQueryResult.success_result(["table_description"], [table_description])

        start_time = time.time()
        pd_df = get_odps_sql_result_as_df(sql)
        if pd_df is not None:
            time_spend=int(time.time() - start_time)
            warning_msg=None
            if pd_df.shape[0] * pd_df.shape[1] > MAX_CSV_CELL_COUNT:
                warning_msg=f"ODPS返回的数据超过 {MAX_CSV_CELL_COUNT} 单元格限制，已截断到 {MAX_CSV_CELL_COUNT} 单元格，请注意数据量"
                logger.warning(warning_msg)
                pd_df = pd_df.iloc[:MAX_CSV_CELL_COUNT // pd_df.shape[1]-1]
            result = SQLQueryResult.success_result(pd_df.columns.tolist(), pd_df.values.tolist(), time_spend=time_spend)
            if warning_msg is not None:
                result.error = warning_msg
            return result
        else:
            return SQLQueryResult.failed_result(f"No data returned from odps, SQL: {sql}")
    except Exception as e:
        logger.exception(f"Failed to execute odps SQL query: {e}, sql: {sql}")
        return SQLQueryResult.failed_result(f"Error executing odps SQL query: {e}, SQL: {sql}")
