"""
资源上传API，用于将图片或其他文件上传到七牛云。
"""
import secrets
import string
from typing import Dict, Any, Optional

import requests
from flask import request, jsonify, Blueprint, session
from werkzeug.datastructures import FileStorage

from src.services.auth.user_login_with_feishu import login_required
from src.utils.logger import logger
from src.utils.user_utils import get_api_token


# 创建资源API蓝图
resource_bp = Blueprint('resource', __name__, url_prefix='')

# 七牛云相关配置
QINIU_UPLOAD_TOKEN_URL = "https://admin.summerfarm.net/qiniu/upload-token/one"
QINIU_UPLOAD_URL = "https://up-z0.qiniup.com/"

# 支持的文件类型和对应的扩展名
ALLOWED_EXTENSIONS = {
    'image/jpeg': '.jpeg',
    'image/jpg': '.jpg', 
    'image/png': '.png',
    'image/gif': '.gif',
    'image/webp': '.webp',
    'application/pdf': '.pdf',
    'text/plain': '.txt',
    'application/msword': '.doc',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': '.docx',
    'application/vnd.ms-excel': '.xls',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': '.xlsx'
}

# 最大文件大小 (10MB)
MAX_FILE_SIZE = 10 * 1024 * 1024


def generate_random_filename(extension: str) -> str:
    """
    生成随机文件名。
    
    Args:
        extension: 文件扩展名（包含点号，如 '.jpeg'）
        
    Returns:
        完整的文件路径，格式为 chatbi-resource/{32位随机字符}{扩展名}
    """
    # 生成32位随机字母和数字
    random_chars = ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(32))
    return f"chatbi-resource/{random_chars}{extension}"


def get_file_extension(content_type: str, filename: str) -> Optional[str]:
    """
    根据文件的Content-Type和文件名获取文件扩展名。
    
    Args:
        content_type: 文件的MIME类型
        filename: 原始文件名
        
    Returns:
        文件扩展名（包含点号），如果不支持则返回None
    """
    # 首先尝试从Content-Type获取
    if content_type in ALLOWED_EXTENSIONS:
        return ALLOWED_EXTENSIONS[content_type]
    
    # 如果Content-Type不匹配，尝试从文件名获取
    if filename and '.' in filename:
        file_ext = '.' + filename.rsplit('.', 1)[1].lower()
        # 检查是否在支持的扩展名列表中
        for ext in ALLOWED_EXTENSIONS.values():
            if ext == file_ext:
                return file_ext
    
    return None


def get_qiniu_upload_token(filename: str, user_token: str) -> Dict[str, Any]:
    """
    从七牛云获取上传token。

    Args:
        filename: 要上传的文件名（包含路径）
        user_token: 用户的summerfarm_api_token

    Returns:
        包含token和key的字典，或错误信息
    """
    try:
        headers = {
            'accept': 'application/json, text/plain, */*',
            'content-type': 'application/x-www-form-urlencoded',
            'token': user_token
        }
        
        # URL编码文件名
        import urllib.parse
        encoded_filename = urllib.parse.quote(filename, safe='')
        data = f'fileName={encoded_filename}'
        
        logger.info(f"请求七牛云上传token，文件名: {filename}")
        
        response = requests.post(QINIU_UPLOAD_TOKEN_URL, headers=headers, data=data, timeout=10)
        response.raise_for_status()
        
        result = response.json()
        logger.info(f"七牛云token响应: {result}")
        
        if result.get('success') and result.get('code') == 'SUCCESS':
            return {
                'success': True,
                'data': result['data']
            }
        else:
            return {
                'success': False,
                'error': f"获取上传token失败: {result.get('msg', '未知错误')}"
            }
            
    except requests.exceptions.RequestException as e:
        logger.exception(f"请求七牛云token失败: {e}")
        return {
            'success': False,
            'error': f"网络请求失败: {str(e)}"
        }
    except Exception as e:
        logger.exception(f"获取七牛云token时发生未知错误: {e}")
        return {
            'success': False,
            'error': f"获取token时发生错误: {str(e)}"
        }


def upload_file_to_qiniu(file_data: bytes, token: str, key: str, content_type: str) -> Dict[str, Any]:
    """
    将文件上传到七牛云。
    
    Args:
        file_data: 文件二进制数据
        token: 七牛云上传token
        key: 文件在七牛云中的key
        content_type: 文件的MIME类型
        
    Returns:
        上传结果字典
    """
    try:
        # 构建multipart/form-data请求
        files = {
            'file': ('blob', file_data, content_type),
            'token': (None, token),
            'key': (None, key)
        }
        
        headers = {
            'accept': 'application/json, text/plain, */*',
            'origin': 'https://admin.summerfarm.net',
            'referer': 'https://admin.summerfarm.net/'
        }
        
        logger.info(f"开始上传文件到七牛云，key: {key}")
        
        response = requests.post(QINIU_UPLOAD_URL, files=files, headers=headers, timeout=30)
        response.raise_for_status()
        
        result = response.json()
        logger.info(f"七牛云上传响应: {result}")
        
        return {
            'success': True,
            'data': result
        }
        
    except requests.exceptions.RequestException as e:
        logger.exception(f"上传文件到七牛云失败: {e}")
        return {
            'success': False,
            'error': f"上传失败: {str(e)}"
        }
    except Exception as e:
        logger.exception(f"上传文件时发生未知错误: {e}")
        return {
            'success': False,
            'error': f"上传时发生错误: {str(e)}"
        }


@resource_bp.route('/api/upload', methods=['POST'])
@login_required
def upload_resource():
    """
    上传资源文件到七牛云。
    
    接收multipart/form-data格式的文件上传请求，
    将文件上传到七牛云并返回文件URL。
    """
    user_info = session.get("user_info")
    username = user_info.get("name") if user_info else "unknown"
    logger.info(f"用户 {username} 开始上传文件")
    union_id = user_info.get("union_id") if user_info else None
    summerfarm_api_token = get_api_token(union_id=union_id)

    if not summerfarm_api_token:
        logger.exception(f"用户 {username} 没有有效的summerfarm_api_token")
        return jsonify({"error": "用户认证token无效，请重新登录"}), 401

    try:
        # 检查是否有文件
        if 'file' not in request.files:
            return jsonify({"error": "没有找到文件字段"}), 400
        
        file: FileStorage = request.files['file']
        
        # 检查文件是否为空
        if file.filename == '':
            return jsonify({"error": "没有选择文件"}), 400
        
        # 读取文件数据
        file_data = file.read()
        
        # 检查文件大小
        if len(file_data) > MAX_FILE_SIZE:
            return jsonify({"error": f"文件大小超过限制 ({MAX_FILE_SIZE // (1024*1024)}MB)"}), 400
        
        # 获取文件类型和扩展名
        content_type = file.content_type or 'application/octet-stream'
        extension = get_file_extension(content_type, file.filename)
        
        if not extension:
            return jsonify({"error": f"不支持的文件类型: {content_type}"}), 400
        
        # 生成随机文件名
        filename = generate_random_filename(extension)
        
        logger.info(f"用户 {username} 开始上传文件: {file.filename} -> {filename}, 大小: {len(file_data)} bytes")

        # 第一步：获取七牛云上传token
        token_result = get_qiniu_upload_token(filename, summerfarm_api_token)
        if not token_result['success']:
            return jsonify({"error": token_result['error']}), 500
        
        token_data = token_result['data']
        upload_token = token_data['token']
        upload_key = token_data['key']
        
        # 第二步：上传文件到七牛云
        upload_result = upload_file_to_qiniu(file_data, upload_token, upload_key, content_type)
        if not upload_result['success']:
            return jsonify({"error": upload_result['error']}), 500
        
        # 构建文件URL（假设七牛云域名）
        file_url = f"https://azure.summerfarm.net/{upload_key}"
        
        logger.info(f"用户 {username} 文件上传成功: {filename} -> {file_url}")
        
        return jsonify({
            "success": True,
            "data": {
                "filename": filename,
                "key": upload_key,
                "url": file_url,
                "size": len(file_data),
                "content_type": content_type
            }
        })
        
    except Exception as e:
        logger.exception(f"用户 {username} 上传文件时发生错误: {e}")
        return jsonify({"error": f"上传失败: {str(e)}"}), 500
