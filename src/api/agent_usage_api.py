"""
Agent使用情况分析API

提供RESTful API接口来查询和分析agent使用情况，
支持统计和可视化数据的获取。
"""

from flask import Blueprint, request, jsonify
from typing import Optional
from datetime import datetime, timedelta

from src.services.agent.agent_usage_analysis import (
    get_agent_usage_statistics,
    get_user_agent_usage_pattern,
    get_agent_performance_metrics
)
from src.utils.logger import logger

# 创建蓝图
agent_usage_bp = Blueprint('agent_usage', __name__, url_prefix='/api/agent-usage')


@agent_usage_bp.route('/statistics', methods=['GET'])
def get_usage_statistics():
    """
    获取agent使用统计信息
    
    Query Parameters:
        - start_time: 开始时间戳（毫秒）
        - end_time: 结束时间戳（毫秒）
        - username: 用户名过滤
        - email: 邮箱过滤
        - limit: 返回结果数量限制（默认100）
        - days: 最近N天的数据（如果未指定start_time和end_time）
    """
    try:
        # 获取查询参数
        start_time = request.args.get('start_time', type=int)
        end_time = request.args.get('end_time', type=int)
        username = request.args.get('username')
        email = request.args.get('email')
        limit = request.args.get('limit', default=100, type=int)
        days = request.args.get('days', default=30, type=int)
        
        # 如果没有指定时间范围，使用最近N天
        if not start_time and not end_time:
            end_time = int(datetime.now().timestamp() * 1000)
            start_time = int((datetime.now() - timedelta(days=days)).timestamp() * 1000)
        
        # 获取统计数据
        stats = get_agent_usage_statistics(
            start_time=start_time,
            end_time=end_time,
            username=username,
            email=email,
            limit=limit
        )
        
        return jsonify({
            "success": True,
            "data": stats
        })
        
    except Exception as e:
        logger.exception(f"获取agent使用统计失败: {e}", exc_info=True)
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500


@agent_usage_bp.route('/user-pattern', methods=['GET'])
def get_user_pattern():
    """
    获取特定用户的agent使用模式
    
    Query Parameters:
        - username: 用户名（必需）
        - email: 用户邮箱（必需）
        - days: 分析的天数（默认30天）
    """
    try:
        username = request.args.get('username')
        email = request.args.get('email')
        days = request.args.get('days', default=30, type=int)
        
        if not username or not email:
            return jsonify({
                "success": False,
                "error": "username和email参数是必需的"
            }), 400
        
        # 获取用户使用模式
        pattern = get_user_agent_usage_pattern(username, email, days)
        
        return jsonify({
            "success": True,
            "data": pattern
        })
        
    except Exception as e:
        logger.exception(f"获取用户agent使用模式失败: {e}", exc_info=True)
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500


@agent_usage_bp.route('/agent-performance', methods=['GET'])
def get_agent_performance():
    """
    获取特定agent的性能指标
    
    Query Parameters:
        - agent_name: agent名称（必需）
        - days: 分析的天数（默认30天）
    """
    try:
        agent_name = request.args.get('agent_name')
        days = request.args.get('days', default=30, type=int)
        
        if not agent_name:
            return jsonify({
                "success": False,
                "error": "agent_name参数是必需的"
            }), 400
        
        # 获取agent性能指标
        metrics = get_agent_performance_metrics(agent_name, days)
        
        return jsonify({
            "success": True,
            "data": metrics
        })
        
    except Exception as e:
        logger.exception(f"获取agent性能指标失败: {e}", exc_info=True)
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500


@agent_usage_bp.route('/summary', methods=['GET'])
def get_usage_summary():
    """
    获取agent使用情况摘要
    
    Query Parameters:
        - days: 分析的天数（默认7天）
    """
    try:
        days = request.args.get('days', default=7, type=int)
        
        # 计算时间范围
        end_time = int(datetime.now().timestamp() * 1000)
        start_time = int((datetime.now() - timedelta(days=days)).timestamp() * 1000)
        
        # 获取基础统计
        stats = get_agent_usage_statistics(
            start_time=start_time,
            end_time=end_time,
            limit=10
        )
        
        # 构建摘要数据
        summary = {
            "time_range": {
                "days": days,
                "start_time": start_time,
                "end_time": end_time
            },
            "overview": stats.get("basic_stats", {}),
            "top_agents": stats.get("agent_frequency", [])[:5],
            "agent_combinations": {
                "single_agent_queries": stats.get("agent_combinations", {}).get("single_agent_queries", 0),
                "multi_agent_queries": stats.get("agent_combinations", {}).get("multi_agent_queries", 0),
                "top_combinations": stats.get("agent_combinations", {}).get("top_combinations", [])[:3]
            },
            "recent_trend": stats.get("time_trend", [])[:7]
        }
        
        return jsonify({
            "success": True,
            "data": summary
        })
        
    except Exception as e:
        logger.exception(f"获取agent使用摘要失败: {e}", exc_info=True)
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500


@agent_usage_bp.route('/health', methods=['GET'])
def health_check():
    """健康检查端点"""
    return jsonify({
        "success": True,
        "message": "Agent Usage API is running",
        "timestamp": int(datetime.now().timestamp() * 1000)
    })


# 错误处理
@agent_usage_bp.errorhandler(404)
def not_found(error):
    return jsonify({
        "success": False,
        "error": "API端点未找到"
    }), 404


@agent_usage_bp.errorhandler(500)
def internal_error(error):
    return jsonify({
        "success": False,
        "error": "内部服务器错误"
    }), 500
