"""
任务调度管理API接口模块。
提供离线推荐任务的管理和控制接口。
"""

from flask import request, jsonify, Blueprint
from src.services.scheduler.offline_task_scheduler import (
    get_scheduler, get_status, trigger_immediate_job, reschedule_job
)
from src.services.auth.user_login_with_feishu import login_required
from src.utils.logger import logger
from src.services.dashboard.dashboard_service import admin_required

# 创建调度管理API的Blueprint
scheduler_bp = Blueprint('scheduler', __name__, url_prefix='/api')


@scheduler_bp.route('/scheduler/status', methods=['GET'])
@login_required
def get_scheduler_status():
    """
    GET /api/scheduler/status 接口，获取任务调度器状态
    
    返回:
        {
            "success": true,
            "status": {
                "running": bool,
                "timezone": str,
                "jobs": int,
                "daily_job": {
                    "exists": bool,
                    "next_run_time": str
                }
            }
        }
    """
    try:
        status_info = get_status()
        return jsonify({
            "success": True,
            "status": status_info
        })
    except Exception as e:
        logger.exception(f"获取调度器状态失败: {e}")
        return jsonify({
            "success": False,
            "error": "获取调度器状态失败"
        }), 500


@scheduler_bp.route('/scheduler/trigger', methods=['POST'])
@login_required
@admin_required
def trigger_scheduler():
    """
    POST /api/scheduler/trigger 接口，手动触发一次推荐任务
    
    需要管理员权限
    
    返回:
        {
            "success": true,
            "message": "任务已触发",
            "job_id": "job_id"
        }
    """
    try:
        
        job_id = trigger_immediate_job()
        
        logger.info(f"管理员手动触发了推荐任务，job_id: {job_id}")
        
        return jsonify({
            "success": True,
            "message": "推荐任务已触发",
            "job_id": job_id
        })
        
    except Exception as e:
        logger.exception(f"手动触发推荐任务失败: {e}")
        return jsonify({
            "success": False,
            "error": "触发任务失败"
        }), 500


@scheduler_bp.route('/scheduler/reschedule', methods=['POST'])
@login_required
@admin_required
def reschedule_scheduler():
    """
    POST /api/scheduler/reschedule 接口，重新安排任务执行间隔
    
    需要管理员权限
    请求体:
        {
            "hours": int,  # 多少小时执行一次，默认24
            "minute": int   # 多少分钟开始，默认0
        }
    
    返回:
        {
            "success": true,
            "message": "任务已重新安排",
            "next_run_time": str
        }
    """
    try:
        
        # 获取参数
        data = request.get_json() or {}
        hours = int(data.get('hours', 24))
        minute = int(data.get('minute', 0))
        
        # 调整任务
        scheduler = get_scheduler()
        scheduler.reschedule_job(hours=hours, minute=minute)
        
        status = scheduler.get_scheduler_status()
        next_run_time = status['daily_job']['next_run_time']
        
        logger.info(f"管理员重新安排了推荐任务: 每{hours}小时执行，下次执行时间: {next_run_time}")
        
        return jsonify({
            "success": True,
            "message": f"推荐任务已重新安排为每{hours}小时执行",
            "next_run_time": next_run_time
        })
        
    except ValueError as e:
        return jsonify({
            "success": False,
            "error": "参数格式错误，hours和minute必须为整数"
        }), 400
    except Exception as e:
        logger.exception(f"重新安排任务失败: {e}")
        return jsonify({
            "success": False,
            "error": "重新安排任务失败"
        }), 500


# === 部门Top10常问清单周度分析调度器API ===

@scheduler_bp.route('/scheduler/weekly-analysis/status', methods=['GET'])
@login_required
@admin_required
def get_weekly_analysis_status():
    """
    GET /api/scheduler/weekly-analysis/status 获取周度分析调度器状态

    Returns:
        JSON response with scheduler status
    """
    try:
        from src.services.scheduler.weekly_top_questions_scheduler import get_weekly_scheduler_status

        status = get_weekly_scheduler_status()

        return jsonify({
            "success": True,
            "status": status
        })

    except Exception as e:
        logger.exception(f"获取周度分析调度器状态失败: {e}")
        return jsonify({
            "success": False,
            "error": "获取调度器状态失败"
        }), 500


@scheduler_bp.route('/scheduler/weekly-analysis/trigger', methods=['POST'])
@login_required
@admin_required
def trigger_weekly_analysis():
    """
    POST /api/scheduler/weekly-analysis/trigger 手动触发周度分析

    Request body:
        {
            "target_week_start": "YYYY-MM-DD" (optional, 目标周的开始日期，默认为上周一)
        }

    Returns:
        JSON response with task ID
    """
    try:
        from src.services.scheduler.weekly_top_questions_scheduler import trigger_manual_weekly_analysis
        from datetime import datetime

        # 解析请求参数
        data = request.get_json() or {}
        target_week_start_str = data.get('target_week_start')

        target_week_start = None
        if target_week_start_str:
            try:
                target_week_start = datetime.strptime(target_week_start_str, '%Y-%m-%d').date()
                # 验证是否为周一
                if target_week_start.weekday() != 0:
                    return jsonify({
                        "success": False,
                        "error": "目标日期必须是周一"
                    }), 400
            except ValueError:
                return jsonify({
                    "success": False,
                    "error": "日期格式错误，应为YYYY-MM-DD"
                }), 400

        # 触发分析任务
        task_id = trigger_manual_weekly_analysis(target_week_start)

        logger.info(f"手动触发周度分析成功，任务ID: {task_id}")
        return jsonify({
            "success": True,
            "task_id": task_id,
            "message": "周度分析任务已触发"
        })

    except Exception as e:
        logger.exception(f"手动触发周度分析失败: {e}")
        return jsonify({
            "success": False,
            "error": "触发分析任务失败"
        }), 500


@scheduler_bp.route('/scheduler/weekly-analysis/trigger-department', methods=['POST'])
@login_required
@admin_required
def trigger_department_analysis():
    """
    POST /api/scheduler/weekly-analysis/trigger-department 手动触发单个部门分析

    Request body:
        {
            "department_name": "部门名称" (required),
            "target_week_start": "YYYY-MM-DD" (optional, 目标周的开始日期，默认为上周一)
        }

    Returns:
        JSON response with task ID
    """
    try:
        from src.services.scheduler.weekly_top_questions_scheduler import trigger_manual_department_analysis
        from datetime import datetime

        # 解析请求参数
        data = request.get_json() or {}
        department_name = data.get('department_name')
        target_week_start_str = data.get('target_week_start')

        # 验证必需参数
        if not department_name:
            return jsonify({
                "success": False,
                "error": "部门名称不能为空"
            }), 400

        target_week_start = None
        if target_week_start_str:
            try:
                target_week_start = datetime.strptime(target_week_start_str, '%Y-%m-%d').date()
                # 验证是否为周一
                if target_week_start.weekday() != 0:
                    return jsonify({
                        "success": False,
                        "error": "目标日期必须是周一"
                    }), 400
            except ValueError:
                return jsonify({
                    "success": False,
                    "error": "日期格式错误，应为YYYY-MM-DD"
                }), 400

        # 触发部门分析任务
        task_id = trigger_manual_department_analysis(department_name, target_week_start)

        logger.info(f"手动触发部门分析成功，部门: {department_name}, 任务ID: {task_id}")
        return jsonify({
            "success": True,
            "task_id": task_id,
            "message": f"部门 {department_name} 的分析任务已触发"
        })

    except Exception as e:
        logger.exception(f"手动触发部门分析失败: {e}")
        return jsonify({
            "success": False,
            "error": "触发部门分析任务失败"
        }), 500