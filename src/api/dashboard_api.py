"""
API endpoints for the admin dashboard.

This module defines the Flask routes for the admin dashboard, including
endpoints for retrieving logs, statistics, and managing bad cases.
"""
from flask import request, jsonify, session, Blueprint

from services.auth.user_login_with_feishu import login_required
from src.services.chatbot import bad_case_service
from src.services.dashboard import dashboard_service
from src.services.dashboard.dashboard_service import admin_required
from src.services.agent.agent_service import get_available_agents
from src.utils.logger import logger

# Create a Blueprint for dashboard endpoints
# Using url_prefix='' to maintain original URL paths
dashboard_bp = Blueprint('dashboard', __name__, url_prefix='')


@dashboard_bp.route('/api/dashboard/logs')
@login_required
@admin_required
def get_logs():
    """
    GET /api/dashboard/logs endpoint that gets filtered chatbot conversations grouped by conversation_id.

    This endpoint returns conversations grouped by conversation_id, similar to the history API,
    but with additional dashboard-specific fields like bad case status, username, etc.

    Query parameters:
        username (str, optional): Filter by username
        email (str, optional): Filter by email
        start_date (str, optional): Filter by start date (YYYY-MM-DD)
        end_date (str, optional): Filter by end date (YYYY-MM-DD)
        content_search (str, optional): Search in message content
        conversation_id (str, optional): Filter by conversation ID
        agent (str, optional): Filter by agent name
        first_level_department (str, optional): Filter by first level department
        limit (int, optional): Maximum number of conversations to return (default: 20)
        offset (int, optional): Number of conversations to skip (default: 0)
        bad_case_filter (str, optional): Filter by bad case status ('only_bad', 'only_good', 'all')
        repair_status_filter (int, optional): Filter by repair status (0=未修复, 1=已修复, 2=暂不修复)
        filter_admin (bool, optional): Whether to filter out admin users (default: false)
    """
    try:
        # Extract filter parameters from request
        filters = {
            'username': request.args.get('username'),
            'email': request.args.get('email'),
            'role': request.args.get('role'),
            'start_date': request.args.get('start_date'),
            'end_date': request.args.get('end_date'),
            'content_search': request.args.get('content_search'),
            'conversation_id': request.args.get('conversation_id'),
            'agent': request.args.get('agent'),  # Filter for agent
            'first_level_department': request.args.get('first_level_department'),  # Filter for department
            'bad_case_filter': request.args.get('bad_case_filter'),  # Filter for bad cases
            'repair_status_filter': request.args.get('repair_status_filter'),  # Filter for repair status
            'filter_admin': request.args.get('filter_admin', 'false').lower() == 'true',
        }

        # Remove None values
        filters = {k: v for k, v in filters.items() if v is not None}

        # Convert repair_status_filter to integer if provided
        if 'repair_status_filter' in filters:
            try:
                filters['repair_status_filter'] = int(filters['repair_status_filter'])
                if filters['repair_status_filter'] not in [0, 1, 2]:
                    return jsonify({"error": "Invalid repair_status_filter. Must be 0, 1, or 2."}), 400
            except ValueError:
                return jsonify({"error": "Invalid repair_status_filter parameter. Must be an integer."}), 400

        # Get pagination parameters from query string, with defaults
        try:
            limit = int(request.args.get('limit', 20))  # Default limit 20
            offset = int(request.args.get('offset', 0))  # Default offset 0
        except ValueError:
            return jsonify({"error": "Invalid limit or offset parameter. Must be integers."}), 400

        if limit <= 0 or offset < 0:
            return jsonify({"error": "Limit must be positive, offset must be non-negative."}), 400

        logger.debug(f"Dashboard API: Getting conversations with filters: {filters}, limit: {limit}, offset: {offset}")

        # Get total conversation count with these filters
        total_count = dashboard_service.get_filtered_conversation_count(filters)

        # Get paginated conversations
        conversations = dashboard_service.get_conversations_for_dashboard(filters, limit, offset)

        return jsonify({
            'conversations': conversations,
            'total_count': total_count
        })
    except Exception as e:
        logger.exception(f"Error getting dashboard logs: {e}", exc_info=True)
        return jsonify({"error": "Failed to load dashboard logs"}), 500


@dashboard_bp.route('/api/dashboard/mark_bad_case', methods=['POST'])
@login_required
@admin_required
def mark_bad_case():
    """
    POST /api/dashboard/mark_bad_case endpoint that marks a conversation as a bad case.
    """
    data = request.get_json()
    if not data or 'conversation_id' not in data:
        return jsonify({'error': 'Missing conversation_id'}), 400

    conversation_id = data['conversation_id']
    is_bad_case = data.get('is_bad_case', True)
    username = data.get('username')
    if not username:  # Basic check, might need email too depending on dashboard_apis implementation
        return jsonify({'error': 'Missing username in request data'}), 400

    try:
        admin_name = session.get('user_info', {}).get('name', 'Admin')
        success = bad_case_service.mark_bad_case(conversation_id, is_bad_case, user_name=admin_name)
        if success:
            logger.info(
                f"Admin {session.get('user_info', {}).get('name')} marked conversation {conversation_id} for user {username} as bad case: {is_bad_case}")
            return jsonify({'status': 'success', 'is_bad_case': is_bad_case})
        else:
            # This could happen if the conversation doesn't exist for the user
            logger.warning(
                f"Admin failed to mark non-existent or unauthorized conversation {conversation_id} as bad case for user {username}")
            return jsonify({
                               'error': 'Failed to mark conversation. It might not exist or belong to this user.'}), 404
    except Exception as e:
        logger.exception(f"Error marking conversation {conversation_id} as bad case by admin: {e}")
        return jsonify({'error': 'Internal server error while marking bad case.'}), 500



@dashboard_bp.route('/api/dashboard/update_repair_status', methods=['POST'])
@login_required
@admin_required
def update_repair_status():
    """
    POST /api/dashboard/update_repair_status endpoint that updates the repair status of a bad case.
    """
    data = request.get_json()
    if not data or 'conversation_id' not in data or 'repair_status' not in data:
        return jsonify({'error': 'Missing conversation_id or repair_status'}), 400

    conversation_id = data['conversation_id']
    repair_status = data['repair_status']
    repair_note = data.get('repair_note')  # 可选的修复说明

    # Validate repair_status
    if repair_status not in [0, 1, 2]:
        return jsonify({'error': 'Invalid repair_status. Must be 0, 1, or 2'}), 400

    try:
        admin_name = session.get('user_info', {}).get('name', 'Admin')
        success, _ = bad_case_service.update_repair_status(
            conversation_id=conversation_id,
            repair_status=repair_status,
            repaired_by=admin_name,  # 传递修复者姓名
            repair_note=repair_note  # 传递修复说明
        )

        if success:
            logger.info(
                f"Admin {admin_name} updated repair status for conversation {conversation_id} to {repair_status}")
            return jsonify({'status': 'success', 'repair_status': repair_status})
        else:
            logger.warning(
                f"Admin failed to update repair status for conversation {conversation_id}")
            return jsonify({
                'error': 'Failed to update repair status. Bad case might not exist.'}), 404
    except Exception as e:
        logger.exception(f"Error updating repair status: {e}", exc_info=True)
        return jsonify({'error': 'Internal server error'}), 500


@dashboard_bp.route('/api/dashboard/users')
@login_required
@admin_required
def get_users():
    """
    GET /api/dashboard/users endpoint that gets a list of unique users.
    """
    logger.debug("Dashboard API: Getting unique users")
    users = dashboard_service.get_unique_users_list()
    return jsonify({'users': users})


@dashboard_bp.route('/api/dashboard/stats')
@login_required
@admin_required
def get_stats():
    """
    GET /api/dashboard/stats endpoint that gets dashboard statistics.

    Query parameters:
        start_time (int, optional): Start timestamp in milliseconds
        end_time (int, optional): End timestamp in milliseconds
        first_level_department (str, optional): Filter by first level department
        filter_admin (bool, optional): Whether to filter out admin users (default: false)
    """
    logger.debug("Dashboard API: Getting stats")
    try:
        # Extract time range parameters
        start_time = request.args.get('start_time')
        end_time = request.args.get('end_time')
        first_level_department = request.args.get('first_level_department')
        filter_admin = request.args.get('filter_admin', 'false').lower() == 'true'

        # Convert string timestamps to integers if provided
        if start_time:
            try:
                start_time = int(start_time)
            except ValueError:
                return jsonify({'error': 'Invalid start_time parameter'}), 400

        if end_time:
            try:
                end_time = int(end_time)
            except ValueError:
                return jsonify({'error': 'Invalid end_time parameter'}), 400

        # Use the updated function from dashboard service to get stats from MySQL
        stats = dashboard_service.get_dashboard_statistics(start_time, end_time, filter_admin, first_level_department)
        logger.debug(f"Dashboard API: Returning stats: {stats}")
        return jsonify(stats)
    except Exception as e:
        logger.exception(f"Error retrieving dashboard stats: {e}")
        return jsonify({'error': 'Failed to retrieve dashboard statistics'}), 500


@dashboard_bp.route('/api/dashboard/daily_usage')
@login_required
@admin_required
def get_daily_usage():
    """
    GET /api/dashboard/daily_usage endpoint that gets daily usage data for charts.

    Query parameters:
        days (int, optional): Number of days to fetch data for (default: 30)
        first_level_department (str, optional): Filter by first level department
    """
    try:
        # Get the days parameter from the request, default to 30 days
        days = request.args.get('days', 30)
        first_level_department = request.args.get('first_level_department')

        try:
            days = int(days)
            if days <= 0:
                days = 30  # Default to 30 if invalid
        except ValueError:
            days = 30  # Default to 30 if not a number

        logger.debug(f"Dashboard API: Getting daily usage data for {days} days")

        # Get the daily usage data
        usage_data = dashboard_service.get_daily_usage_statistics(days, first_level_department)

        return jsonify(usage_data)
    except Exception as e:
        logger.exception(f"Error getting daily usage data: {e}")
        return jsonify({'error': 'Failed to get daily usage data'}), 500


@dashboard_bp.route('/api/dashboard/daily_users')
@login_required
@admin_required
def get_daily_users():
    """
    GET /api/dashboard/daily_users endpoint that gets daily unique user count for charts.

    Query parameters:
        days (int, optional): Number of days to fetch data for (default: 30)
        first_level_department (str, optional): Filter by first level department
    """
    try:
        days = request.args.get('days', 30)
        first_level_department = request.args.get('first_level_department')

        try:
            days = int(days)
            if days <= 0:
                days = 30
        except ValueError:
            days = 30
        logger.debug(f"Dashboard API: Getting daily users data for {days} days")
        users_data = dashboard_service.get_daily_users_statistics(days, first_level_department)
        return jsonify(users_data)
    except Exception as e:
        logger.exception(f"Error getting daily users data: {e}")
        return jsonify({'error': 'Failed to get daily users data'}), 500


@dashboard_bp.route('/api/dashboard/top_users')
@login_required
@admin_required
def get_top_users():
    """
    GET /api/dashboard/top_users endpoint that gets top users by query count.

    Query parameters:
        limit (int, optional): Maximum number of users to return (default: 10)
        start_time (int, optional): Start timestamp in milliseconds
        end_time (int, optional): End timestamp in milliseconds
        first_level_department (str, optional): Filter by first level department
        filter_admin (bool, optional): Whether to filter out admin users (default: false)
    """
    try:
        # Extract parameters
        limit = request.args.get('limit', 10)
        start_time = request.args.get('start_time')
        end_time = request.args.get('end_time')
        first_level_department = request.args.get('first_level_department')
        filter_admin = request.args.get('filter_admin', 'false').lower() == 'true'

        # Convert parameters to appropriate types
        try:
            limit = int(limit)
            if limit <= 0:
                limit = 10  # Default to 10 if invalid
        except ValueError:
            limit = 10  # Default to 10 if not a number

        if start_time:
            try:
                start_time = int(start_time)
            except ValueError:
                return jsonify({'error': 'Invalid start_time parameter'}), 400

        if end_time:
            try:
                end_time = int(end_time)
            except ValueError:
                return jsonify({'error': 'Invalid end_time parameter'}), 400

        logger.debug(f"Dashboard API: Getting top {limit} users with time range [{start_time}, {end_time}] and filter_admin={filter_admin}")

        # Get the top users data
        top_users = dashboard_service.get_top_users_statistics(limit, start_time, end_time, filter_admin, first_level_department)

        return jsonify({'top_users': top_users})
    except Exception as e:
        logger.exception(f"Error getting top users data: {e}")
        return jsonify({'error': 'Failed to get top users data'}), 500


@dashboard_bp.route('/api/dashboard/top_agents')
@login_required
@admin_required
def get_top_agents():
    """
    GET /api/dashboard/top_agents endpoint that gets top agents by conversation count.

    Query parameters:
        limit (int, optional): Maximum number of agents to return (default: 10)
        start_time (int, optional): Start timestamp in milliseconds
        end_time (int, optional): End timestamp in milliseconds
        first_level_department (str, optional): Filter by first level department
        filter_admin (bool, optional): Whether to filter out admin users (default: false)
    """
    try:
        # Extract parameters
        limit = request.args.get('limit', 10)
        start_time = request.args.get('start_time')
        end_time = request.args.get('end_time')
        first_level_department = request.args.get('first_level_department')
        filter_admin = request.args.get('filter_admin', 'false').lower() == 'true'

        # Convert parameters to appropriate types
        try:
            limit = int(limit)
            if limit <= 0:
                limit = 10  # Default to 10 if invalid
        except ValueError:
            limit = 10  # Default to 10 if not a number

        if start_time:
            try:
                start_time = int(start_time)
            except ValueError:
                return jsonify({'error': 'Invalid start_time parameter'}), 400

        if end_time:
            try:
                end_time = int(end_time)
            except ValueError:
                return jsonify({'error': 'Invalid end_time parameter'}), 400

        logger.debug(f"Dashboard API: Getting top {limit} agents with time range [{start_time}, {end_time}] and filter_admin={filter_admin}")

        # Get the top agents data
        top_agents = dashboard_service.get_top_agents_statistics(limit, start_time, end_time, filter_admin, first_level_department)

        return jsonify({'top_agents': top_agents})
    except Exception as e:
        logger.exception(f"Error getting top agents data: {e}")
        return jsonify({'error': 'Failed to get top agents data'}), 500


@dashboard_bp.route('/api/dashboard/agents')
@login_required
@admin_required
def get_agents():
    """
    GET /api/dashboard/agents endpoint that gets all available agents.

    Returns:
        JSON response with list of available agents including their names and descriptions
    """
    try:
        logger.debug("Dashboard API: Getting available agents")

        # Get all available agents
        agents = get_available_agents()

        return jsonify({'agents': agents})
    except Exception as e:
        logger.exception(f"Error getting agents data: {e}")
        return jsonify({'error': 'Failed to get agents data'}), 500


@dashboard_bp.route('/api/dashboard/departments')
@login_required
@admin_required
def get_departments():
    """
    GET /api/dashboard/departments endpoint that gets all unique first-level departments.

    This endpoint returns a list of all unique first-level departments from the user table,
    filtered to exclude null and empty values, sorted alphabetically.

    Returns:
        JSON response with list of department names:
        {
            "departments": ["技术部", "销售部", "市场部", ...]
        }

    Error responses:
        500: Internal server error if department retrieval fails
    """
    try:
        logger.debug("Dashboard API: Getting departments list")

        # Get all departments from the service layer
        departments = dashboard_service.get_departments_list()

        logger.debug(f"Dashboard API: Successfully retrieved {len(departments)} departments")
        return jsonify({'departments': departments})
    except Exception as e:
        logger.exception(f"Error getting departments data: {e}", exc_info=True)
        return jsonify({'error': 'Failed to load departments'}), 500


@dashboard_bp.route('/api/dashboard/department_stats')
@login_required
@admin_required
def get_department_stats():
    """
    GET /api/dashboard/department_stats endpoint that gets conversation statistics by department.

    This endpoint returns conversation count statistics grouped by first-level department,
    supporting time range filtering and admin user filtering.

    Query parameters:
        start_time (int, optional): Start timestamp in milliseconds
        end_time (int, optional): End timestamp in milliseconds
        filter_admin (bool, optional): Whether to filter out admin users (default: false)
        limit (int, optional): Maximum number of departments to return (default: 10)

    Returns:
        JSON response with department statistics:
        {
            "department_stats": [
                {
                    "department_name": "技术部",
                    "conversation_count": 150,
                    "user_query_count": 280
                },
                {
                    "department_name": "销售部",
                    "conversation_count": 120,
                    "user_query_count": 195
                }
            ]
        }

    Error responses:
        400: Bad request if timestamp parameters are invalid
        500: Internal server error if statistics retrieval fails
    """
    try:
        logger.debug("Dashboard API: Getting department conversation statistics")

        # Extract query parameters
        start_time = request.args.get('start_time')
        end_time = request.args.get('end_time')
        filter_admin = request.args.get('filter_admin', 'false').lower() == 'true'
        limit = request.args.get('limit', 10)

        # Convert string timestamps to integers if provided
        if start_time:
            try:
                start_time = int(start_time)
            except ValueError:
                return jsonify({'error': 'Invalid start_time parameter'}), 400

        if end_time:
            try:
                end_time = int(end_time)
            except ValueError:
                return jsonify({'error': 'Invalid end_time parameter'}), 400

        # Convert limit to integer
        try:
            limit = int(limit)
        except ValueError:
            return jsonify({'error': 'Invalid limit parameter'}), 400

        # Get department statistics from service layer
        department_stats = dashboard_service.get_department_conversation_statistics(
            start_time, end_time, filter_admin, limit
        )

        logger.debug(f"Dashboard API: Successfully retrieved statistics for {len(department_stats)} departments")
        return jsonify({'department_stats': department_stats})
    except Exception as e:
        logger.exception(f"Error getting department statistics: {e}", exc_info=True)
        return jsonify({'error': 'Failed to retrieve department statistics'}), 500


# === 部门Top10常问清单相关API ===

@dashboard_bp.route('/api/dashboard/department-top-questions')
@login_required
@admin_required
def get_department_top_questions():
    """
    GET /api/dashboard/department-top-questions 获取部门Top10常问清单

    Query parameters:
        department_name (str, optional): 部门名称筛选
        week_start_date (str, optional): 周开始日期筛选 (YYYY-MM-DD)
        week_end_date (str, optional): 周结束日期筛选 (YYYY-MM-DD)
        analysis_status (int, optional): 分析状态筛选 (0=待分析, 1=分析中, 2=分析完成, 3=分析失败)
        page (int, optional): 页码，默认1
        page_size (int, optional): 页面大小，默认20，最大100

    Returns:
        JSON response with department top questions data
    """
    try:
        from src.repositories.chatbi.department_top_questions import DepartmentTopQuestionsRepository
        from src.models.department_top_questions import DepartmentTopQuestionsQuery, AnalysisStatus
        from datetime import datetime

        # 解析查询参数
        department_name = request.args.get('department_name')
        week_start_date_str = request.args.get('week_start_date')
        week_end_date_str = request.args.get('week_end_date')
        analysis_status_str = request.args.get('analysis_status')
        page = request.args.get('page', 1, type=int)
        page_size = request.args.get('page_size', 20, type=int)

        # 验证参数
        if page < 1:
            return jsonify({'error': '页码必须大于0'}), 400
        if page_size < 1 or page_size > 100:
            return jsonify({'error': '页面大小必须在1-100之间'}), 400

        # 解析日期参数
        week_start_date = None
        week_end_date = None
        if week_start_date_str:
            try:
                week_start_date = datetime.strptime(week_start_date_str, '%Y-%m-%d').date()
            except ValueError:
                return jsonify({'error': '周开始日期格式错误，应为YYYY-MM-DD'}), 400

        if week_end_date_str:
            try:
                week_end_date = datetime.strptime(week_end_date_str, '%Y-%m-%d').date()
            except ValueError:
                return jsonify({'error': '周结束日期格式错误，应为YYYY-MM-DD'}), 400

        # 解析分析状态
        analysis_status = None
        if analysis_status_str:
            try:
                status_value = int(analysis_status_str)
                analysis_status = AnalysisStatus(status_value)
            except (ValueError, TypeError):
                return jsonify({'error': '分析状态参数错误'}), 400

        # 构建查询条件
        query_params = DepartmentTopQuestionsQuery(
            department_name=department_name,
            week_start_date=week_start_date,
            week_end_date=week_end_date,
            analysis_status=analysis_status,
            page=page,
            page_size=page_size
        )

        # 执行查询
        repository = DepartmentTopQuestionsRepository()
        result = repository.query(query_params)

        # 转换为API响应格式
        items = []
        for item in result.items:
            item_data = {
                'id': item.id,
                'department_name': item.department_name,
                'week_start_date': item.week_start_date.isoformat(),
                'week_end_date': item.week_end_date.isoformat(),
                'week_display': item.get_week_display(),
                'total_conversations': item.total_conversations,
                'total_user_queries': item.total_user_queries,
                'analysis_status': item.analysis_status.value,
                'analysis_status_display': item.get_analysis_status_display(),
                'error_message': item.error_message,
                'created_at': item.created_at.isoformat() if item.created_at else None,
                'updated_at': item.updated_at.isoformat() if item.updated_at else None,
                'top_questions': [q.to_dict() for q in item.top_questions]
            }
            items.append(item_data)

        response_data = {
            'items': items,
            'pagination': {
                'total_count': result.total_count,
                'page': result.page,
                'page_size': result.page_size,
                'total_pages': result.total_pages,
                'has_next_page': result.has_next_page(),
                'has_previous_page': result.has_previous_page()
            }
        }

        logger.info(f"Dashboard API: 成功获取部门Top10问题数据，共{result.total_count}条记录")
        return jsonify(response_data)

    except Exception as e:
        logger.exception(f"获取部门Top10问题数据时发生错误: {e}", exc_info=True)
        return jsonify({'error': '获取数据失败'}), 500


@dashboard_bp.route('/api/dashboard/department-top-questions/departments')
@login_required
@admin_required
def get_departments_with_top_questions():
    """
    GET /api/dashboard/department-top-questions/departments 获取有Top10数据的部门列表

    Returns:
        JSON response with department list
    """
    try:
        from src.repositories.chatbi.department_top_questions import DepartmentTopQuestionsRepository

        repository = DepartmentTopQuestionsRepository()
        departments = repository.get_departments_with_data()

        logger.info(f"Dashboard API: 成功获取{len(departments)}个有数据的部门")
        return jsonify({'departments': departments})

    except Exception as e:
        logger.exception(f"获取部门列表时发生错误: {e}", exc_info=True)
        return jsonify({'error': '获取部门列表失败'}), 500


@dashboard_bp.route('/api/dashboard/department-top-questions/<int:record_id>/export')
@login_required
@admin_required
def export_department_top_questions(record_id):
    """
    GET /api/dashboard/department-top-questions/<record_id>/export 导出部门Top10常问清单为Excel

    Args:
        record_id (int): 记录ID

    Returns:
        Excel文件下载响应
    """
    try:
        from src.repositories.chatbi.department_top_questions import DepartmentTopQuestionsRepository
        from src.services.dashboard.department_top_questions_export_service import DepartmentTopQuestionsExportService

        # 获取记录
        repository = DepartmentTopQuestionsRepository()
        record = repository.find_by_id(record_id)

        if not record:
            return jsonify({'error': '记录不存在'}), 404

        # 检查状态是否为已完成
        if record.analysis_status.value != 2:  # 2 = 分析完成
            return jsonify({'error': '只有分析完成的记录才能导出'}), 400

        # 生成Excel文件
        export_service = DepartmentTopQuestionsExportService()
        excel_file = export_service.export_to_excel(record)

        # 生成文件名
        filename = f"{record.department_name}_{record.get_week_display()}_Top10常问清单.xlsx"

        # 返回文件下载响应
        from flask import send_file
        import io

        # 将Excel文件转换为字节流
        output = io.BytesIO()
        excel_file.save(output)
        output.seek(0)

        logger.info(f"成功导出部门Top10常问清单: {filename}")

        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=filename
        )

    except Exception as e:
        logger.error(f"导出部门Top10常问清单时发生错误: {e}", exc_info=True)
        return jsonify({'error': '导出失败'}), 500
