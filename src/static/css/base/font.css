/**
 * Typography System
 *
 * This file centralizes all typography-related styles for the application.
 * Inspired by ChatGPT's typography system with optimizations for Chinese text.
 *
 * The font stack prioritizes:
 * 1. System fonts for optimal performance and native feel
 * 2. Noto Sans SC for excellent Chinese character support
 * 3. Appropriate fallbacks for various operating systems
 */

/* Font Family Variables */
:root {
  /* Primary UI Font - 优化的中文字体栈，优先使用思源黑体和苹方 */
  --font-family-sans: -apple-system, BlinkMacSystemFont, 'Source Han Sans SC', 'Source Han Sans CN', 'Noto Sans SC',
                      'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', 'Segoe UI', Arial, sans-serif;

  /* 衬线字体 - 用于特殊场景，如引用或强调 */
  --font-family-serif: 'Source Han Serif SC', 'Source Han Serif CN', 'Noto Serif SC', 'SimSun', 'STSong', serif;

  /* Monospace Font for Code */
  --font-family-mono: 'JetBrains Mono', 'Fira Code', 'SF Mono', 'Menlo', 'Consolas', 'Source Code Pro', 'PingFang SC', monospace;

  /* Font Weight Variables */
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* Font Size Scale - Based on a 16px base */
  --font-size-xs: 0.75rem;     /* 12px */
  --font-size-sm: 0.875rem;    /* 14px */
  --font-size-base: 1rem;      /* 16px */
  --font-size-lg: 1.125rem;    /* 18px */
  --font-size-xl: 1.25rem;     /* 20px */
  --font-size-2xl: 1.5rem;     /* 24px */
  --font-size-3xl: 1.875rem;   /* 30px */
  --font-size-4xl: 2.25rem;    /* 36px */

  /* Line Height Scale */
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;

  /* Letter Spacing */
  --letter-spacing-tighter: -0.05em;
  --letter-spacing-tight: -0.025em;
  --letter-spacing-normal: 0;
  --letter-spacing-wide: 0.025em;
  --letter-spacing-wider: 0.05em;
}

/* Base Typography Styles */
html, body {
  font-family: var(--font-family-sans);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  font-weight: var(--font-weight-normal);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  color: var(--color-text-primary);
}

/* Heading Styles */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-family-sans);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  margin-top: 0;
  color: var(--color-text-primary);
}

h1 {
  font-size: var(--font-size-3xl);
  letter-spacing: var(--letter-spacing-tight);
}

h2 {
  font-size: var(--font-size-2xl);
  letter-spacing: var(--letter-spacing-tight);
}

h3 {
  font-size: var(--font-size-xl);
}

h4 {
  font-size: var(--font-size-lg);
}

h5, h6 {
  font-size: var(--font-size-base);
}

/* Paragraph Styles */
p {
  margin-top: 0;
  margin-bottom: 1rem;
}

/* Link Styles */
a {
  color: var(--color-accent-blue);
  text-decoration: none;
  transition: color var(--duration-normal);
}

a:hover {
  text-decoration: underline;
}

/* UI Element Typography */
.btn, button {
  font-family: var(--font-family-sans);
  font-weight: var(--font-weight-medium);
}

input, textarea, select {
  font-family: var(--font-family-sans);
  font-size: var(--font-size-base);
}

/* Code Typography */
code, pre {
  font-family: var(--font-family-mono);
}

code {
  font-size: 0.9em;
}

/* Component-Specific Typography */

/* Header Typography */
.header-title {
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-lg);
}

/* Sidebar Typography */
.sidebar-title {
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-lg);
}

.conversation-item {
  font-size: var(--font-size-sm);
}

.conversation-group > div:first-child {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  letter-spacing: var(--letter-spacing-wide);
  text-transform: uppercase;
}

/* Message Typography */
.user-message-content {
  font-size: var(--font-size-base);
  line-height: var(--line-height-relaxed);
}

.ai-message-content {
  font-size: var(--font-size-base);
  line-height: var(--line-height-relaxed);
}

.message-footer {
  font-size: var(--font-size-xs);
  color: var(--color-text-tertiary);
}

/* Markdown Content Typography */
.markdown-content {
  line-height: var(--line-height-relaxed);
}

.markdown-content h1 {
  font-size: var(--font-size-2xl);
  margin-top: 1.5rem;
  margin-bottom: 1rem;
}

.markdown-content h2 {
  font-size: var(--font-size-xl);
  margin-top: 1.5rem;
  margin-bottom: 1rem;
}

.markdown-content h3 {
  font-size: var(--font-size-lg);
  margin-top: 1.25rem;
  margin-bottom: 0.75rem;
}

.markdown-content p, .markdown-content ul, .markdown-content ol {
  margin-bottom: 1rem;
}

.markdown-content code:not(pre code) {
  font-family: var(--font-family-mono);
  font-size: 0.9em;
}

/* Responsive Typography */
@media (max-width: 768px) {
  html, body {
    font-size: 15px; /* Slightly smaller base size on tablets */
  }

  h1 {
    font-size: var(--font-size-2xl);
  }

  h2 {
    font-size: var(--font-size-xl);
  }
}

@media (max-width: 640px) {
  html, body {
    font-size: 14px; /* Smaller base size on mobile */
  }

  .markdown-content h1 {
    font-size: var(--font-size-xl);
  }

  .markdown-content h2 {
    font-size: var(--font-size-lg);
  }
}
