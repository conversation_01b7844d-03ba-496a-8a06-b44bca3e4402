/**
 * 滚动条样式
 *
 * 统一的滚动条样式，遵循OpenAI/ChatGPT设计风格
 * 特点：
 * 1. 默认状态下非常淡/透明，几乎融入背景
 * 2. 仅在悬停或滚动时显示正常颜色
 * 3. 无滚动轨道背景，只有滚动条自身可见
 * 4. 统一的圆角和宽度
 */

/* ===== 全局滚动条样式 ===== */

/* 基础滚动行为 */
.scrollbar-auto,
.overflow-auto,
.overflow-y-auto,
.overflow-x-auto,
.chat-scroll-container,
.sidebar-scroll-container,
.hljs-content,
.markdown-content .table-container {
    /* 使用平滑滚动 */
    scroll-behavior: smooth;
    /* 触摸设备上的滚动行为 */
    -webkit-overflow-scrolling: touch;
}

/* Firefox 滚动条样式 */
.scrollbar-auto,
.overflow-auto,
.overflow-y-auto,
.overflow-x-auto,
.chat-scroll-container,
.sidebar-scroll-container,
.hljs-content,
.markdown-content .table-container {
    scrollbar-width: thin;
    scrollbar-color: rgba(0, 0, 0, 0.05) transparent;
}

/* Firefox 滚动条悬停/激活状态 */
.scrollbar-auto:hover,
.overflow-auto:hover,
.overflow-y-auto:hover,
.overflow-x-auto:hover,
.chat-scroll-container:hover,
.sidebar-scroll-container:hover,
.hljs-content:hover,
.markdown-content .table-container:hover {
    scrollbar-color: rgba(0, 0, 0, 0.12) transparent;
}

/* 暗色模式下的 Firefox 滚动条 */
[data-theme="dark"] .scrollbar-auto,
[data-theme="dark"] .overflow-auto,
[data-theme="dark"] .overflow-y-auto,
[data-theme="dark"] .overflow-x-auto,
[data-theme="dark"] .chat-scroll-container,
[data-theme="dark"] .sidebar-scroll-container,
[data-theme="dark"] .hljs-content,
[data-theme="dark"] .markdown-content .table-container {
    scrollbar-color: rgba(255, 255, 255, 0.05) transparent;
    transition: scrollbar-color 0.3s ease;
}

/* 暗色模式下的 Firefox 滚动条悬停/激活状态 */
[data-theme="dark"] .scrollbar-auto:hover,
[data-theme="dark"] .overflow-auto:hover,
[data-theme="dark"] .overflow-y-auto:hover,
[data-theme="dark"] .overflow-x-auto:hover,
[data-theme="dark"] .chat-scroll-container:hover,
[data-theme="dark"] .sidebar-scroll-container:hover,
[data-theme="dark"] .hljs-content:hover,
[data-theme="dark"] .markdown-content .table-container:hover {
    scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
}

/* Webkit 滚动条样式 (Chrome, Safari, Edge) */
.scrollbar-auto::-webkit-scrollbar,
.overflow-auto::-webkit-scrollbar,
.overflow-y-auto::-webkit-scrollbar,
.overflow-x-auto::-webkit-scrollbar,
.chat-scroll-container::-webkit-scrollbar,
.sidebar-scroll-container::-webkit-scrollbar,
.hljs-content::-webkit-scrollbar,
.markdown-content .table-container::-webkit-scrollbar {
    width: 5px;
    height: 5px;
    background-color: transparent;
}

/* Webkit 滚动条轨道 */
.scrollbar-auto::-webkit-scrollbar-track,
.overflow-auto::-webkit-scrollbar-track,
.overflow-y-auto::-webkit-scrollbar-track,
.overflow-x-auto::-webkit-scrollbar-track,
.chat-scroll-container::-webkit-scrollbar-track,
.sidebar-scroll-container::-webkit-scrollbar-track,
.hljs-content::-webkit-scrollbar-track,
.markdown-content .table-container::-webkit-scrollbar-track {
    background-color: transparent;
    border-radius: 10px;
}

/* Webkit 滚动条滑块 - 默认状态 */
.scrollbar-auto::-webkit-scrollbar-thumb,
.overflow-auto::-webkit-scrollbar-thumb,
.overflow-y-auto::-webkit-scrollbar-thumb,
.overflow-x-auto::-webkit-scrollbar-thumb,
.chat-scroll-container::-webkit-scrollbar-thumb,
.sidebar-scroll-container::-webkit-scrollbar-thumb,
.hljs-content::-webkit-scrollbar-thumb,
.markdown-content .table-container::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 10px;
}

/* Webkit 滚动条滑块 - 悬停状态 */
.scrollbar-auto:hover::-webkit-scrollbar-thumb,
.overflow-auto:hover::-webkit-scrollbar-thumb,
.overflow-y-auto:hover::-webkit-scrollbar-thumb,
.overflow-x-auto:hover::-webkit-scrollbar-thumb,
.chat-scroll-container:hover::-webkit-scrollbar-thumb,
.sidebar-scroll-container:hover::-webkit-scrollbar-thumb,
.hljs-content:hover::-webkit-scrollbar-thumb,
.markdown-content .table-container:hover::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.12);
}

/* Webkit 滚动条滑块 - 激活状态 */
.scrollbar-auto::-webkit-scrollbar-thumb:active,
.overflow-auto::-webkit-scrollbar-thumb:active,
.overflow-y-auto::-webkit-scrollbar-thumb:active,
.overflow-x-auto::-webkit-scrollbar-thumb:active,
.chat-scroll-container::-webkit-scrollbar-thumb:active,
.sidebar-scroll-container::-webkit-scrollbar-thumb:active,
.hljs-content::-webkit-scrollbar-thumb:active,
.markdown-content .table-container::-webkit-scrollbar-thumb:active {
    background-color: rgba(0, 0, 0, 0.18);
}

/* 暗色模式下的 Webkit 滚动条滑块 - 默认状态 */
[data-theme="dark"] .scrollbar-auto::-webkit-scrollbar-thumb,
[data-theme="dark"] .overflow-auto::-webkit-scrollbar-thumb,
[data-theme="dark"] .overflow-y-auto::-webkit-scrollbar-thumb,
[data-theme="dark"] .overflow-x-auto::-webkit-scrollbar-thumb,
[data-theme="dark"] .chat-scroll-container::-webkit-scrollbar-thumb,
[data-theme="dark"] .sidebar-scroll-container::-webkit-scrollbar-thumb,
[data-theme="dark"] .hljs-content::-webkit-scrollbar-thumb,
[data-theme="dark"] .markdown-content .table-container::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.05);
}

/* 暗色模式下的 Webkit 滚动条滑块 - 悬停状态 */
[data-theme="dark"] .scrollbar-auto:hover::-webkit-scrollbar-thumb,
[data-theme="dark"] .overflow-auto:hover::-webkit-scrollbar-thumb,
[data-theme="dark"] .overflow-y-auto:hover::-webkit-scrollbar-thumb,
[data-theme="dark"] .overflow-x-auto:hover::-webkit-scrollbar-thumb,
[data-theme="dark"] .chat-scroll-container:hover::-webkit-scrollbar-thumb,
[data-theme="dark"] .sidebar-scroll-container:hover::-webkit-scrollbar-thumb,
[data-theme="dark"] .hljs-content:hover::-webkit-scrollbar-thumb,
[data-theme="dark"] .markdown-content .table-container:hover::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.2);
}

/* 暗色模式下的 Webkit 滚动条滑块 - 激活状态 */
[data-theme="dark"] .scrollbar-auto::-webkit-scrollbar-thumb:active,
[data-theme="dark"] .overflow-auto::-webkit-scrollbar-thumb:active,
[data-theme="dark"] .overflow-y-auto::-webkit-scrollbar-thumb:active,
[data-theme="dark"] .overflow-x-auto::-webkit-scrollbar-thumb:active,
[data-theme="dark"] .chat-scroll-container::-webkit-scrollbar-thumb:active,
[data-theme="dark"] .sidebar-scroll-container::-webkit-scrollbar-thumb:active,
[data-theme="dark"] .hljs-content::-webkit-scrollbar-thumb:active,
[data-theme="dark"] .markdown-content .table-container::-webkit-scrollbar-thumb:active {
    background-color: rgba(255, 255, 255, 0.3);
}


