/**
 * Theme System
 *
 * This file defines the color variables and theme system for the application.
 * It follows Apple and OpenAI design aesthetics with a minimalist yet sophisticated approach.
 *
 * The color palette is carefully selected to create a clean, elegant interface
 * that feels modern and professional while maintaining excellent readability.
 */

:root {
  /* Base colors - Light theme (Apple/OpenAI inspired) */
  --color-bg-primary: #ffffff; /* Pure white background like Apple products */
  --color-bg-secondary: #f7f7f8; /* Subtle off-white like ChatGPT light mode */
  --color-bg-tertiary: #f0f0f1; /* Slightly darker background for hover states */
  --color-bg-input: transparent; /* Transparent input backgrounds */
  --color-bg-bubble: #f0f0f1; /* Light gray bubble background */
  --color-bg-accent: #f5f5f7; /* Apple's signature light gray */
  --color-bg-sidebar: #fbfbfd; /* Sidebar background - very subtle off-white like Apple's website */

  /* Text colors - Light theme */
  --color-text-primary: #1c1c1e; /* Near-black for primary text - better contrast than pure black */
  --color-text-secondary: #6e6e80; /* Medium gray for secondary text */
  --color-text-tertiary: #8e8ea0; /* Lighter gray for tertiary text */
  --color-text-placeholder: #8e8ea0; /* Placeholder text color */
  --color-text-inverse: #ffffff; /* White text for dark backgrounds */

  /* Accent colors - Shared between themes */
  --color-accent-blue: #0071e3; /* Apple's signature blue */
  --color-accent-green: #34c759; /* Apple's green */
  --color-accent-indigo: #5856d6; /* Apple's indigo */
  --color-accent-teal: #10a37f; /* OpenAI's teal */
  --color-accent-red: #ff3b30; /* Apple's red */
  --color-accent-orange: #ff9500; /* Apple's orange - for interrupted messages */

  /* Border colors - Light theme */
  --color-border-primary: #e3e3e5; /* Subtle light border */
  --color-border-secondary: rgba(0, 0, 0, 0.05); /* Very subtle border */
  --color-border-focus: #0071e3; /* Focus border uses accent color */

  /* UI element colors - Light theme */
  --color-button-bg: #f7f7f8; /* Subtle button background */
  --color-button-hover: #f0f0f1; /* Slightly darker on hover */
  --color-button-active: #e3e3e5; /* Slightly darker when active */
  --color-button-disabled: rgba(247, 247, 248, 0.5); /* Faded when disabled */

  /* Shadow colors - Light theme */
  --color-shadow: rgba(0, 0, 0, 0.05); /* Very subtle shadow */
  --color-shadow-hover: rgba(0, 0, 0, 0.08); /* Slightly more pronounced on hover */
  --color-shadow-elevated: rgba(0, 0, 0, 0.1); /* For elevated elements */

  /* Animation durations */
  --duration-fast: 150ms; /* Quick transitions */
  --duration-normal: 250ms; /* Standard transitions */
  --duration-slow: 400ms; /* Slower transitions for larger elements */
  --duration-text: 600ms; /* Text animations */
}

/* Dark theme - Apple/OpenAI inspired */
[data-theme="dark"] {
  /* Base colors - Dark theme */
  --color-bg-primary: #1a1a1a; /* Dark background but not pure black (like ChatGPT) */
  --color-bg-secondary: #232323; /* Slightly lighter than primary */
  --color-bg-tertiary: #2a2a2a; /* For hover states */
  --color-bg-input: transparent; /* Transparent input backgrounds */
  --color-bg-bubble: #2a2a2a; /* Dark bubble background */
  --color-bg-accent: #323232; /* Slightly lighter accent background */
  --color-bg-sidebar: #161618; /* Sidebar background - 深色但不是纯黑，接近 Apple 暗色模式 */

  /* Text colors - Dark theme */
  --color-text-primary: #f1f1f3; /* Off-white for better eye comfort than pure white */
  --color-text-secondary: #a0a0b0; /* Medium gray for secondary text */
  --color-text-tertiary: #6e6e80; /* Darker gray for tertiary text */
  --color-text-placeholder: #6e6e80; /* Placeholder text color */
  --color-text-inverse: #1c1c1e; /* Dark text for light backgrounds */

  /* Border colors - Dark theme */
  --color-border-primary: #3a3a3c; /* Subtle dark border */
  --color-border-secondary: rgba(255, 255, 255, 0.1); /* Very subtle light border */
  --color-border-focus: #0071e3; /* Focus border uses accent color */

  /* UI element colors - Dark theme */
  --color-button-bg: #232323; /* Subtle button background */
  --color-button-hover: #2a2a2a; /* Slightly lighter on hover */
  --color-button-active: #323232; /* Slightly lighter when active */
  --color-button-disabled: rgba(35, 35, 35, 0.5); /* Faded when disabled */

  /* Shadow colors - Dark theme */
  --color-shadow: rgba(0, 0, 0, 0.3); /* Deeper shadows for dark theme */
  --color-shadow-hover: rgba(0, 0, 0, 0.4); /* More pronounced on hover */
  --color-shadow-elevated: rgba(0, 0, 0, 0.5); /* For elevated elements */
}

/* Responsive breakpoints as CSS variables */
:root {
  --breakpoint-sm: 640px; /* Matches sm: */
  --breakpoint-md: 768px; /* Matches md: */
  --breakpoint-lg: 1024px; /* Matches lg: */
  --breakpoint-xl: 1280px; /* Matches xl: */
  --breakpoint-2xl: 1536px; /* Matches 2xl: */
}
