/**
 * 模态框样式
 *
 * 定义模态框的样式，遵循 Apple/OpenAI 设计风格
 * 特点：
 * 1. 简约而不简单的设计
 * 2. 优雅的过渡动画
 * 3. 适当的阴影和圆角
 * 4. 暗色模式适配
 */

/* 模态框容器 - 确保正确的z-index和背景 */
.modal {
    z-index: 100;
    background-color: transparent;
    /* 不添加额外的样式，使用DaisyUI的原生实现 */
}

/* 模态框内容盒子 */
.modal-box {
    padding: 1.5rem;
    max-width: 32rem;
    border-radius: 0.75rem;
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1),
                0 8px 10px -6px rgba(0, 0, 0, 0.05);
    background-color: #ffffff;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

/* 暗色模式下的模态框内容盒子 */
[data-theme="dark"] .modal-box {
    background-color: #232323;
    border: 1px solid rgba(255, 255, 255, 0.05);
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.2),
                0 8px 10px -6px rgba(0, 0, 0, 0.1);
}

/* 模态框背景 - 确保不会有文字穿透问题 */
.modal-backdrop {
    background-color: rgba(0, 0, 0, 0.4);
}

/* 暗色模式下的模态框背景 - 稍微调暗一些 */
[data-theme="dark"] .modal-backdrop {
    background-color: rgba(0, 0, 0, 0.6);
}

/* 模态框按钮区域 */
.modal-action {
    margin-top: 1.5rem;
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
}

/* 取消按钮样式 */
.btn-cancel {
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: #666;
    background-color: #f1f1f1;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-cancel:hover {
    background-color: #e5e5e5;
    transform: translateY(-1px);
}

.btn-cancel:active {
    transform: translateY(0);
}

/* 暗色模式下的取消按钮 */
[data-theme="dark"] .btn-cancel {
    color: #e0e0e0;
    background-color: #3a3a3a;
}

[data-theme="dark"] .btn-cancel:hover {
    background-color: #444;
}

/* 删除按钮样式 */
.btn-delete {
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: white;
    background-color: #ff3b30; /* Apple红色 */
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-delete:hover {
    background-color: #ff2d20;
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(255, 59, 48, 0.2);
}

.btn-delete:active {
    transform: translateY(0);
    box-shadow: none;
}

/* 暗色模式下的删除按钮 */
[data-theme="dark"] .btn-delete {
    background-color: #ff453a; /* 暗色模式下稍微亮一点的红色 */
}

[data-theme="dark"] .btn-delete:hover {
    background-color: #ff574d;
    box-shadow: 0 4px 6px -1px rgba(255, 69, 58, 0.3);
}

/* 使用DaisyUI原生的模态框动画 */
