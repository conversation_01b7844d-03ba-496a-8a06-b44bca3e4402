/**
 * 代码块样式
 *
 * 定义代码块的样式，使用Atom One Light/Dark主题
 * 集成了Highlight.js语法高亮和复制功能
 * 使用font.css中定义的字体变量
 */

/* 代码块容器 */
.hljs-container {
    position: relative;
    margin: 1.5rem 0;
    overflow: hidden;
    /* 移除容器边框 */
}

/* 代码内容区域 */
.hljs-content {
    position: relative;
    overflow-x: auto;
    border: none;
}

/* 语言标签和复制按钮 */
.hljs-toolbar {
    position: absolute;
    top: 0;
    right: 0;
    display: flex;
    align-items: center;
    padding: 0.25rem;
    z-index: 2; /* 降低z-index以避免与导航栏菜单冲突 */
    background-color: rgba(0, 0, 0, 0.05);
    border-bottom-left-radius: 0.25rem;
}

[data-theme="dark"] .hljs-toolbar {
    background-color: rgba(255, 255, 255, 0.05);
}

/* 语言标签 */
.hljs-language {
    font-size: var(--font-size-xs);
    color: var(--color-text-secondary);
    font-weight: var(--font-weight-medium);
    text-transform: uppercase;
    letter-spacing: var(--letter-spacing-wide);
    margin-right: 0.5rem;
    font-family: var(--font-family-sans);
}

/* 复制按钮 */
.hljs-copy-button {
    font-size: var(--font-size-xs);
    padding: 0.15rem 0.4rem;
    height: auto;
    min-height: 1.5rem;
    opacity: 0.8;
    font-family: var(--font-family-sans);
}

/* 适配Highlight.js的样式 */
.hljs-content pre {
    margin: 0;
    border: none;
}

.hljs-content code {
    font-family: var(--font-family-mono);
    font-size: 0.8125rem; /* 13px - 比原来的14px小一点 */
    line-height: var(--line-height-normal);
    padding: 1rem;
    display: block;
    border: none;
    letter-spacing: -0.01em; /* 编程字体通常需要稍微紧凑一点 */
}

/* 暗色主题样式覆盖 */
/* 移除暗色主题下的边框样式 */

/* 响应式调整 */
@media (max-width: 768px) {
    .hljs-content code {
        padding: 0.875rem;
    }
}

@media (max-width: 640px) {
    .hljs-content code {
        padding: 0.75rem;
    }

    .hljs-toolbar {
        padding: 0.2rem;
    }

    .hljs-copy-button {
        padding: 0.1rem 0.3rem;
    }
}
