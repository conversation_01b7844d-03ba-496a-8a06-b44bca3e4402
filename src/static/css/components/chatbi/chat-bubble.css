/**
 * 聊天气泡样式
 * 
 * 定义聊天气泡的样式，遵循 Apple/OpenAI 设计风格
 */

/* 聊天气泡基础样式 */
.chat-bubble {
    white-space: pre-wrap;
    word-break: break-word;
    box-shadow: 0 1px 2px var(--color-shadow);
    transition: background-color var(--duration-normal),
                border-color var(--duration-normal);
    border-radius: 1rem;
    border: 1px solid var(--color-border-secondary);
}

/* 聊天消息底部信息 */
.chat-footer {
    transition: opacity var(--duration-text) ease;
    font-size: 0.75rem;
    margin-top: 0.25rem;
    color: var(--color-text-secondary);
}

/* 响应式调整 */
@media (max-width: 768px) {
    .chat-bubble {
        max-width: 90%;
        border-radius: 0.875rem;
    }
}

@media (max-width: 640px) {
    .chat-bubble {
        max-width: 85%;
        border-radius: 0.75rem;
    }
}
