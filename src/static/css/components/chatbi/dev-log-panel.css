/**
 * 开发日志面板样式
 *
 * 定义开发日志面板的样式，遵循 Apple/OpenAI 设计风格
 * 特点：
 * 1. 简约而不简单的设计
 * 2. 优雅的过渡动画
 * 3. 适当的阴影和圆角
 * 4. 暗色模式适配
 */

/* 日志面板容器 - 使用与chat-input-container相同的样式 */
.chat-input-container {
    /* 已在chat-input.css中定义 */
    transition: background-color var(--duration-normal), border-color var(--duration-normal);
}

/* 日志面板主体 - 使用与chat-input-box相同的样式 */
.dev-log-panel {
    margin-top: 0.5rem;
    margin-bottom: 0.5rem;
    border-color: var(--color-border-primary);
    background-color: var(--color-bg-primary);
    transition: background-color var(--duration-normal), box-shadow var(--duration-normal), border-radius var(--duration-normal);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.03), 0 1px 2px rgba(0, 0, 0, 0.02);
    border-radius: 1.1rem; /* 与chat-input-box一致 */
    overflow: hidden;
}

/* 暗色模式下的日志面板 */
[data-theme="dark"] .dev-log-panel {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.07);
    border-color: var(--color-border-secondary);
}

/* 日志面板头部 */
.dev-log-header {
    background-color: transparent;
    border-bottom: 1px solid var(--color-border-secondary);
    transition: background-color var(--duration-normal),
                border-color var(--duration-normal);
}

/* 暗色模式下的日志面板头部 */
[data-theme="dark"] .dev-log-header {
    background-color: transparent;
}

/* 日志内容区域 */
.dev-log-content {
    background-color: transparent;
    color: var(--color-text-primary);
    transition: background-color var(--duration-normal),
                color var(--duration-normal);
    font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
    font-size: 0.75rem;
    line-height: 1.5;
    white-space: pre-wrap;
    word-break: break-word;
}

/* 暗色模式下的日志内容区域 */
[data-theme="dark"] .dev-log-content {
    background-color: transparent;
}

/* 日志行样式 */
.log-line {
    padding: 0.125rem 0;
    border-radius: 0.25rem;
    transition: background-color var(--duration-fast);
    opacity: 0.9;
    font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
    font-size: 0.9em;
    margin: 2px 0;
    color: #666;
}

/* 错误日志样式 */
.log-line.error {
    color: #e53e3e;
}

/* 警告日志样式 */
.log-line.warning {
    color: #dd6b20;
}

/* 日志行悬停效果 */
.log-line:hover {
    background-color: var(--color-button-hover);
    opacity: 1;
}

/* 暗色模式下的日志行悬停效果 */
[data-theme="dark"] .log-line:hover {
    background-color: var(--color-button-hover);
    opacity: 1;
}

/* 暗色模式下的日志行样式 */
[data-theme="dark"] .log-line {
    color: #999;
}

/* 暗色模式下的错误日志样式 */
[data-theme="dark"] .log-line.error {
    color: #fc8181;
}

/* 暗色模式下的警告日志样式 */
[data-theme="dark"] .log-line.warning {
    color: #fbd38d;
}

/* 信息日志样式 */
.log-line.info {
    color: var(--color-info);
}

/* 成功日志样式 */
.log-line.success {
    color: var(--color-success);
}

/* 淡入淡出动画 */
.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
    opacity: 0;
    transform: translateY(10px);
}

/* 响应式调整 */
@media (max-width: 768px) {
    .dev-log-panel {
        margin-top: 0.375rem;
        margin-bottom: 0.375rem;
        border-radius: 0.875rem;
    }

    .dev-log-content {
        font-size: 0.7rem; /* 稍微小一点的字体 */
    }
}

@media (max-width: 640px) {
    .dev-log-panel {
        margin-top: 0.25rem;
        margin-bottom: 0.25rem;
        border-radius: 0.75rem;
    }

    .dev-log-header {
        padding: 0.375rem 0.75rem;
    }

    .dev-log-content {
        padding: 0.5rem;
        font-size: 0.65rem; /* 更小的字体 */
    }
}