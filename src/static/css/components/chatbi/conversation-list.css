/**
 * 会话列表样式
 *
 * 定义侧边栏中会话列表的样式，遵循 Apple/OpenAI 设计风格
 * 采用更精致、克制的配色方案
 */

/* 会话行 */
.conversation-row {
    display: grid;
    grid-template-columns: 1fr auto;
    align-items: center;
    margin-bottom: 0.25rem;
    border-radius: 0.375rem;
    transition: background-color 0.2s ease;
    padding: 0.5rem 0.75rem;
    cursor: pointer;
}

.conversation-row:hover:not(.menu-active) {
    background-color: #edf0f3;
}

[data-theme="dark"] .conversation-row:hover:not(.menu-active) {
    background-color: rgba(255, 255, 255, 0.03);
}

/* 自定义菜单项样式，使其更符合 Apple/OpenAI 设计风格 */
.menu-active {
    background-color: #e8eaed !important; /* Light模式下的高亮背景 */
    color: #333 !important; /* 明确设置为深色文本 */
    font-weight: var(--font-weight-medium);
    font-family: var(--font-family-sans);
}

[data-theme="dark"] .menu-active {
    background-color: rgba(255, 255, 255, 0.06) !important; /* 暗色模式下的高亮 */
    color: #f1f1f3 !important; /* 明确设置为浅色文本 */
}

.conversation-item > div:hover:not(.menu-active) {
    background-color: #edf0f3 !important; /* Light模式下稍微加强的悬停效果 */
}

[data-theme="dark"] .conversation-item > div:hover:not(.menu-active) {
    background-color: rgba(255, 255, 255, 0.03) !important; /* 暗色模式下的悬停效果 */
}

/* 确保所有过渡效果平滑并移除下划线 */
.menu li > a, .navbar a, .sidebar-bg a {
    transition: background-color 0.2s ease, color 0.2s ease !important;
    text-decoration: none !important; /* 移除下划线 */
}

/* 确保悬停时也没有下划线 */
.menu li > a:hover, .navbar a:hover, .sidebar-bg a:hover {
    text-decoration: none !important;
}

/* 侧边栏菜单项中的三点菜单按钮样式 - 仅在桌面端（PC）悬停或聚焦时显示三点菜单按钮 */
/* 仅PC端生效，移动端不应用此规则 */
@media (min-width: 769px) {
    .conversation-item .menu-actions {
        opacity: 0;
        transition: opacity 0.2s ease;
    }

    .conversation-item > div:hover .menu-actions,
    .conversation-item .dropdown:focus-within .menu-actions,
    .conversation-item .menu-actions:focus-within {
        opacity: 1;
    }
}

/* 移动设备上只显示当前会话的三点菜单 */
@media (max-width: 768px) {
    /* 默认隐藏所有三点菜单 */
    .conversation-item .menu-actions {
        opacity: 0;
    }

    /* 只显示当前活跃会话的三点菜单 */
    .conversation-item > div.menu-active .menu-actions {
        opacity: 0.7; /* 使用稍微透明的效果，保持美观 */
    }
}

/* 确保菜单项内容正确布局 */
.conversation-item > div {
    width: 100%;
    font-family: var(--font-family-sans);
    font-size: var(--font-size-sm);
}

/* 确保标题在必要时截断 */
.conversation-item .truncate {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 对话分组样式 */
.conversation-group {
    margin-bottom: 1rem;
}

.conversation-group:last-child {
    margin-bottom: 0;
}

/* 分组标题样式 */
.conversation-group > div:first-child {
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-xs);
    letter-spacing: var(--letter-spacing-wide);
    text-transform: uppercase;
    margin-bottom: 0.25rem;
    color: var(--color-text-secondary);
    font-family: var(--font-family-sans);
}

/* 头像样式 */
.avatar {
    position: relative;
    display: inline-flex;
}

.avatar .w-8 {
    width: 2rem;
    height: 2rem;
}

.avatar .rounded-full {
    border-radius: 9999px;
    overflow: hidden;
}

.avatar-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background-color: #0284c7;
    color: white;
    font-weight: bold;
    font-size: 1.25rem;
}

/* 错误文本颜色 */
.text-error {
    color: #e11d48;
}

/* 会话列表布局样式 */
.filter-section {
    margin-bottom: 0;
}

/* 用户名搜索输入框 */
.filter-input-group {
    display: flex;
    align-items: center;
    height: 2.5rem;
    border: 1px solid var(--b3, #e5e7eb);
    border-radius: 0.5rem;
    overflow: hidden;
    transition: all 0.2s ease;
    background-color: var(--b1, #ffffff);
}

.filter-input-group:focus-within {
    border-color: var(--p, #4f46e5);
    box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.1);
}

.filter-input-group .input-group-addon {
    background-color: var(--b2, #f9fafb);
    padding: 0 0.75rem;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    border-right: 1px solid var(--b3, #e5e7eb);
}

.filter-input-group input {
    border: none;
    background-color: transparent;
    padding: 0 0.75rem;
    height: 100%;
    width: 100%;
    font-size: 0.875rem;
    color: var(--bc, #374151);
}

.filter-input-group input:focus {
    outline: none;
}

/* 高级筛选下拉菜单 */
.advanced-filters-dropdown {
    position: relative;
}

.advanced-filters-button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0 1rem;
    height: 2.5rem;
    border-radius: 0.5rem;
    background-color: var(--b2, #f9fafb);
    border: 1px solid var(--b3, #e5e7eb);
    color: var(--bc, #374151);
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.advanced-filters-button:hover {
    background-color: var(--b3, #e5e7eb);
}

.advanced-filters-content {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: 0.5rem;
    width: 16rem;
    background-color: var(--b1, #ffffff);
    border: 1px solid var(--b3, #e5e7eb);
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    padding: 1rem;
    z-index: 50;
}

/* 应用筛选按钮 */
.apply-filters-button {
    width: 100%;
    padding: 0.5rem;
    border-radius: 0.375rem;
    background-color: var(--p, #4f46e5);
    color: white;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease;
    border: none;
}

.apply-filters-button:hover {
    background-color: var(--pf, #4338ca);
}

/* 活跃筛选标签 */
.active-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 1rem;
}

.filter-badge {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    background-color: var(--b2, #f9fafb);
    border: 1px solid var(--b3, #e5e7eb);
    font-size: 0.75rem;
    color: var(--bc, #374151);
}

.filter-badge-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 1rem;
    height: 1rem;
    border-radius: 9999px;
    background-color: transparent;
    border: none;
    color: var(--bc, #374151);
    opacity: 0.7;
    transition: all 0.2s ease;
    padding: 0;
}

.filter-badge-button:hover {
    opacity: 1;
    background-color: rgba(0, 0, 0, 0.05);
}

.clear-all-button {
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    background-color: transparent;
    border: 1px solid var(--b3, #e5e7eb);
    font-size: 0.75rem;
    color: var(--bc, #374151);
    transition: all 0.2s ease;
}

.clear-all-button:hover {
    background-color: var(--b2, #f9fafb);
}

/* 会话表格样式 */
.conversation-list-container {
    width: 100%;
}

.conversation-table {
    width: 100%;
    overflow-x: auto;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.conversation-table table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    flex: 1;
}

.conversation-table th {
    padding: 0.75rem 1rem;
    text-align: left;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--bc, #374151);
    border-bottom: 1px solid var(--b3, #e5e7eb);
}

.conversation-table td {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid var(--b3, #e5e7eb);
    font-size: 0.875rem;
    color: var(--bc, #374151);
}

/* 第一行的上边框 */
.conversation-table thead tr th {
    border-top: none;
}

/* 最后一行的下边框 */
.conversation-table tbody tr:last-child td {
    border-bottom: none;
}

.conversation-table tr {
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.conversation-table tr:hover {
    background-color: var(--b2, #f9fafb);
}

/* 用户信息样式 */
.user-info {
    display: flex;
    flex-direction: column;
}

.user-name {
    font-weight: 500;
}

.user-email {
    font-size: 0.75rem;
    color: var(--bc-secondary, #6b7280);
}

/* 问题案例标签 */
.bad-case-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.125rem 0.5rem;
    border-radius: 9999px;
    background-color: rgba(239, 68, 68, 0.1);
    color: #ef4444;
    font-size: 0.75rem;
    font-weight: 500;
}

/* 查看按钮 */
.view-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    height: 2rem;
    padding: 0 0.75rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    font-weight: 500;
    background-color: transparent;
    color: var(--p, #4f46e5);
    transition: all 0.15s ease;
    border: none;
    cursor: pointer;
}

.view-button:hover {
    background-color: rgba(79, 70, 229, 0.1);
}

/* 分页样式 */
.pagination {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.5rem 1rem;
}

.pagination-info {
    font-size: 0.875rem;
    color: var(--bc-secondary, #6b7280);
}

.pagination-buttons {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.pagination-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
    border-radius: 0.375rem;
    background-color: var(--b2, #f9fafb);
    border: 1px solid var(--b3, #e5e7eb);
    color: var(--bc, #374151);
    transition: all 0.2s ease;
}

.pagination-button:hover:not(:disabled) {
    background-color: var(--b3, #e5e7eb);
}

.pagination-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-button-current {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
    border-radius: 0.375rem;
    background-color: var(--p, #4f46e5);
    color: white;
    font-weight: 500;
}

/* 加载和空状态 */
.loading-state, .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem 0;
    text-align: center;
}

.empty-state-icon {
    font-size: 2rem;
    margin-bottom: 1rem;
}

.empty-state-text {
    font-size: 0.875rem;
    color: var(--bc-secondary, #6b7280);
}

/* 标题截断 */
.truncate-title {
    max-width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

@media (min-width: 768px) {
    .truncate-title {
        max-width: 24rem;
    }
}

@media (max-width: 767px) {
    .truncate-title {
        max-width: 12rem;
    }
}
