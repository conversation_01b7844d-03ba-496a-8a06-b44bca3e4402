/**
 * Welcome Title Component Styles
 *
 * 独立的欢迎标题组件样式
 * 专为页面居中和优雅动画设计
 */

/* 标题容器 - 固定在chat-content-container中心，不受底部元素影响 */
.welcome-title-container {
    /* 使用绝对定位相对于chat-content-container */
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    /* 限制最大宽度，避免在宽屏上过宽 */
    max-width: 48rem;
    width: 100%;
    text-align: center;
    z-index: 1;
    /* 确保只在chat区域内显示 */
    pointer-events: none;
}

/* 欢迎标题 - 优雅设计 */
.welcome-title {
    font-size: 1.75rem; /* 稍大一些，更突出 */
    font-weight: 500;
    color: var(--color-text-primary);
    margin: 0;
    line-height: 1.3;
    letter-spacing: -0.01em;
    pointer-events: auto; /* 恢复文本交互 */
}

/* Vue过渡动画 - 下滑淡入效果，正常文档流 */
.welcome-title-enter-active {
    transition: all 0.8s cubic-bezier(0.16, 1, 0.3, 1);
}

.welcome-title-leave-active {
    transition: all 0.6s cubic-bezier(0.16, 1, 0.3, 1);
}

.welcome-title-enter-from {
    opacity: 0;
    transform: translate(-50%, -50%) translateY(-20px);
}

.welcome-title-leave-to {
    opacity: 0;
    transform: translate(-50%, -50%) translateY(-10px);
}

/* 暗色模式适配 */
[data-theme="dark"] .welcome-title {
    color: var(--color-text-primary);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .welcome-title {
        font-size: 1.5rem;
    }
}

@media (max-width: 640px) {
    .welcome-title {
        font-size: 1.375rem;
    }
}
