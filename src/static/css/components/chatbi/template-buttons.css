/**
 * Template <PERSON>tons Component Styles
 *
 * 模板按钮组件样式，遵循Apple/OpenAI设计风格
 * 与chat-input保持美学一致性
 */



/* 模板按钮容器 */
.template-buttons-container {
    /* 使用与chat-input-container相同的样式基础 */
    transition: opacity var(--duration-normal) ease, transform var(--duration-normal) ease;
    width: 100%;
    /* 比输入框稍微窄一点 */
    padding: 0 1rem;
    display: flex;
    justify-content: center;
}

/* 滚动区域 */
.template-buttons-scroll {
    overflow-x: auto;
    overflow-y: hidden;
    /* 使用项目统一的滚动条样式 */
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    /* 隐藏滚动条但保持功能 */
    scrollbar-width: thin;
    scrollbar-color: transparent transparent;
}

/* Webkit滚动条样式 - 更加微妙 */
.template-buttons-scroll::-webkit-scrollbar {
    height: 3px;
    background-color: transparent;
}

.template-buttons-scroll::-webkit-scrollbar-track {
    background-color: transparent;
}

.template-buttons-scroll::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.03);
    border-radius: 10px;
}

.template-buttons-scroll:hover::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.08);
}

/* 暗色模式下的滚动条 */
[data-theme="dark"] .template-buttons-scroll::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.03);
}

[data-theme="dark"] .template-buttons-scroll:hover::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.12);
}

/* 内容区域 */
.template-buttons-content {
    display: flex;
    gap: 0.5rem;
    padding: 0.25rem 0;
    min-width: max-content;
}

/* 模板按钮 */
.template-button {
    /* 基础样式 */
    display: inline-flex;
    align-items: center;
    justify-content: flex-start; /* 改为左对齐 */
    padding: 0.75rem 1.25rem;
    white-space: nowrap;

    /* 边框和圆角 - 更方一点 */
    border: 1px solid var(--color-border-primary);
    border-radius: 0.75rem; /* 更方一点，从1.1rem改为0.75rem */

    /* 背景和颜色 */
    background-color: var(--color-bg-primary);
    color: var(--color-text-primary);

    /* 阴影 - 与输入框保持一致但更微妙 */
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.02), 0 1px 1px rgba(0, 0, 0, 0.01);

    /* 过渡动画 - 只保留必要的过渡 */
    transition: background-color var(--duration-fast) ease, border-color var(--duration-fast) ease;

    /* 交互状态 */
    cursor: pointer;
    user-select: none;

    /* 防止按钮收缩 */
    flex-shrink: 0;
}

/* 按钮内容容器 */
.template-button-content {
    display: flex;
    flex-direction: column;
    align-items: flex-start; /* 改为左对齐 */
    gap: 0.125rem;
    width: 100%;
}

/* 按钮主标题 */
.template-button-label {
    font-size: 0.875rem;
    font-weight: 500;
    line-height: 1.25;
    color: var(--color-text-primary);
}

/* 按钮副标题 */
.template-button-subtitle {
    font-size: 0.75rem;
    font-weight: 400;
    line-height: 1.2;
    color: var(--color-text-secondary);
    opacity: 0.8;
}

/* 悬停状态 - 简化特效 */
.template-button:hover {
    background-color: var(--color-button-hover);
    /* 移除蓝色边框特效，保持原边框色 */
}

/* 激活状态 */
.template-button:active {
    background-color: var(--color-button-active);
}

/* 焦点状态 */
.template-button:focus {
    outline: none;
    /* 移除蓝色焦点特效 */
}

/* 暗色模式适配 */
[data-theme="dark"] .template-button {
    background-color: var(--color-bg-primary);
    border-color: var(--color-border-primary);
    color: var(--color-text-primary);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.07);
}

[data-theme="dark"] .template-button:hover {
    background-color: var(--color-button-hover);
    /* 移除蓝色边框特效，保持原边框色 */
}

[data-theme="dark"] .template-button:active {
    background-color: var(--color-button-active);
}

/* 进入/离开动画 */
.template-buttons-container {
    animation: templateFadeIn var(--duration-normal) ease-out;
}

@keyframes templateFadeIn {
    0% {
        opacity: 0;
        transform: translateY(-10px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 响应式调整 */
@media (max-width: 768px) {
    .template-buttons-container {
        padding: 0 0.75rem; /* 减少左右内边距 */
    }

    .template-button {
        padding: 0.6rem 1rem;
        border-radius: 0.625rem; /* 保持相对方形 */
    }

    .template-button-label {
        font-size: 0.8rem;
    }

    .template-button-subtitle {
        font-size: 0.7rem;
    }

    .template-buttons-content {
        gap: 0.375rem;
    }
}

@media (max-width: 640px) {
    .template-buttons-container {
        padding: 0 0.5rem; /* 进一步减少左右内边距 */
    }

    .template-button {
        padding: 0.5rem 0.8rem;
        border-radius: 0.5rem; /* 保持相对方形 */
    }

    .template-button-label {
        font-size: 0.75rem;
    }

    .template-button-subtitle {
        font-size: 0.65rem;
    }

    .template-buttons-content {
        gap: 0.25rem;
    }
}
