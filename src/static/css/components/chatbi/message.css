/**
 * Message Component Styles
 *
 * These styles enhance the existing Tailwind classes without replacing them.
 * They provide theme-based styling using CSS variables following Apple/OpenAI aesthetics.
 * Uses font variables from font.css for consistent typography.
 */

/* Common message styles - minimal to avoid conflicts */
.message {
  /* No layouts properties to avoid conflicts with Tailwind */
  transition: background-color var(--duration-normal), border-color var(--duration-normal);
}

.message-group {
  /* Minimal styling to avoid conflicts */
  transition: margin var(--duration-normal);
}

/* Message timestamp and actions - enhance without replacing */
.message-footer {
  /* Transition is already handled by Tailwind classes */
  transition: opacity var(--duration-text);
}

/* Dashboard 模式下始终显示消息工具栏，便于管理员定位 */
.dashboard-mode .message-footer {
  opacity: 1 !important; /* 覆盖 Tailwind 的 opacity-0 类 */
  padding-top: 0.25rem; /* 增加一点上边距 */
  padding-bottom: 0.25rem; /* 增加一点下边距 */
}

/* 在移动设备上始终显示消息工具栏 */
@media (max-width: 768px) {
  .message-footer {
    opacity: 1 !important; /* 覆盖 Tailwind 的 opacity-0 类 */
    padding-top: 0.25rem; /* 增加一点上边距 */
    padding-bottom: 0.25rem; /* 增加一点下边距 */
  }

  /* 移动端上稍微调整工具栏的样式，使其更加美观 */
  .message-action-button {
    opacity: 0.8; /* 稍微透明一点 */
  }

  .message-timestamp {
    opacity: 0.8 !important; /* 确保时间戳在移动端上可见但不突兀 */
  }
}

.message-timestamp {
  color: var(--color-text-secondary);
  font-size: var(--font-size-xs);
  font-family: var(--font-family-sans);
  transition: color var(--duration-normal), opacity var(--duration-text);
}

.message-action-button {
  /* Button styling is already handled by Tailwind/DaisyUI */
  transition: background-color var(--duration-fast), transform var(--duration-fast);
}

.message-action-button:hover {
  transform: translateY(-1px);
}

.message-action-button:active {
  transform: translateY(0);
}

.message-action-icon {
  color: var(--color-text-secondary);
  transition: color var(--duration-normal);
}

.message-action-icon:hover {
  color: var(--color-text-primary);
}

/* User message specific styles */
.user-message {
  /* Alignment is already handled by Tailwind */
  transition: margin var(--duration-normal);
}

.user-message-bubble {
  background-color: var(--color-bg-bubble);
  border-color: var(--color-border-secondary);
  box-shadow: 0 1px 2px var(--color-shadow);
  transition: background-color var(--duration-normal),
              border-color var(--duration-normal);
}

.user-message-content {
  color: var(--color-text-primary);
  font-family: var(--font-family-sans);
  font-size: 0.9375rem; /* 15px - 比原来的16px小一点 */
  font-weight: 450; /* 介于normal(400)和medium(500)之间的值 */
  line-height: var(--line-height-relaxed);
  transition: color var(--duration-normal);
}

/* 确保暗色模式下用户消息和AI消息文本颜色一致 */
[data-theme="dark"] .user-message-content {
  color: var(--color-text-primary); /* 使用与AI消息相同的颜色变量 */
}

.user-message-footer {
  /* Layout is already handled by Tailwind */
  transition: opacity var(--duration-text);
}

/* AI message specific styles */
.ai-message {
  /* Alignment is already handled by Tailwind */
  transition: background-color var(--duration-normal), border-color var(--duration-normal);
}

.ai-message-content {
  /* Layout is already handled by Tailwind */
  font-family: var(--font-family-sans);
  font-size: 0.9375rem; /* 15px - 比原来的16px小一点 */
  font-weight: 450; /* 介于normal(400)和medium(500)之间的值 */
  line-height: var(--line-height-relaxed);
  transition: color var(--duration-normal), border-left var(--duration-normal);
  position: relative; /* 为流式传输指示器提供定位上下文 */
}

/* 流式传输中的AI消息 */
.ai-message-streaming .ai-message-content {
  border-left: 3px solid var(--color-accent-blue);
}

/* 错误状态的AI消息 */
.ai-message-error .ai-message-content {
  border-left: 3px solid var(--color-accent-red, #ff3b30);
  background-color: rgba(255, 59, 48, 0.05);
}

/* 被中断的AI消息 */
.ai-message-interrupted .ai-message-content {
  border-left: 3px solid var(--color-accent-orange, #ff9500);
  background-color: rgba(255, 149, 0, 0.05);
}

/* 流式传输指示器 */
.streaming-indicator {
  display: inline-block;
  margin-left: 0.5rem;
  vertical-align: middle;
}

.ai-message-footer {
  /* Layout is already handled by Tailwind */
  transition: opacity var(--duration-text);
}

/* 不良案例标记按钮样式 */
.bad-case-marked {
  opacity: 1 !important;
}

.bad-case-icon {
  color: #e05252 !important; /* 更柔和的红色 */
  transition: color 0.2s ease;
}

[data-theme="dark"] .bad-case-icon {
  color: #ff6b6b !important; /* 暗色模式下更明亮的红色 */
}

/* Good Case标记按钮样式 */
.good-case-marked {
  opacity: 1 !important;
}

.good-case-icon {
  color: #10b981 !important; /* 绿色 */
  transition: color 0.2s ease;
}

[data-theme="dark"] .good-case-icon {
  color: #34d399 !important; /* 暗色模式下更明亮的绿色 */
}

/* Markdown content in AI messages */
.markdown-content {
  color: var(--color-text-primary);
  transition: color var(--duration-normal);
}

/* Code blocks in markdown content */
.markdown-content pre {
  background-color: var(--color-bg-tertiary);
  border: 1px solid var(--color-border-secondary);
  border-radius: 6px;
  transition: background-color var(--duration-normal), border-color var(--duration-normal);
}

/* Tables in markdown content */
.markdown-content table {
  border-color: var(--color-border-secondary);
  transition: border-color var(--duration-normal);
}

.markdown-content th,
.markdown-content td {
  border-color: var(--color-border-secondary);
  transition: border-color var(--duration-normal), background-color var(--duration-normal);
}

.markdown-content th {
  background-color: var(--color-bg-secondary);
}

/* 反馈收集功能样式 */

/* 扩展按钮样式 - Apple风格 */
.extended-button {
  width: auto !important;
  padding: 4px 10px 4px 4px !important;
  border-radius: 12px !important;
  transition: all 200ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
  background: rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

.extended-button:hover {
  background: rgba(0, 0, 0, 0.06);
  border-color: rgba(0, 0, 0, 0.12);
  transform: scale(1.02);
}

[data-theme="dark"] .extended-button {
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.12);
}

[data-theme="dark"] .extended-button:hover {
  background: rgba(255, 255, 255, 0.12);
  border-color: rgba(255, 255, 255, 0.18);
}

/* 反馈提示文字样式 - 简洁风格 */
.feedback-prompt-text {
  font-size: 12px;
  font-weight: 500;
  margin-left: 6px;
  white-space: nowrap;
  transition: opacity 200ms ease;
  letter-spacing: -0.01em;
  opacity: 0.8;
}

.feedback-prompt-text.clickable {
  cursor: pointer;
  color: #007AFF;
  opacity: 1;
}

.feedback-prompt-text.clickable:hover {
  opacity: 0.7;
}

.feedback-prompt-text.submitted {
  color: #34C759;
  cursor: default;
  opacity: 1;
}

.feedback-prompt-text.submitted::before {
  content: "✓ ";
  font-weight: 600;
  margin-right: 1px;
}

[data-theme="dark"] .feedback-prompt-text.clickable {
  color: #0A84FF;
}

[data-theme="dark"] .feedback-prompt-text.submitted {
  color: #30D158;
}

/* 反馈模态框样式 - Apple风格 */
.feedback-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 300ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.feedback-modal-content {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 24px;
  max-width: 400px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow:
    0 32px 64px rgba(0, 0, 0, 0.12),
    0 0 0 1px rgba(255, 255, 255, 0.05);
  animation: slideUp 300ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

[data-theme="dark"] .feedback-modal-content {
  background: rgba(28, 28, 30, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow:
    0 32px 64px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.05);
}

/* 反馈标签样式 - 简洁风格 */
.feedback-tag {
  display: inline-flex;
  align-items: center;
  padding: 8px 12px;
  margin: 4px 6px 4px 0;
  border-radius: 8px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  background: rgba(0, 0, 0, 0.04);
  color: #1d1d1f;
  cursor: pointer;
  transition: all 150ms ease;
  font-size: 13px;
  font-weight: 500;
  user-select: none;
  letter-spacing: -0.01em;
}

.feedback-tag:hover {
  background: rgba(0, 0, 0, 0.06);
  border-color: rgba(0, 0, 0, 0.15);
  transform: scale(1.02);
}

.feedback-tag.selected {
  background: #007AFF;
  border-color: #007AFF;
  color: white;
  box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
}

.feedback-tag.selected::after {
  content: "✓";
  margin-left: 4px;
  font-weight: 600;
}

[data-theme="dark"] .feedback-tag {
  border: 1px solid rgba(255, 255, 255, 0.15);
  background: rgba(255, 255, 255, 0.08);
  color: #f2f2f7;
}

[data-theme="dark"] .feedback-tag:hover {
  background: rgba(255, 255, 255, 0.12);
  border-color: rgba(255, 255, 255, 0.2);
}

[data-theme="dark"] .feedback-tag.selected {
  background: #0A84FF;
  border-color: #0A84FF;
  color: white;
  box-shadow: 0 2px 8px rgba(10, 132, 255, 0.3);
}

/* 动画关键帧 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 按钮扩展动画 */
@keyframes buttonExtend {
  from {
    width: 32px;
    padding: 4px;
  }
  to {
    width: auto;
    padding: 4px 8px 4px 4px;
  }
}

/* 模态框按钮容器 */
.feedback-modal .flex.justify-end {
  gap: 8px;
  margin-top: 16px;
  align-items: center;
}

/* 取消按钮样式 - Apple风格 */
.btn-cancel {
  padding: 10px 16px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  background: rgba(0, 0, 0, 0.04);
  color: #1d1d1f;
  border-radius: 8px;
  cursor: pointer;
  transition: all 150ms ease;
  font-size: 13px;
  font-weight: 500;
  min-width: 70px;
  display: flex;
  align-items: center;
  justify-content: center;
  letter-spacing: -0.01em;
}

.btn-cancel:hover {
  background: rgba(0, 0, 0, 0.06);
  border-color: rgba(0, 0, 0, 0.15);
}

.btn-cancel:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 主要按钮样式 - Apple风格 */
.feedback-modal .btn-primary {
  padding: 10px 20px;
  background: #007AFF;
  border: none;
  color: white;
  border-radius: 8px;
  cursor: pointer;
  transition: all 150ms ease;
  font-size: 13px;
  font-weight: 600;
  min-width: 90px;
  display: flex;
  align-items: center;
  justify-content: center;
  letter-spacing: -0.01em;
  box-shadow: 0 1px 3px rgba(0, 122, 255, 0.3);
}

.feedback-modal .btn-primary:hover {
  background: #0056CC;
  box-shadow: 0 2px 6px rgba(0, 122, 255, 0.4);
}

.feedback-modal .btn-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  box-shadow: 0 1px 3px rgba(0, 122, 255, 0.2);
}

[data-theme="dark"] .btn-cancel {
  border: 1px solid rgba(255, 255, 255, 0.15);
  background: rgba(255, 255, 255, 0.08);
  color: #f2f2f7;
}

[data-theme="dark"] .btn-cancel:hover {
  background: rgba(255, 255, 255, 0.12);
  border-color: rgba(255, 255, 255, 0.2);
}

[data-theme="dark"] .feedback-modal .btn-primary {
  background: #0A84FF;
  box-shadow: 0 1px 3px rgba(10, 132, 255, 0.3);
}

[data-theme="dark"] .feedback-modal .btn-primary:hover {
  background: #0066CC;
  box-shadow: 0 2px 6px rgba(10, 132, 255, 0.4);
}
