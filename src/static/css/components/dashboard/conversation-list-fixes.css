/**
 * Conversation List Fixes
 *
 * 修复会话列表组件的样式问题，特别是暗色模式下的边框和背景问题
 */

/* 确保所有dropdown都有正确的定位 */
.dropdown {
  position: relative !important;
}

/* 确保dropdown定位正确 */

/* 修复暗色模式下的边框问题 */
[data-theme="dark"] .input,
[data-theme="dark"] .select,
[data-theme="dark"] .btn-outline {
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

/* 修复暗色模式下的背景透明问题 - 全局修复 */
[data-theme="dark"] .dropdown-content,
[data-theme="dark"] .date-range-picker .dropdown-content,
[data-theme="dark"] .menu.dropdown-content,
[data-theme="dark"] .menu.menu-dropdown-content {
  background-color: #2a2a2a !important; /* 使用固定的背景色而不是DaisyUI变量 */
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2) !important;
  z-index: 50 !important; /* 确保所有下拉菜单都有正确的z-index */
}

/* GroupSection标题区域样式 */
.group-section-title {
  position: relative;
}

/* 确保DateRangePicker在暗色模式下有正确的背景色 */
[data-theme="dark"] .date-range-picker label.input {
  background-color: #2a2a2a !important; /* 使用固定的背景色而不是DaisyUI变量 */
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
}

/* 修复BaseHeader下拉菜单在暗色模式下的背景问题 */
[data-theme="dark"] .navbar .dropdown-content {
  background-color: #2a2a2a !important; /* 使用固定的背景色而不是DaisyUI变量 */
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2) !important;
}

/* 修复移动端下拉菜单位置问题 */
@media (max-width: 768px) {
  .dropdown .dropdown-content {
    left: auto !important;
    right: 0 !important;
    max-width: 90vw !important;
    position: absolute !important;
  }
}
