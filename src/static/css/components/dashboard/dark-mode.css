/**
 * Dark Mode Styles for Dashboard Components
 *
 * This file contains dark mode specific styles for dashboard components
 * using DaisyUI's data-theme="dark" attribute for theme switching
 */

/* Badge styles */
[data-theme="dark"] .dark-mode-badge {
  background-color: rgba(239, 68, 68, 0.2) !important;
  color: rgba(248, 113, 113, 0.9) !important;
}

[data-theme="dark"] .dark-mode-badge-lg {
  background-color: rgba(239, 68, 68, 0.2) !important;
  color: rgba(248, 113, 113, 0.9) !important;
}

/* Repair status badge styles for dark mode */
[data-theme="dark"] .bg-yellow-100 {
  background-color: rgba(251, 191, 36, 0.2) !important;
}

[data-theme="dark"] .text-yellow-800 {
  color: rgba(251, 191, 36, 0.9) !important;
}

[data-theme="dark"] .bg-green-100 {
  background-color: rgba(34, 197, 94, 0.2) !important;
}

[data-theme="dark"] .text-green-800 {
  color: rgba(34, 197, 94, 0.9) !important;
}

[data-theme="dark"] .bg-gray-100 {
  background-color: rgba(156, 163, 175, 0.2) !important;
}

[data-theme="dark"] .text-gray-800 {
  color: rgba(156, 163, 175, 0.9) !important;
}

/* Button styles */
[data-theme="dark"] .dark-mode-button:hover {
  background-color: rgba(79, 70, 229, 0.1) !important;
  color: rgba(129, 140, 248, 0.9) !important;
}

[data-theme="dark"] .dark-mode-close-button:hover {
  background-color: rgba(239, 68, 68, 0.1) !important;
  color: rgba(248, 113, 113, 0.9) !important;
}

[data-theme="dark"] .dark-mode-outline-button:hover {
  background-color: rgba(79, 70, 229, 0.1) !important;
  border-color: rgba(79, 70, 229, 0.3) !important;
  color: rgba(129, 140, 248, 0.9) !important;
}

/* Tab styles */
[data-theme="dark"] .dark-mode-tab-active {
  color: rgba(129, 140, 248, 0.9) !important;
}

[data-theme="dark"] .dark-mode-tab-indicator {
  background-color: rgba(129, 140, 248, 0.9) !important;
}

/* Message styles */
[data-theme="dark"] .dark-mode-assistant-message {
  background-color: rgba(79, 70, 229, 0.1) !important;
  border-color: rgba(79, 70, 229, 0.15) !important;
}

[data-theme="dark"] .dark-mode-assistant-name {
  color: rgba(129, 140, 248, 0.9) !important;
}

/* Success text */
[data-theme="dark"] .dark-mode-success-text {
  color: rgba(52, 211, 153, 0.9) !important;
}

/* Icon background colors */
[data-theme="dark"] .dark-mode-icon-bg {
  background-color: rgba(30, 30, 35, 0.7) !important;
  border-color: rgba(255, 255, 255, 0.05) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
}

/* Color-specific icon backgrounds */
[data-theme="dark"] .dark-mode-blue-bg {
  background-color: rgba(59, 130, 246, 0.2) !important;
}

[data-theme="dark"] .dark-mode-blue-text {
  color: rgba(96, 165, 250, 0.9) !important;
}

[data-theme="dark"] .dark-mode-purple-bg {
  background-color: rgba(124, 58, 237, 0.2) !important;
}

[data-theme="dark"] .dark-mode-purple-text {
  color: rgba(167, 139, 250, 0.9) !important;
}

[data-theme="dark"] .dark-mode-emerald-bg {
  background-color: rgba(16, 185, 129, 0.2) !important;
}

[data-theme="dark"] .dark-mode-emerald-text {
  color: rgba(52, 211, 153, 0.9) !important;
}

[data-theme="dark"] .dark-mode-rose-bg {
  background-color: rgba(225, 29, 72, 0.2) !important;
}

[data-theme="dark"] .dark-mode-rose-text {
  color: rgba(251, 113, 133, 0.9) !important;
}

/* DashboardDemo specific colors */
[data-theme="dark"] .dark-mode-primary-bg {
  background-color: rgba(var(--p), 0.2) !important;
}

[data-theme="dark"] .dark-mode-secondary-bg {
  background-color: rgba(var(--s), 0.2) !important;
}

[data-theme="dark"] .dark-mode-accent-bg {
  background-color: rgba(var(--a), 0.2) !important;
}

[data-theme="dark"] .dark-mode-error-bg {
  background-color: rgba(var(--er), 0.2) !important;
}
