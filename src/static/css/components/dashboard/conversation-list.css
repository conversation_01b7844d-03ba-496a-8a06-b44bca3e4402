/**
 * Conversation List Component Styles
 *
 * Modern, elegant styling for the conversation list component
 * Following Apple/OpenAI design aesthetics with clean, minimalist approach
 */

/* Filter section styling */
.filter-section {
  /* Removed background-color, border, box-shadow to blend with page */
  /* padding: 1.25rem; */ /* Padding is now handled by py-6 px-1 in ConversationListLayout.js */
  margin-bottom: 1.5rem;
  transition: all 0.2s ease;
}

/* Filter input groups */
.filter-input-group {
  display: flex;
  align-items: center;
  border: 1px solid var(--b3, #e5e7eb); /* Ensured border is present */
  border-radius: 0.5rem; 
  overflow: hidden;
  transition: all 0.2s ease;
  height: 2.75rem; /* Increased height slightly for better visual balance */
  background-color: var(--b1, #fff);
}

.filter-input-group:focus-within {
  border-color: var(--p, #4f46e5);
  box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.1);
}

.filter-input-group .input-group-addon {
  background-color: var(--b2, #f9fafb);
  padding: 0 0.75rem; /* Adjusted padding for icon */
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  border-right: 1px solid var(--b3, #e5e7eb);
}

.filter-input-group .input-group-addon span svg {
  width: 1rem; /* Consistent icon size */
  height: 1rem; /* Consistent icon size */
  opacity: 0.7;
}

.filter-input-group input {
  border: none;
  background-color: transparent;
  padding: 0 0.875rem; /* Adjusted padding for text input */
  height: 100%;
  width: 100%;
  font-size: 0.875rem; /* Consistent font size */
  color: var(--bc, #374151);
}

.filter-input-group input::placeholder {
    color: var(--bc, #9ca3af); /* Lighter placeholder text */
    opacity: 0.7;
}

.filter-input-group input:focus {
  outline: none;
}

/* Advanced filters dropdown */
.advanced-filters-dropdown {
  position: relative;
}

.advanced-filters-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem; /* Added gap for icon and text */
  height: 2.75rem; /* Matched height with .filter-input-group */
  padding: 0 1rem;
  border: 1px solid var(--b3, #e5e7eb); /* Default border */
  border-radius: 0.5rem;
  background-color: var(--b2, hsl(var(--b2) / var(--tw-bg-opacity, 1))); /* Light background, theme-aware */
  color: var(--bc, hsl(var(--bc) / var(--tw-text-opacity, 1)));
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.advanced-filters-button:hover {
  background-color: var(--b3, hsl(var(--b3) / var(--tw-bg-opacity, 1))); /* Slightly darker on hover */
  border-color: var(--b3, #d1d5db);
}

.advanced-filters-button svg {
    width: 1rem; /* Ensure icon size */
    height: 1rem;
}

.advanced-filters-content {
  position: absolute;
  right: 0;
  top: calc(100% + 0.5rem);
  width: 22rem; /* Slightly wider for better spacing */
  /* padding is now handled in the JS template for this element for direct bg/border/shadow */
  /* background-color: var(--b1, #fff); */
  /* border-radius: 0.75rem; */
  /* border: 1px solid var(--b3, #e5e7eb); */
  /* box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); */
  z-index: 60; 
  overflow: visible;
}

/* Styling for elements inside advanced-filters-content (inputs, selects, labels) */
.advanced-filters-content .form-control .label-text {
    font-size: 0.875rem;
    color: var(--bc, hsl(var(--bc) / 0.7)); /* Slightly muted label */
    margin-bottom: 0.25rem;
}

.advanced-filters-content .input,
.advanced-filters-content .select {
    border-radius: 0.375rem; /* Slightly less rounded than main inputs */
    border-color: var(--b3, #e0e0e0);
    font-size: 0.875rem;
}

.advanced-filters-content .input:focus,
.advanced-filters-content .select:focus {
    border-color: var(--p, #4f46e5);
    box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.1);
}

/* Apply filters button */
.apply-filters-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 2.5rem;
  margin-top: 1rem;
  border: none;
  border-radius: 0.5rem;
  background-color: var(--p, #4f46e5); /* Primary button color */
  color: var(--pc, white); /* Primary button text color */
  font-size: 0.875rem;
  font-weight: 500;
  transition: background-color 0.2s ease;
  cursor: pointer;
}

.apply-filters-button:hover {
  background-color: var(--pf, #4338ca); /* Darker primary on hover */
}

/* Table styling enhancements */
.conversation-table {
  background-color: var(--b1, #fff); /* This is part of BaseDashboardCard now */
  border-radius: 0.75rem; /* This is part of BaseDashboardCard now */
  overflow: hidden; /* This is part of BaseDashboardCard now */
  /* border: 1px solid var(--b2, #f0f0f0); */ /* Removed outer border as requested */
  /* box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05); */ /* This is part of BaseDashboardCard now */
}

.conversation-table table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

.conversation-table thead {
  background-color: var(--b2, #f9fafb);
}

.conversation-table th {
  color: var(--bc, #374151);
  opacity: 0.7;
  font-weight: 500;
  text-align: left;
  padding: 0.75rem 1rem;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.conversation-table tbody tr {
  border-bottom: 1px solid var(--b2, #f0f0f0);
  transition: background-color 0.15s ease;
}

.conversation-table tbody tr:hover {
  background-color: rgba(79, 70, 229, 0.05);
}

.conversation-table td {
  padding: 0.75rem 1rem;
  vertical-align: middle;
  font-size: 0.875rem;
}

.conversation-table td:last-child {
    padding-right: 2rem; /* Equivalent to pr-8 (8 * 0.25rem) */
}

/* User info styling */
.user-info {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-weight: 500;
  color: var(--bc, #374151);
}

.user-email {
  font-size: 0.75rem;
  color: var(--bc, #374151);
  opacity: 0.6;
  margin-top: 0.125rem;
}

/* Bad case badge styling */
.bad-case-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 500;
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--er, #ef4444);
}

.bad-case-badge svg {
  width: 0.75rem;
  height: 0.75rem;
  display: inline-block;
  vertical-align: middle;
}

/* Dark mode table adjustments */
[data-theme="dark"] .conversation-table thead {
  background-color: rgba(255, 255, 255, 0.03);
}

[data-theme="dark"] .conversation-table tbody tr {
  border-bottom-color: rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .conversation-table tbody tr:hover {
  background-color: rgba(255, 255, 255, 0.03);
}

[data-theme="dark"] .bad-case-badge {
  background-color: rgba(239, 68, 68, 0.2);
  color: rgba(239, 68, 68, 0.9);
}

/* Chat message styling for conversation detail modal */
.chat-message {
  transition: background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  margin-bottom: 1rem;
  border-radius: 0.75rem;
}

.chat-message-user {
  background-color: rgba(240, 240, 245, 0.7);
  border: 1px solid rgba(0, 0, 0, 0.03);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.02);
}

.chat-message-assistant {
  background-color: rgba(79, 70, 229, 0.05);
  border: 1px solid rgba(79, 70, 229, 0.1);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.02);
}

/* Dark mode adjustments */
[data-theme="dark"] .chat-message-user {
  background-color: rgba(50, 50, 60, 0.4);
  border-color: rgba(255, 255, 255, 0.03);
}

[data-theme="dark"] .chat-message-assistant {
  background-color: rgba(79, 70, 229, 0.1);
  border-color: rgba(79, 70, 229, 0.15);
}

/* Log entry styling */
.log-entry pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  font-size: 0.75rem;
  line-height: 1.5;
  white-space: pre-wrap;
  word-break: break-word;
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

/* Modal content scrollbar styling */
.modal-box .overflow-y-auto {
    scrollbar-width: thin;
    scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}





.modal-box .overflow-y-auto::-webkit-scrollbar {
    width: 6px;
}

.modal-box .overflow-y-auto::-webkit-scrollbar-track {
    background: transparent;
}

.modal-box .overflow-y-auto::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
}

/* Dark mode scrollbar */
[data-theme="dark"] .modal-box .overflow-y-auto {
    scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
}

[data-theme="dark"] .modal-box .overflow-y-auto::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.2);
}

/* Pagination styling */
.pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  border-top: 1px solid var(--b2, #f0f0f0);
}

.pagination-info {
  font-size: 0.875rem;
  color: var(--bc, #374151);
  opacity: 0.7;
}

.pagination-buttons {
  display: flex;
}

.pagination-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 2rem;
  min-width: 2rem;
  padding: 0 0.5rem;
  border: 1px solid var(--b3, #e5e7eb);
  background-color: var(--b1, #fff);
  color: var(--bc, #374151);
  font-size: 0.875rem;
  transition: all 0.15s ease;
}

.pagination-button:hover {
  background-color: rgba(79, 70, 229, 0.05);
}

.pagination-button:first-child {
  border-top-left-radius: 0.375rem;
  border-bottom-left-radius: 0.375rem;
}

.pagination-button:last-child {
  border-top-right-radius: 0.375rem;
  border-bottom-right-radius: 0.375rem;
}

.pagination-button-current {
  background-color: rgba(79, 70, 229, 0.1);
  color: var(--p, #4f46e5);
  border-color: rgba(79, 70, 229, 0.2);
  font-weight: 500;
}

/* Empty and loading states */
.empty-state, .loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1rem;
  text-align: center;
}

.empty-state-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.3;
}

.empty-state-text {
  color: var(--bc, #374151);
  opacity: 0.7;
  font-size: 0.875rem;
}

/* Filter badges */
.filter-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.375rem 0.75rem;
  border-radius: 0.5rem;
  font-size: 0.75rem;
  font-weight: 500;
  background-color: rgba(79, 70, 229, 0.1);
  color: var(--p, #4f46e5);
  margin-right: 0.5rem;
}

.filter-badge-button {
  margin-left: 0.5rem;
  padding: 0.25rem; /* Added padding to make click area larger */
  border-radius: 50%; /* Make it circular for a cleaner look */
  background-color: transparent;
  color: currentColor; /* Inherit color from parent */
  opacity: 0.7;
  transition: all 0.2s ease;
  display: inline-flex; /* For centering icon */
  align-items: center; /* For centering icon */
  justify-content: center; /* For centering icon */
}

.filter-badge-button:hover {
  opacity: 1;
  background-color: rgba(0, 0, 0, 0.1); /* Subtle hover effect */
  box-shadow: 0 0 0 2px rgba(0,0,0,0.05); /* Added subtle shadow to make it pop a bit on hover*/
}

.filter-badge-button svg {
  width: 0.75rem; /* Adjust icon size if necessary */
  height: 0.75rem; /* Adjust icon size if necessary */
}

/* Clear all button styling */
.clear-all-button {
  display: inline-flex;
  align-items: center;
  padding: 0.375rem 0.75rem;
  border-radius: 0.5rem;
  font-size: 0.75rem;
  font-weight: 500;
  background-color: var(--b2, #f0f0f0);
  color: var(--bc, #374151);
  transition: background-color 0.15s ease;
}

.clear-all-button:hover {
  background-color: var(--b3, #e5e7eb);
}

/* View button styling */
.view-button {
  display: inline-flex; /* Changed to inline-flex for horizontal layout */
  align-items: center;
  justify-content: center;
  gap: 0.25rem; /* Space between icon and text if any */
  padding: 0.375rem 0.75rem; 
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--p, #4f46e5);
  transition: all 0.2s ease;
  background-color: transparent; /* Ensure no background unless hovered/focused */
}

.view-button:hover {
  background-color: rgba(79, 70, 229, 0.1);
  color: var(--pf, #4338ca);
}

.view-button:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
    box-shadow: 0 0 0 2px var(--b1, #fff), 0 0 0 4px var(--p, #4f46e5);
}

/* Modal styling */
.modal-box {
  border-radius: 1rem;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1),
              0 8px 10px -6px rgba(0, 0, 0, 0.05);
  border: 1px solid var(--b2, #f0f0f0);
  animation: modal-pop 0.2s ease-out;
}

@keyframes modal-pop {
  0% {
    opacity: 0;
    transform: scale(0.95);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Tabs styling */
.tabs-boxed {
  border-radius: 0.75rem;
  background-color: var(--b2, #f9fafb);
  padding: 0.25rem;
}

.tabs-boxed .tab {
  border-radius: 0.5rem;
  transition: all 0.2s ease;
  font-size: 0.875rem;
  height: 2.25rem;
}

.tabs-boxed .tab-active {
  background-color: var(--p, #4f46e5);
  color: white;
}

/* Repair status select styling */
.repair-status-select {
  font-size: 0.6875rem !important; /* 11px */
  padding: 0.125rem 1.5rem 0.125rem 0.375rem !important; /* 增加右侧padding，让箭头在最右侧 */
  height: 1.5rem !important; /* 24px */
  min-height: 1.5rem !important;
  line-height: 1.125rem !important; /* 18px */
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .filter-section {
    padding: 1rem;
  }

  .conversation-table th,
  .conversation-table td {
    padding: 0.75rem;
  }

  .modal-box {
    max-height: 90vh;
    margin: 1rem;
  }
}
