/**
 * 侧边栏布局样式
 *
 * 定义侧边栏的布局和位置，包括桌面端和移动端的不同显示方式
 * 使用 transform 而非 width 来隐藏侧边栏，提高 Safari 兼容性
 */

/* 统一的侧边栏过渡效果 */
.sidebar-transition {
    transition-property: width, transform, margin;
    transition-duration: var(--duration-normal);
    transition-timing-function: ease-out;
}

/* 桌面侧边栏容器 */
.desktop-sidebar {
    position: fixed;
    top: 0;
    left: 0;
    width: 16rem; /* 256px = 64 * 4 (tailwind w-64) */
    height: 100%;
    z-index: 50;
    border-right: 1px solid rgba(0, 0, 0, 0.05);
    background-color: var(--color-bg-sidebar);
    transform: translateX(0);
    transition: transform var(--duration-normal) ease-out;
    overflow: hidden;
}

/* 暗色模式下的边框 */
[data-theme="dark"] .desktop-sidebar {
    border-right-color: rgba(255, 255, 255, 0.05);
}

/* 侧边栏关闭状态 */
.desktop-sidebar.sidebar-closed {
    transform: translateX(-100%);
}

/* 确保 Safari 兼容性 */
.desktop-sidebar {
    -webkit-transform: translateX(0);
    transform: translateX(0);
    -webkit-transition: -webkit-transform var(--duration-normal) ease-out;
    transition: -webkit-transform var(--duration-normal) ease-out;
    transition: transform var(--duration-normal) ease-out;
    transition: transform var(--duration-normal) ease-out, -webkit-transform var(--duration-normal) ease-out;
    will-change: transform;
}

.desktop-sidebar.sidebar-closed {
    -webkit-transform: translateX(-100%);
    transform: translateX(-100%);
}

/* 使用 3D 变换提高性能并解决 Safari 中的层叠上下文问题 */
.desktop-sidebar {
    transform: translate3d(0, 0, 0);
}

.desktop-sidebar.sidebar-closed {
    transform: translate3d(-100%, 0, 0);
}

/* 内容区域的边距过渡 */
.desktop-content {
    transition: margin-left var(--duration-normal) ease-out;
    margin-left: 16rem; /* 256px = 64 * 4 (tailwind w-64) */
}

.desktop-content.sidebar-closed {
    margin-left: 0;
}

/* 自定义侧边栏背景色 */
.sidebar-bg {
    background-color: #f9f9f9 !important; /* Light模式下的背景色 */
}

[data-theme="dark"] .sidebar-bg {
    background-color: #1e2226 !important; /* Dark模式下的背景色 */
}

/* 侧边栏滚动容器样式 */
.sidebar-scroll-container {
    /* 确保滚动条始终可见 */
    overflow-y: auto !important;
    /* 减少过渡效果，提高响应速度 */
    transition: none !important;
    /* 确保Safari滚动流畅 */
    -webkit-overflow-scrolling: touch;
}

/* 侧边栏容器样式 */
.sidebar-container {
    /* 确保内容不会被裁剪 */
    overflow: visible;
    /* 减少不必要的过渡效果 */
    will-change: auto;
}
