/**
 * 主样式文件
 *
 * 这是应用的主要样式入口点，采用模块化方式组织 CSS
 * 遵循关注点分离原则，按功能和组件分类样式
 * 充分利用Tailwind和DaisyUI的功能
 */

/* 基础样式 */
@import './base/theme.css';     /* 主题系统 - 提供颜色变量 */
@import './base/font.css';      /* 字体系统 - 提供字体变量和排版样式 */
@import './base/base.css';      /* 基础样式（合并了reset和responsive） */
@import './base/scrollbar.css'; /* 滚动条样式 */
@import './base/animations.css'; /* 动画效果 */

/* 布局样式 */
@import './layouts/sidebar-layout.css';  /* 侧边栏布局 */
@import './layouts/drawer-layout.css';   /* 抽屉布局 */
@import './layouts/content-layout.css';  /* 内容区域布局 */

/* 通用组件样式 */
@import './components/common/ui.css';        /* 通用UI元素 */
@import './components/common/buttons.css';   /* 按钮样式 */
@import './components/common/dropdown.css';  /* 下拉菜单 */
@import './components/common/markdown.css';  /* Markdown内容 */
@import './components/common/code-block.css'; /* 代码块 */
@import './components/common/modal.css';     /* 模态框样式 */

/* 聊天界面组件样式 */
@import './components/chatbi/chat-layout.css'; /* 聊天布局 - 现在是包装器，导入 layouts/content-layout.css */
@import './components/chatbi/chat-bubble.css'; /* 聊天气泡 */
@import './components/chatbi/message.css';    /* 消息样式 */
@import './components/chatbi/chat-input.css'; /* 聊天输入框 */
@import './components/chatbi/template-buttons.css'; /* 模板按钮 */
@import './components/chatbi/welcome.css';    /* 欢迎模块 */
@import './components/chatbi/question-prompts.css'; /* 问题提示组件 */
@import './components/chatbi/welcome-title.css'; /* 欢迎标题组件 */
@import './components/chatbi/conversation-list.css'; /* 会话列表 */
@import './components/chatbi/dev-log-panel.css'; /* 开发日志面板 */

/* 仪表板组件样式 */
@import './components/dashboard/dashboard-card.css'; /* 仪表板卡片 */
@import './components/dashboard/dashboard-header.css'; /* 仪表板头部 */
@import './components/dashboard/conversation-list.css'; /* 会话列表组件 */
@import './components/dashboard/dark-mode.css'; /* 暗色模式样式 */
@import './components/dashboard/conversation-list-fixes.css'; /* 会话列表修复 */
@import './components/dashboard/agent-tags.css'; /* 助手标签样式 */
