import { MenuIcon, SunIcon, MoonIcon, SunMoonIcon, HomeIcon, ArrowRightOnRectangleIcon, ChartBarIcon } from '../../utils/Icons.js';
import { toggleTheme, getCurrentTheme } from '../../utils/ThemeManager.js';
import { zDropdownContent } from '../../utils/zIndex.js';
import { ref, watch, computed } from 'vue'; // Import computed

export default {
    name: 'BaseHeader',
    props: {
        userInfo: {
            type: Object,
            required: true
        },
        isDarkTheme: {
            type: Boolean,
            default: false
        },
        title: {
            type: String,
            default: 'ChatBI'
        },
        homeUrl: {
            type: String,
            default: '/'
        },
        isDashboard: {
            type: Boolean,
            default: false
        },
        sidebarOpen: {
            type: Boolean,
            default: false
        },
        isDesktopView: {
            type: Boolean,
            default: true
        }
    },
    emits: ['toggleTheme', 'toggleSidebar'],
    setup(props, { emit }) {
        // Get current theme
        const currentTheme = ref(getCurrentTheme());

        // Toggle theme function
        const handleToggleTheme = () => {
            const isDark = toggleTheme();
            currentTheme.value = getCurrentTheme();
            emit('toggleTheme', isDark);
        };

        // Toggle sidebar function
        const handleToggleSidebar = () => {
            emit('toggleSidebar');
        };



        // Get theme text for display
        const themeText = computed(() => {
            switch(currentTheme.value) {
                case 'system': return '跟随系统';
                case 'light': return '亮色模式';
                case 'dark': return '暗色模式';
                default: return '切换主题';
            }
        });

        // --- Title Animation Logic ---
        // Ref to store the current transform value
        const currentTranslateX = ref(0);

        // Calculate the target position based on sidebar state and view
        const getTargetTranslateX = () => {
            if (props.sidebarOpen && props.isDesktopView) {
                // 使用CSS变量或计算值获取侧边栏宽度
                const sidebarWidth = 256; // w-64 = 16rem = 256px

                // 动态计算标题到navbar-start的距离
                let titleOffset = 110; // 默认值

                // 尝试动态计算实际偏移量
                setTimeout(() => {
                    const titleElement = document.querySelector('.navbar-start .btn-ghost');
                    if (titleElement && window.getComputedStyle) {
                        const navbarStart = titleElement.closest('.navbar-start');
                        if (navbarStart) {
                            const navbarStartRect = navbarStart.getBoundingClientRect();
                            const titleRect = titleElement.getBoundingClientRect();

                            // 计算实际偏移量
                            const actualOffset = titleRect.left - navbarStartRect.left;
                            if (actualOffset > 0 && actualOffset < 200) { // 合理范围检查
                                titleOffset = actualOffset;
                            }
                        }
                    }
                }, 0);

                return sidebarWidth - titleOffset;
            }
            return 0; // 默认位置
        };

        // Animation function using requestAnimationFrame and easing
        const animateTitlePosition = (startValue, endValue, duration = 200) => {
            const startTime = performance.now();

            const animate = (currentTime) => {
                const elapsedTime = currentTime - startTime;
                const progress = Math.min(elapsedTime / duration, 1);

                // EaseInOutQuad easing function
                const easeProgress = progress < 0.5
                    ? 2 * progress * progress
                    : 1 - Math.pow(-2 * progress + 2, 2) / 2;

                // Calculate current value based on progress
                currentTranslateX.value = startValue + (endValue - startValue) * easeProgress;

                // Continue animation if not finished
                if (progress < 1) {
                    requestAnimationFrame(animate);
                }
            };
            // Start the animation
            requestAnimationFrame(animate);
        };

        // Watch for changes in sidebar state or view type to trigger animation
        watch(
            () => [props.sidebarOpen, props.isDesktopView],
            () => {
                const targetValue = getTargetTranslateX();
                // Animate from current position to target position
                animateTitlePosition(currentTranslateX.value, targetValue);
            },
            { immediate: true } // Run immediately on component mount
        );

        // Computed property for the transform style
        const titleTransformStyle = computed(() => {
            return { transform: `translateX(${currentTranslateX.value}px)` };
        });
        // --- End Title Animation Logic ---

        return {
            handleToggleTheme,
            handleToggleSidebar,
            titleTransformStyle, // Use the computed property in the template
            currentTheme,
            themeText,
            // Icons
            MenuIcon,
            SunIcon,
            MoonIcon,
            SunMoonIcon,
            HomeIcon,
            ChartBarIcon,
            ArrowRightOnRectangleIcon,
            // zIndex utility
            zDropdownContent
        };
    },
    template: `
        <div class="navbar bg-base-100 px-4"> <div class="navbar-start">
                <slot name="start">
                    <!-- 移除了侧边栏切换按钮，由具体组件自行实现 -->
                </slot>
                <a :href="homeUrl" class="btn btn-ghost text-xl font-bold normal-case" :style="titleTransformStyle" v-html="title">
                </a>
            </div>

            <div class="navbar-end gap-1">
                <slot name="actions"></slot>

                <div class="dropdown dropdown-end ml-1">
                    <div tabindex="0" role="button" class="p-0 flex items-center justify-center rounded-full w-8 h-8 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary dark:focus:ring-offset-base-100">
                        <div class="avatar">
                            <div class="w-8 h-8 rounded-full overflow-hidden ring-1 ring-base-300 dark:ring-base-content/30"> <img v-if="userInfo.avatar" :src="userInfo.avatar" alt="用户头像" class="w-full h-full object-cover" />
                                <div v-else class="bg-primary text-primary-content flex items-center justify-center h-full text-lg font-bold">
                                    {{ userInfo.name ? userInfo.name.charAt(0).toUpperCase() : '?' }} </div>
                            </div>
                        </div>
                    </div>
                    <ul tabindex="0" class="mt-3 p-2 shadow menu menu-sm dropdown-content bg-base-100 rounded-box border border-base-300 dark:border-base-content/20 min-w-max" :style="{ zIndex: zDropdownContent }">
                        <li>
                            <div class="p-2">
                                <div class="font-bold">{{ userInfo.name }}</div>
                                <div class="text-sm opacity-60">{{ userInfo.jobTitle || '用户' }}</div>
                            </div>
                        </li>
                        <div class="divider my-0"></div>
                        <li>
                            <button @click="handleToggleTheme" class="flex items-center gap-2 py-2">
                                <span class="w-4 h-4 flex items-center justify-center" v-if="currentTheme === 'system'" v-html="SunMoonIcon"></span>
                                <span class="w-4 h-4 flex items-center justify-center" v-else-if="currentTheme === 'dark'" v-html="SunIcon"></span>
                                <span class="w-4 h-4 flex items-center justify-center" v-else v-html="MoonIcon"></span>
                                {{ themeText }}
                            </button>
                        </li>
                        <li v-if="isDashboard">
                            <a href="/" class="flex items-center gap-2 py-2">
                                <span class="w-4 h-4 flex items-center justify-center" v-html="HomeIcon"></span>
                                返回聊天
                            </a>
                        </li>
                        <li v-else-if="userInfo.isAdmin">
                            <a href="/dashboard" class="flex items-center gap-2 py-2">
                                <span class="w-4 h-4 flex items-center justify-center" v-html="ChartBarIcon"></span>
                                Dashboard
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    `
};
