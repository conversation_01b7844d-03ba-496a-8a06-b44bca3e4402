/**
 * Toast Notification Component
 *
 * A reusable toast notification component with support for different types and loading state
 * Follows Apple/OpenAI-inspired aesthetic with clean design
 */
export default {
    name: 'ToastNotification',
    props: {
        /**
         * The message to display in the toast
         */
        message: {
            type: String,
            required: true
        },
        /**
         * The type of toast notification
         * Possible values: 'success', 'error', 'info', 'warning'
         */
        type: {
            type: String,
            default: 'success',
            validator: (value) => ['success', 'error', 'info', 'warning'].includes(value)
        },
        /**
         * Whether the toast is visible
         */
        isVisible: {
            type: Boolean,
            required: true
        },
        /**
         * Whether to show a loading spinner
         */
        isLoading: {
            type: Boolean,
            default: false
        }
    },
    template: `
        <div v-if="isVisible" class="toast toast-center toast-bottom z-50">
            <div class="alert glass-effect px-4 py-3" :class="{
                'alert-success': type === 'success',
                'alert-error': type === 'error',
                'alert-info': type === 'info',
                'alert-warning': type === 'warning'
            }">
                <!-- Loading Spinner -->
                <span v-if="isLoading" class="loading loading-spinner loading-xs mr-2"></span>
                
                <!-- Success Icon -->
                <svg v-else-if="type === 'success'" xmlns="http://www.w3.org/2000/svg" class="stroke-success flex-shrink-0 h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                </svg>
                
                <!-- Error Icon -->
                <svg v-else-if="type === 'error'" xmlns="http://www.w3.org/2000/svg" class="stroke-error flex-shrink-0 h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                
                <!-- Info Icon -->
                <svg v-else-if="type === 'info'" xmlns="http://www.w3.org/2000/svg" class="stroke-info flex-shrink-0 h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                
                <!-- Warning Icon -->
                <svg v-else-if="type === 'warning'" xmlns="http://www.w3.org/2000/svg" class="stroke-warning flex-shrink-0 h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
                
                <!-- Message -->
                <span class="text-sm font-medium">{{ message }}</span>
            </div>
        </div>
    `
};
