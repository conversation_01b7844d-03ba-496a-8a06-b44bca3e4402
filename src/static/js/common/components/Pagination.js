/**
 * Pagination Component
 *
 * 通用分页组件
 * 支持页码导航和页面大小选择
 */
import { computed } from 'vue';
import {
    ChevronLeftIcon,
    ChevronRightIcon,
    ChevronDoubleLeftIcon,
    ChevronDoubleRightIcon
} from '../../utils/Icons.js';

export default {
    name: 'Pagination',
    props: {
        currentPage: {
            type: Number,
            required: true
        },
        totalPages: {
            type: Number,
            required: true
        },
        pageSize: {
            type: Number,
            default: 20
        },
        totalCount: {
            type: Number,
            required: true
        },
        hasNext: {
            type: Boolean,
            default: false
        },
        hasPrevious: {
            type: Boolean,
            default: false
        },
        pageSizeOptions: {
            type: Array,
            default: () => [10, 20, 50, 100]
        }
    },
    emits: ['page-change', 'page-size-change'],
    setup(props, { emit }) {
        // 计算显示的页码范围
        const visiblePages = computed(() => {
            const current = props.currentPage;
            const total = props.totalPages;
            const delta = 2; // 当前页前后显示的页数
            
            let start = Math.max(1, current - delta);
            let end = Math.min(total, current + delta);
            
            // 确保显示足够的页码
            if (end - start < 2 * delta) {
                if (start === 1) {
                    end = Math.min(total, start + 2 * delta);
                } else if (end === total) {
                    start = Math.max(1, end - 2 * delta);
                }
            }
            
            const pages = [];
            for (let i = start; i <= end; i++) {
                pages.push(i);
            }
            
            return pages;
        });
        
        // 计算显示的记录范围
        const recordRange = computed(() => {
            const start = (props.currentPage - 1) * props.pageSize + 1;
            const end = Math.min(props.currentPage * props.pageSize, props.totalCount);
            return { start, end };
        });
        
        // 页码变化处理
        const handlePageChange = (page) => {
            if (page >= 1 && page <= props.totalPages && page !== props.currentPage) {
                emit('page-change', page);
            }
        };
        
        // 页面大小变化处理
        const handlePageSizeChange = (event) => {
            const newPageSize = parseInt(event.target.value);
            if (newPageSize !== props.pageSize) {
                emit('page-size-change', newPageSize);
            }
        };
        
        return {
            visiblePages,
            recordRange,
            handlePageChange,
            handlePageSizeChange,
            
            // 图标
            ChevronLeftIcon,
            ChevronRightIcon,
            ChevronDoubleLeftIcon,
            ChevronDoubleRightIcon
        };
    },
    template: `
        <div class="flex items-center justify-between">
            <!-- 记录信息 -->
            <div class="flex items-center space-x-4">
                <div class="text-sm text-gray-700">
                    显示第 <span class="font-medium">{{ recordRange.start }}</span> 到 
                    <span class="font-medium">{{ recordRange.end }}</span> 条，
                    共 <span class="font-medium">{{ totalCount }}</span> 条记录
                </div>
                
                <!-- 页面大小选择 -->
                <div class="flex items-center space-x-2">
                    <label class="text-sm text-gray-700">每页显示:</label>
                    <select
                        :value="pageSize"
                        @change="handlePageSizeChange"
                        class="border border-gray-300 rounded-md text-sm px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                        <option
                            v-for="size in pageSizeOptions"
                            :key="size"
                            :value="size"
                        >
                            {{ size }}
                        </option>
                    </select>
                </div>
            </div>
            
            <!-- 分页导航 -->
            <div class="flex items-center space-x-1">
                <!-- 首页 -->
                <button
                    @click="handlePageChange(1)"
                    :disabled="!hasPrevious"
                    class="relative inline-flex items-center px-2 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed rounded-l-md"
                >
                    <ChevronDoubleLeftIcon class="h-4 w-4" />
                </button>
                
                <!-- 上一页 -->
                <button
                    @click="handlePageChange(currentPage - 1)"
                    :disabled="!hasPrevious"
                    class="relative inline-flex items-center px-2 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                    <ChevronLeftIcon class="h-4 w-4" />
                </button>
                
                <!-- 页码 -->
                <template v-for="page in visiblePages" :key="page">
                    <button
                        @click="handlePageChange(page)"
                        :class="[
                            page === currentPage
                                ? 'bg-blue-50 border-blue-500 text-blue-600'
                                : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50',
                            'relative inline-flex items-center px-4 py-2 border text-sm font-medium'
                        ]"
                    >
                        {{ page }}
                    </button>
                </template>
                
                <!-- 省略号 -->
                <span
                    v-if="visiblePages[visiblePages.length - 1] < totalPages"
                    class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700"
                >
                    ...
                </span>
                
                <!-- 下一页 -->
                <button
                    @click="handlePageChange(currentPage + 1)"
                    :disabled="!hasNext"
                    class="relative inline-flex items-center px-2 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                    <ChevronRightIcon class="h-4 w-4" />
                </button>
                
                <!-- 末页 -->
                <button
                    @click="handlePageChange(totalPages)"
                    :disabled="!hasNext"
                    class="relative inline-flex items-center px-2 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed rounded-r-md"
                >
                    <ChevronDoubleRightIcon class="h-4 w-4" />
                </button>
            </div>
        </div>
    `
};
