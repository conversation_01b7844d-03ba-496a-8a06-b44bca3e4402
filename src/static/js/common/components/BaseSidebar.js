/**
 * BaseSidebar Component
 *
 * 基础侧边栏组件，提供通用的侧边栏结构和功能
 * 只包含侧边栏切换按钮，其他特定功能由使用此组件的子组件提供
 */
import { MenuIcon } from '../../utils/Icons.js';

export default {
    name: 'BaseSidebar',
    props: {
        isOpen: {
            type: Boolean,
            default: true
        },
        userInfo: {
            type: Object,
            required: true
        },
        title: {
            type: String,
            default: ''
        }
    },
    emits: ['close-sidebar', 'toggle-sidebar', 'scroll'],
    setup(props, { emit, slots }) {
        const toggleSidebar = () => {
            emit('toggle-sidebar');
        };

        const closeSidebar = () => {
            emit('close-sidebar');
        };

        return {
            toggleSidebar,
            closeSidebar,
            MenuIcon
        };
    },
    template: `
        <div class="h-full flex flex-col w-full text-base-content sidebar-bg">
            <!-- 固定头部 -->
            <div class="flex-none border-b border-base-300/10">
                <div class="flex items-center justify-between px-4 py-3 h-16">
                    <div class="flex items-center">
                        <!-- 侧边栏切换按钮 -->
                        <button class="btn btn-ghost btn-square" @click="toggleSidebar">
                            <span v-html="MenuIcon"></span>
                        </button>

                        <!-- 标题 -->
                        <slot name="title">
                            <span class="ml-2 text-lg font-medium">{{ title }}</span>
                        </slot>
                    </div>

                    <!-- 右侧操作按钮 -->
                    <slot name="header-actions"></slot>
                </div>
            </div>

            <!-- 滚动内容区域 -->
            <div class="flex-1 overflow-y-auto sidebar-scroll-container" @scroll="$emit('scroll', $event)">
                <slot></slot>
            </div>

            <!-- 底部区域 -->
            <div v-if="$slots.footer" class="flex-none border-t border-base-300/10 p-4">
                <slot name="footer"></slot>
            </div>
        </div>
    `
};
