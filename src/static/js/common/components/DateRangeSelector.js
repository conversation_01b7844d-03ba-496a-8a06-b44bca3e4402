/**
 * DateRangeSelector Component
 *
 * A reusable date range selector component that can be used across the dashboard
 * Uses a dropdown style for consistency
 */
import { ref, computed } from 'vue';

export default {
    name: 'DateRangeSelector',
    props: {
        /**
         * The currently selected range value
         */
        modelValue: {
            type: [String, Number],
            required: true
        },
        /**
         * Array of range options
         * Each option should have id and label properties
         */
        ranges: {
            type: Array,
            required: true
        },
        /**
         * Label text to display before the selector
         */
        label: {
            type: String,
            default: '时间范围'
        }
    },
    emits: ['update:modelValue'],
    setup(props, { emit }) {
        // Handle value change
        const handleChange = (value) => {
            emit('update:modelValue', value);
        };

        // Find the current range label
        const currentRangeLabel = computed(() => {
            const range = props.ranges.find(r => r.id === props.modelValue);
            return range ? range.label : '';
        });

        return {
            handleChange,
            currentRangeLabel
        };
    },
    template: `
        <div class="flex items-center gap-3">
            <span class="text-sm font-medium text-base-content/70">{{ label }}</span>

            <select
                class="select select-sm select-bordered w-36 focus:outline-none bg-base-100 border-base-300 text-base-content dark:border-base-content/20 dark:border"
                :value="modelValue"
                @change="(event) => handleChange(event.target.value)"
            >
                <option v-for="range in ranges" :key="range.id" :value="range.id">
                    {{ range.label }}
                </option>
            </select>
        </div>
    `
};
