/**
 * LoadingSpinner Component
 *
 * 通用加载动画组件
 */
export default {
    name: 'LoadingSpinner',
    props: {
        size: {
            type: String,
            default: 'medium',
            validator: (value) => ['small', 'medium', 'large'].includes(value)
        },
        color: {
            type: String,
            default: 'primary'
        }
    },
    computed: {
        sizeClass() {
            const sizeMap = {
                small: 'h-4 w-4',
                medium: 'h-6 w-6',
                large: 'h-8 w-8'
            };
            return sizeMap[this.size];
        },
        colorClass() {
            return `border-${this.color}`;
        }
    },
    template: `
        <div 
            class="animate-spin rounded-full border-2 border-t-transparent"
            :class="[sizeClass, colorClass]"
        ></div>
    `
};
