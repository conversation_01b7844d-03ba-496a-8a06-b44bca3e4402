/**
 * ContentContainer Component
 *
 * 侧边栏容器布局组件，处理桌面端和移动端的不同显示方式
 * 提供统一的侧边栏开关逻辑和响应式行为
 * 只负责侧边栏和内容区域的布局，不包含header和模态框
 */
import { computed } from 'vue';
import { zLayoutSidebar } from '../../utils/zIndex.js';

export default {
    name: 'ContentContainer',
    props: {
        isOpen: {
            type: Boolean,
            default: true
        }
    },
    emits: ['toggle'],
    setup(props, { emit, slots }) {
        const toggleSidebar = () => {
            emit('toggle');
        };

        // 检查是否有各种插槽
        const hasMainContent = computed(() => {
            return !!slots.mainContent;
        });

        const hasSidebar = computed(() => {
            return !!slots.sidebar;
        });

        return {
            toggleSidebar,
            hasMainContent,
            hasSidebar,
            zLayoutSidebar
        };
    },
    template: `
        <div class="relative flex-1 overflow-hidden">
            <!-- Desktop Sidebar (fixed, at the top) - Safari 兼容性修复版本 -->
            <div v-if="hasSidebar" class="hidden lg:block desktop-sidebar sidebar-bg"
                 :class="{ 'sidebar-closed': !isOpen }">
                <div class="h-full sidebar-container">
                    <slot name="sidebar"></slot>
                </div>
            </div>

            <!-- Mobile View: Using Custom Drawer Component -->
            <div v-if="hasSidebar && hasMainContent" class="custom-drawer custom-drawer-mobile lg:hidden" :class="{ 'drawer-open': isOpen }">
                <!-- Main Content -->
                <div class="custom-drawer-content">
                    <slot name="mainContent"></slot>
                </div>

                <!-- Sidebar -->
                <div class="custom-drawer-side">
                    <div class="w-64 h-full sidebar-bg border-r border-base-300/10 sidebar-container" @click.stop @touchend.stop>
                        <slot name="sidebar"></slot>
                    </div>
                </div>

                <!-- Overlay -->
                <div class="custom-drawer-overlay" @click="toggleSidebar" @touchend="toggleSidebar"></div>
            </div>

            <!-- Desktop View: Regular Layout - Safari 兼容性修复版本 -->
            <div v-if="hasMainContent" class="hidden lg:flex lg:flex-1 desktop-content" :class="{ 'sidebar-closed': !isOpen }">
                <slot name="mainContent"></slot>
            </div>
        </div>
    `
};
