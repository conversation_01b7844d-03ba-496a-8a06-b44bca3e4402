/**
 * User Service
 *
 * Provides API services for user data
 * Centralizes all API calls for better separation of concerns
 */

/**
 * Fetch unique users list
 * @returns {Promise<Array>} List of unique users
 */
export const fetchUsers = async () => {
    try {
        const response = await fetch('/api/dashboard/users');
        
        if (!response.ok) {
            throw new Error(`Failed to fetch users: ${response.status} ${response.statusText}`);
        }
        
        const data = await response.json();
        return data.users || [];
    } catch (error) {
        console.error('Error fetching users:', error);
        throw error;
    }
};
