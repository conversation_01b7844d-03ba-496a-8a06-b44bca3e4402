/**
 * Department Service
 *
 * 提供部门数据的API服务
 * 集中管理所有部门相关的API调用，实现更好的关注点分离
 */

/**
 * 获取所有一级部门列表
 * @returns {Promise<Array>} 部门名称列表
 */
export const fetchDepartments = async () => {
    try {
        const response = await fetch('/api/dashboard/departments');
        
        if (!response.ok) {
            throw new Error(`获取部门列表失败: ${response.status} ${response.statusText}`);
        }
        
        const data = await response.json();
        return data.departments || [];
    } catch (error) {
        console.error('获取部门列表时发生错误:', error);
        throw error;
    }
};

/**
 * 获取部门列表（移除重试机制，架构已稳定）
 * @returns {Promise<Array>} 部门名称列表
 */
export const fetchDepartmentsWithRetry = async () => {
    try {
        return await fetchDepartments();
    } catch (error) {
        console.error('获取部门列表失败:', error.message);
        throw error;
    }
};
