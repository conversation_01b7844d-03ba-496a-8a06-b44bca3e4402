/**
 * Agent Service
 * 
 * Service for fetching agent-related data from the dashboard API
 */

/**
 * Fetch all available agents
 * @returns {Promise<Array>} Promise that resolves to an array of agent objects
 */
export async function fetchAgents() {
    try {
        const response = await fetch('/api/dashboard/agents');
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        return data.agents || [];
    } catch (error) {
        console.error('Error fetching agents:', error);
        throw error;
    }
}
