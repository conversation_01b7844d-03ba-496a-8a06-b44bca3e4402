/**
 * DepartmentTopQuestionsLayout Component
 *
 * 部门Top10常问清单页面布局组件
 * 提供筛选、表格展示和手动触发分析功能
 *
 * Cache-Buster: 20241225-v2
 */
import { ref, reactive, onMounted, watch, computed } from 'vue';
import DepartmentTopQuestionsTable from '../components/DepartmentTopQuestionsTable.js';
import DateRangePicker from '../../common/components/DateRangePicker.js';
import GroupSection from '../components/GroupSection.js';
import ToastNotification from '../../common/components/ToastNotification.js';

import {
    ChartBarIcon,
    CalendarIcon,
    BuildingOfficeIcon,
    PlayIcon,
    ArrowPathIcon,
    ExclamationTriangleIcon
} from '../../utils/Icons.js';

export default {
    name: 'DepartmentTopQuestionsLayout',
    components: {
        DepartmentTopQuestionsTable,
        DateRangePicker,
        GroupSection,
        ToastNotification
    },
    setup() {
        // 加载状态
        const isLoading = ref(true);
        const isTriggering = ref(false);
        
        // 数据状态
        const topQuestionsData = ref([]);
        const departments = ref([]);
        const pagination = ref({
            total_count: 0,
            page: 1,
            page_size: 20,
            total_pages: 0,
            has_next_page: false,
            has_previous_page: false
        });
        
        // 筛选条件
        const filters = reactive({
            department_name: '',
            week_start_date: '',
            week_end_date: '',
            analysis_status: '',
            page: 1,
            page_size: 20
        });

        // 计算上周的日期范围
        const getLastWeekRange = () => {
            const today = new Date();
            const dayOfWeek = today.getDay(); // 0 = 周日, 1 = 周一, ..., 6 = 周六
            const daysToLastMonday = dayOfWeek === 0 ? 8 : dayOfWeek + 6; // 如果是周日，往前8天；否则往前 dayOfWeek + 6 天

            const lastMonday = new Date(today);
            lastMonday.setDate(today.getDate() - daysToLastMonday);

            const lastSunday = new Date(lastMonday);
            lastSunday.setDate(lastMonday.getDate() + 6);

            return {
                start: lastMonday.toISOString().split('T')[0],
                end: lastSunday.toISOString().split('T')[0]
            };
        };

        // 检查是否包含未来时间
        const checkFutureDate = (dateStr) => {
            if (!dateStr) return false;

            const inputDate = new Date(dateStr);
            const today = new Date();
            today.setHours(23, 59, 59, 999); // 设置为今天的最后一刻

            return inputDate > today;
        };

        // 获取指定日期所在的自然周的周一
        const getWeekMonday = (dateStr) => {
            const date = new Date(dateStr);
            const dayOfWeek = date.getDay(); // 0 = 周日, 1 = 周一, ..., 6 = 周六
            const daysToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // 周日往前6天，其他往前 dayOfWeek - 1 天

            const monday = new Date(date);
            monday.setDate(date.getDate() - daysToMonday);

            return monday.toISOString().split('T')[0];
        };

        // 获取时间范围内的所有自然周
        const getWeeksInRange = (startDateStr, endDateStr) => {
            const weeks = [];
            const startDate = new Date(startDateStr);
            const endDate = new Date(endDateStr);

            // 获取开始日期所在周的周一
            let currentMonday = new Date(getWeekMonday(startDateStr));

            // 遍历每个周一，直到超出结束日期
            while (currentMonday <= endDate) {
                const mondayStr = currentMonday.toISOString().split('T')[0];

                // 计算这一周的周日
                const sunday = new Date(currentMonday);
                sunday.setDate(currentMonday.getDate() + 6);

                // 检查这一周是否与选择的时间范围有交集
                if (sunday >= startDate) {
                    weeks.push({
                        monday: mondayStr,
                        sunday: sunday.toISOString().split('T')[0]
                    });
                }

                // 移动到下一周
                currentMonday.setDate(currentMonday.getDate() + 7);
            }

            return weeks;
        };

        // 验证时间范围是否合法
        const validateDateRange = () => {
            // 检查是否选择了未来时间
            if (checkFutureDate(startDate.value) || checkFutureDate(endDate.value)) {
                showToast('不能选择未来时间进行分析', 'error');
                return false;
            }

            // 检查是否选择了时间范围
            if (!startDate.value || !endDate.value) {
                showToast('请选择完整的时间范围', 'warning');
                return false;
            }

            return true;
        };

        // 日期范围状态 - 默认显示上周
        const lastWeek = getLastWeekRange();
        const startDate = ref(lastWeek.start);
        const endDate = ref(lastWeek.end);

        // 初始化筛选条件为上周
        filters.week_start_date = lastWeek.start;
        filters.week_end_date = lastWeek.end;
        
        // 调度器状态
        const schedulerStatus = ref({
            is_running: false,
            jobs: []
        });
        
        // Toast通知状态
        const toast = reactive({
            isVisible: false,
            message: '',
            type: 'info',
            isLoading: false
        });
        
        // 计算属性
        const hasData = computed(() => topQuestionsData.value.length > 0);
        const totalRecords = computed(() => pagination.value.total_count);
        
        // 显示Toast通知
        const showToast = (message, type = 'info', duration = 3000, isLoading = false) => {
            toast.message = message;
            toast.type = type;
            toast.isVisible = true;
            toast.isLoading = isLoading;

            if (!isLoading && duration > 0) {
                setTimeout(() => {
                    toast.isVisible = false;
                }, duration);
            }
        };

        // 隐藏Toast通知
        const hideToast = () => {
            toast.isVisible = false;
        };
        
        // 获取部门Top10问题数据
        const fetchTopQuestionsData = async () => {
            try {
                isLoading.value = true;
                
                const params = new URLSearchParams();
                Object.keys(filters).forEach(key => {
                    if (filters[key] !== '' && filters[key] !== null && filters[key] !== undefined) {
                        params.append(key, filters[key]);
                    }
                });
                
                const response = await fetch(`/api/dashboard/department-top-questions?${params}`);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                topQuestionsData.value = data.items || [];
                pagination.value = data.pagination || pagination.value;
                
                console.log('获取Top10问题数据成功:', data);
                
            } catch (error) {
                console.error('获取Top10问题数据失败:', error);
                showToast('获取数据失败: ' + error.message, 'error');
                topQuestionsData.value = [];
            } finally {
                isLoading.value = false;
            }
        };
        
        // 获取部门列表
        const fetchDepartments = async () => {
            try {
                const response = await fetch('/api/dashboard/department-top-questions/departments');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                departments.value = data.departments || [];
                
                console.log('获取部门列表成功:', departments.value);
                
            } catch (error) {
                console.error('获取部门列表失败:', error);
                showToast('获取部门列表失败: ' + error.message, 'error');
            }
        };
        
        // 获取调度器状态
        const fetchSchedulerStatus = async () => {
            try {
                const response = await fetch('/api/scheduler/weekly-analysis/status');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                schedulerStatus.value = data.status || schedulerStatus.value;
                
                console.log('获取调度器状态成功:', schedulerStatus.value);
                
            } catch (error) {
                console.error('获取调度器状态失败:', error);
                showToast('获取调度器状态失败: ' + error.message, 'error');
            }
        };
        
        // 手动触发周度分析
        const triggerWeeklyAnalysis = async (targetWeekStart = null) => {
            try {
                // 如果没有指定目标周开始时间，验证当前选中的时间范围并分析所有自然周
                if (!targetWeekStart) {
                    if (!validateDateRange()) {
                        return; // 验证失败，直接返回
                    }

                    // 获取时间范围内的所有自然周
                    const weeks = getWeeksInRange(startDate.value, endDate.value);

                    if (weeks.length === 0) {
                        showToast('选择的时间范围内没有完整的自然周', 'warning');
                        return;
                    }

                    isTriggering.value = true;

                    // 显示开始分析的消息
                    if (weeks.length === 1) {
                        showToast(`已开始分析 ${weeks[0].monday} 至 ${weeks[0].sunday} 的数据`, 'success');
                    } else {
                        showToast(`已开始分析 ${weeks.length} 个自然周的数据`, 'success');
                    }

                    // 依次分析每个自然周
                    for (let i = 0; i < weeks.length; i++) {
                        const week = weeks[i];
                        console.log(`开始分析第 ${i + 1}/${weeks.length} 个自然周: ${week.monday} 至 ${week.sunday}`);

                        const payload = {
                            target_week_start: week.monday
                        };

                        try {
                            const response = await fetch('/api/scheduler/weekly-analysis/trigger', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json'
                                },
                                body: JSON.stringify(payload)
                            });

                            if (!response.ok) {
                                const errorData = await response.json();
                                console.error(`分析周 ${week.monday} 失败:`, errorData);
                                // 继续分析其他周，不中断
                            } else {
                                console.log(`成功触发分析周 ${week.monday}`);
                            }
                        } catch (error) {
                            console.error(`分析周 ${week.monday} 请求失败:`, error);
                        }


                    }

                    // 刷新数据
                    setTimeout(() => {
                        fetchTopQuestionsData();
                        fetchSchedulerStatus();
                    }, 2000);

                } else {
                    // 分析指定的单个自然周
                    isTriggering.value = true;

                    const payload = {
                        target_week_start: targetWeekStart
                    };

                    const response = await fetch('/api/scheduler/weekly-analysis/trigger', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(payload)
                    });

                    if (!response.ok) {
                        const errorData = await response.json();
                        throw new Error(errorData.error || `HTTP ${response.status}`);
                    }

                    showToast('已开始分析，请稍后查看结果', 'success');

                    // 刷新数据
                    setTimeout(() => {
                        fetchTopQuestionsData();
                        fetchSchedulerStatus();
                    }, 2000);
                }

            } catch (error) {
                console.error('触发周度分析失败:', error);
                showToast('触发分析失败: ' + error.message, 'error');
            } finally {
                isTriggering.value = false;
            }
        };
        
        // 手动触发部门分析
        const triggerDepartmentAnalysis = async (departmentName, targetWeekStart = null) => {
            try {
                // 如果没有指定目标周开始时间，验证当前选中的时间范围并分析所有自然周
                if (!targetWeekStart) {
                    if (!validateDateRange()) {
                        return; // 验证失败，直接返回
                    }

                    // 获取时间范围内的所有自然周
                    const weeks = getWeeksInRange(startDate.value, endDate.value);

                    if (weeks.length === 0) {
                        showToast('选择的时间范围内没有完整的自然周', 'warning');
                        return;
                    }

                    isTriggering.value = true;

                    // 显示开始分析的消息
                    if (weeks.length === 1) {
                        showToast(`已开始分析 ${departmentName} 在 ${weeks[0].monday} 至 ${weeks[0].sunday} 的数据`, 'success');
                    } else {
                        showToast(`已开始分析 ${departmentName} 在 ${weeks.length} 个自然周的数据`, 'success');
                    }

                    // 依次分析每个自然周
                    for (let i = 0; i < weeks.length; i++) {
                        const week = weeks[i];
                        console.log(`开始分析 ${departmentName} 第 ${i + 1}/${weeks.length} 个自然周: ${week.monday} 至 ${week.sunday}`);

                        const payload = {
                            department_name: departmentName,
                            target_week_start: week.monday
                        };

                        try {
                            const response = await fetch('/api/scheduler/weekly-analysis/trigger-department', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json'
                                },
                                body: JSON.stringify(payload)
                            });

                            if (!response.ok) {
                                const errorData = await response.json();
                                console.error(`分析 ${departmentName} 周 ${week.monday} 失败:`, errorData);
                                // 继续分析其他周，不中断
                            } else {
                                console.log(`成功触发分析 ${departmentName} 周 ${week.monday}`);
                            }
                        } catch (error) {
                            console.error(`分析 ${departmentName} 周 ${week.monday} 请求失败:`, error);
                        }


                    }

                    // 刷新数据
                    setTimeout(() => {
                        fetchTopQuestionsData();
                    }, 2000);

                } else {
                    // 分析指定的单个自然周
                    isTriggering.value = true;

                    const payload = {
                        department_name: departmentName,
                        target_week_start: targetWeekStart
                    };

                    const response = await fetch('/api/scheduler/weekly-analysis/trigger-department', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(payload)
                    });

                    if (!response.ok) {
                        const errorData = await response.json();
                        throw new Error(errorData.error || `HTTP ${response.status}`);
                    }

                    showToast('已开始分析，请稍后查看结果', 'success');

                    // 刷新数据
                    setTimeout(() => {
                        fetchTopQuestionsData();
                    }, 2000);
                }

            } catch (error) {
                console.error('触发部门分析失败:', error);
                showToast('触发部门分析失败: ' + error.message, 'error');
            } finally {
                isTriggering.value = false;
            }
        };
        
        // 处理筛选条件变化
        const handleFiltersChange = (newFilters) => {
            Object.assign(filters, newFilters);
            filters.page = 1; // 重置到第一页
            fetchTopQuestionsData();
        };

        // 应用筛选器
        const applyFilters = () => {
            filters.page = 1; // 重置到第一页
            fetchTopQuestionsData();
        };
        
        // 处理分页变化
        const handlePageChange = (page) => {
            filters.page = page;
            fetchTopQuestionsData();
        };
        
        // 处理页面大小变化
        const handlePageSizeChange = (pageSize) => {
            filters.page_size = pageSize;
            filters.page = 1; // 重置到第一页
            fetchTopQuestionsData();
        };
        
        // 刷新数据
        const refreshData = () => {
            fetchTopQuestionsData();
            fetchSchedulerStatus();
        };

        // 导出Excel
        const exportExcel = async (recordId) => {
            try {
                showToast('正在生成Excel文件...', 'info', 0, true);

                const response = await fetch(`/api/dashboard/department-top-questions/${recordId}/export`);

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.error || `HTTP ${response.status}`);
                }

                // 获取文件名
                const contentDisposition = response.headers.get('Content-Disposition');
                let filename = 'department_top_questions.xlsx';
                if (contentDisposition) {
                    const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
                    if (filenameMatch && filenameMatch[1]) {
                        filename = filenameMatch[1].replace(/['"]/g, '');
                    }
                }

                // 下载文件
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = filename;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);

                hideToast();
                showToast('Excel文件下载成功', 'success');

            } catch (error) {
                console.error('导出Excel失败:', error);
                hideToast();
                showToast('导出失败: ' + error.message, 'error');
            }
        };
        
        // 监听日期变化 - 只有开始和结束时间都选上时才调用
        watch([startDate, endDate], ([newStartDate, newEndDate]) => {
            filters.week_start_date = newStartDate;
            filters.week_end_date = newEndDate;

            // 只有当开始和结束时间都有值时，或者都为空时，才触发查询
            if ((newStartDate && newEndDate) || (!newStartDate && !newEndDate)) {
                filters.page = 1; // 重置到第一页
                fetchTopQuestionsData();
            }
        });

        // 组件挂载时初始化数据
        onMounted(async () => {
            await Promise.all([
                fetchDepartments(),
                fetchSchedulerStatus(),
                fetchTopQuestionsData()
            ]);
        });
        
        return {
            // 状态
            isLoading,
            isTriggering,
            topQuestionsData,
            departments,
            pagination,
            filters,
            schedulerStatus,
            toast,
            startDate,
            endDate,
            
            // 计算属性
            hasData,
            totalRecords,
            
            // 方法
            handleFiltersChange,
            applyFilters,
            handlePageChange,
            handlePageSizeChange,
            triggerWeeklyAnalysis,
            triggerDepartmentAnalysis,
            refreshData,
            exportExcel,
            showToast,
            hideToast,
            validateDateRange,
            getWeekMonday,
            getWeeksInRange,
            
            // 图标
            ChartBarIcon,
            CalendarIcon,
            BuildingOfficeIcon,
            PlayIcon,
            ArrowPathIcon,
            ExclamationTriangleIcon
        };
    },
    template: `
        <GroupSection group-id="department-top-questions">
            <template #title>
                <div class="flex flex-col md:flex-row items-start md:items-center justify-between gap-3">
                    <h2 class="text-l font-bold">部门Top10常问清单</h2>
                    <div class="flex flex-wrap items-center gap-3">
                        <!-- 部门筛选 -->
                        <div class="flex items-center gap-2">
                            <span class="text-sm font-medium text-base-content/70">部门</span>
                            <select
                                v-model="filters.department_name"
                                @change="applyFilters"
                                class="select select-sm h-9 select-bordered w-32 focus:outline-none bg-base-100 border-base-300 text-base-content"
                            >
                                <option value="">全部</option>
                                <option v-for="dept in departments" :key="dept" :value="dept">{{ dept }}</option>
                            </select>
                        </div>

                        <!-- 状态筛选 -->
                        <div class="flex items-center gap-2">
                            <span class="text-sm font-medium text-base-content/70">状态</span>
                            <select
                                v-model="filters.analysis_status"
                                @change="applyFilters"
                                class="select select-sm h-9 select-bordered w-24 focus:outline-none bg-base-100 border-base-300 text-base-content"
                            >
                                <option value="">全部</option>
                                <option value="0">待分析</option>
                                <option value="1">分析中</option>
                                <option value="2">已完成</option>
                                <option value="3">失败</option>
                            </select>
                        </div>

                        <!-- 时间范围筛选 -->
                        <div class="flex-none" style="max-width: 300px;">
                            <DateRangePicker
                                v-model:startDate="startDate"
                                v-model:endDate="endDate"
                                label=""
                                displayClass="input input-bordered input-sm h-9 w-full"
                            />
                        </div>

                        <!-- 刷新按钮 -->
                        <button
                            @click="refreshData"
                            :disabled="isLoading"
                            class="btn btn-sm h-9 btn-outline border-base-300 flex items-center gap-2 hover:bg-base-200 hover:border-base-300 hover:text-base-content disabled:opacity-50"
                        >
                            <span v-html="ArrowPathIcon" class="h-4 w-4" :class="{ 'animate-spin': isLoading }"></span>
                            刷新
                        </button>
                    </div>
                </div>
            </template>

            <!-- 调度器状态 -->
            <div v-if="schedulerStatus.is_running !== undefined" class="mb-6">
                <div class="flex items-center gap-2 bg-base-200/50 px-3 py-2 rounded-lg border border-base-300/50">
                    <div class="w-2 h-2 rounded-full" :class="schedulerStatus.is_running ? 'bg-success' : 'bg-error'"></div>
                    <span class="text-sm font-medium text-base-content/80">
                        调度器: {{ schedulerStatus.is_running ? '运行中' : '已停止' }}
                    </span>
                    <span v-if="schedulerStatus.jobs && schedulerStatus.jobs.length > 0" class="text-xs text-base-content/60 ml-auto">
                        下次执行: {{ schedulerStatus.jobs[0]?.next_run_time ? new Date(schedulerStatus.jobs[0].next_run_time).toLocaleString('zh-CN') : '未安排' }}
                    </span>
                </div>
            </div>

            <!-- 数据表格 -->
            <DepartmentTopQuestionsTable
                :data="topQuestionsData"
                :pagination="pagination"
                :is-loading="isLoading"
                :is-triggering="isTriggering"
                @page-change="handlePageChange"
                @page-size-change="handlePageSizeChange"
                @trigger-department-analysis="triggerDepartmentAnalysis"
                @trigger-weekly-analysis="triggerWeeklyAnalysis"
                @export-excel="exportExcel"
            />

            <!-- Toast通知 -->
            <ToastNotification
                :is-visible="toast.isVisible"
                :message="toast.message"
                :type="toast.type"
                :is-loading="toast.isLoading"
            />
        </GroupSection>
    `
};
