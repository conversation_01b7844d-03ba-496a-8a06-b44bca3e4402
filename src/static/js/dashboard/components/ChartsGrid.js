/**
 * ChartsGrid Component
 *
 * 3-column grid with adaptive row height for dashboard charts
 * Responsive design: 1 column on mobile, 2 on tablet, 3 on desktop
 */
import { ref, reactive } from 'vue';
import BarCard from './cards/BarCard.js';

export default {
    name: 'ChartsGrid',
    components: {
        BarCard
    },
    props: {
        charts: {
            type: Array,
            required: true,
            // Expected format for each chart:
            // {
            //   title: String,
            //   subtitle: String,
            //   type: String ('bar', 'line', etc.),
            //   data: Object (chart data object)
            // }
        },
        isLoading: {
            type: Boolean,
            default: false
        }
    },
    setup(props) {
        // Default colors for charts if not provided
        const defaultColors = {
            light: {
                backgroundColor: 'rgba(79, 70, 229, 0.3)',
                borderColor: 'rgb(79, 70, 229)'
            },
            dark: {
                backgroundColor: 'rgba(129, 140, 248, 0.3)',
                borderColor: 'rgb(129, 140, 248)'
            }
        };

        return {
            defaultColors
        };
    },
    template: `
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <BarCard
                v-for="(chart, index) in charts"
                :key="index"
                :title="chart.title"
                :subtitle="chart.subtitle"
                :chart-data="chart.data"
                :loading="isLoading"
                size="lg"
                :bar-colors="chart.colors || defaultColors"
                :chart-options="chart.chartOptions || {}"
            />
        </div>
    `
};
