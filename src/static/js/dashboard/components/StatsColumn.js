/**
 * StatsColumn Component
 *
 * Horizontal row of stats cards with responsive layout
 * Contains stats cards showing metric changes in a single row
 * Note: Title and filters are now handled by GroupSection component
 */
import { ref, reactive, computed } from 'vue';
import StatsCard from './cards/StatsCard.js';
import { UserIcon, ChatBubbleLeftRightIcon, MagnifyingGlassIcon, ExclamationTriangleIcon } from '../../utils/Icons.js';

export default {
    name: 'StatsColumn',
    components: {
        StatsCard
    },
    props: {
        stats: {
            type: Array,
            default: () => [
                { label: 'Users', value: 0, icon: UserIcon, iconBgColor: 'bg-primary/10 dark:bg-primary/20', iconColor: 'text-primary' },
                { label: 'Chats', value: 0, icon: ChatBubbleLeftRightIcon, iconBgColor: 'bg-secondary/10 dark:bg-secondary/20', iconColor: 'text-secondary' },
                { label: 'Queries', value: 0, icon: MagnifyingGlassIcon, iconBgColor: 'bg-accent/10 dark:bg-accent/20', iconColor: 'text-accent' },
                { label: 'Bad Cases', value: 0, icon: ExclamationTriangleIcon, iconBgColor: 'bg-error/10 dark:bg-error/20', iconColor: 'text-error' }
            ]
        },
        isLoading: {
            type: Boolean,
            default: false
        }
    },
    setup(props) {
        return {};
    },
    template: `
        <div class="w-full">
            <!-- Stats cards grid - horizontal layout with responsive design -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <StatsCard
                    v-for="(stat, index) in stats"
                    :key="index"
                    :title="stat.label"
                    :subtitle="stat.subtitle"
                    :description="stat.description"
                    :value="stat.value"
                    :loading="isLoading"
                    :icon="stat.icon"
                    :icon-bg-color="stat.iconBgColor"
                    :icon-color="stat.iconColor"
                    size="sm"
                    class="h-16"
                />
            </div>
        </div>
    `
};
