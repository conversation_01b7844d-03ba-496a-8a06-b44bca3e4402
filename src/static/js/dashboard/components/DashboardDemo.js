/**
 * Dashboard Demo Component
 *
 * Demonstrates all dashboard card components with example data
 * Using GroupSection for consistent layout across dashboard sections
 */
import { ref, reactive, onMounted } from 'vue';
import BaseDashboardCard from './cards/BaseDashboardCard.js';
import StatsCard from './cards/StatsCard.js';
import BarCard from './cards/BarCard.js';
import TrendCard from './cards/TrendCard.js';
import StatsColumn from './StatsColumn.js';
import ChartsGrid from './ChartsGrid.js';
import GroupSection from './GroupSection.js';
import { ChartBarIcon, UserIcon, ChatBubbleLeftRightIcon, MagnifyingGlassIcon, ExclamationTriangleIcon } from '../../utils/Icons.js';

export default {
    name: 'DashboardDemo',
    components: {
        BaseDashboardCard,
        StatsCard,
        BarCard,
        TrendCard,
        StatsColumn,
        ChartsGrid,
        GroupSection
    },
    setup() {
        // Loading states
        const isLoading = ref(true);

        // Date range options
        const dateRanges = [
            { id: 'today', label: '今天' },
            { id: 'week', label: '最近7天' },
            { id: 'month', label: '最近30天' },
            { id: 'all', label: '全部时间' }
        ];
        const selectedDateRange = ref('week');

        // Admin filter state
        const filterAdmin = ref(false);

        // Stats data
        const statsData = reactive([
            { label: '用户数', value: 0, icon: UserIcon, iconBgColor: 'bg-primary/10 dark-mode-primary-bg', iconColor: 'text-primary' },
            { label: '会话数', value: 0, icon: ChatBubbleLeftRightIcon, iconBgColor: 'bg-secondary/10 dark-mode-secondary-bg', iconColor: 'text-secondary' },
            { label: '查询数', value: 0, icon: MagnifyingGlassIcon, iconBgColor: 'bg-accent/10 dark-mode-accent-bg', iconColor: 'text-accent' },
            { label: '问题数', value: 0, icon: ExclamationTriangleIcon, iconBgColor: 'bg-error/10 dark-mode-error-bg', iconColor: 'text-error' }
        ]);

        // Bar chart data for top users
        const topUsersData = reactive({
            labels: [],
            datasets: [
                {
                    label: '会话数',
                    data: []
                }
            ]
        });

        // Bar chart data for top domains
        const topDomainsData = reactive({
            labels: [],
            datasets: [
                {
                    label: '查询数',
                    data: []
                }
            ]
        });

        // Charts collection for ChartsGrid
        const chartsData = reactive([
            {
                title: '活跃用户排名',
                subtitle: '按会话数量统计',
                type: 'bar',
                data: topUsersData,
                colors: {
                    light: {
                        backgroundColor: 'rgba(79, 70, 229, 0.3)',
                        borderColor: 'rgb(79, 70, 229)'
                    },
                    dark: {
                        backgroundColor: 'rgba(129, 140, 248, 0.3)',
                        borderColor: 'rgb(129, 140, 248)'
                    }
                }
            },
            {
                title: '热门领域排名',
                subtitle: '按查询数量统计',
                type: 'bar',
                data: topDomainsData,
                colors: {
                    light: {
                        backgroundColor: 'rgba(16, 185, 129, 0.3)',
                        borderColor: 'rgb(16, 185, 129)'
                    },
                    dark: {
                        backgroundColor: 'rgba(52, 211, 153, 0.3)',
                        borderColor: 'rgb(52, 211, 153)'
                    }
                }
            }
        ]);

        // Trend chart data
        const trendData = reactive({
            labels: [],
            datasets: [
                {
                    label: '查询数',
                    data: []
                },
                {
                    label: '问题数',
                    data: []
                }
            ]
        });

        // Simulate loading data from API
        const loadDashboardData = () => {
            isLoading.value = true;

            // Simulate API delay
            setTimeout(() => {
                // Update stats data
                statsData[0].value = 128;
                statsData[1].value = 1024;
                statsData[2].value = 4096;
                statsData[3].value = 24;

                // Update top users data
                topUsersData.labels = ['张三', '李四', '王五', '赵六', '钱七'];
                topUsersData.datasets[0].data = [42, 36, 28, 21, 15];

                // Update top domains data
                topDomainsData.labels = ['销售数据', '产品信息', '客户分析', '市场趋势', '财务报表'];
                topDomainsData.datasets[0].data = [156, 132, 98, 76, 54];

                // Update trend data
                trendData.labels = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
                trendData.datasets[0].data = [65, 78, 52, 91, 43, 56, 72];
                trendData.datasets[1].data = [12, 19, 8, 17, 10, 14, 11];

                isLoading.value = false;
            }, 1500);
        };

        // Handle date range change
        const handleDateRangeChange = (range) => {
            selectedDateRange.value = range;
            loadDashboardData();
        };

        // Handle admin filter change
        const handleFilterAdminChange = (value) => {
            filterAdmin.value = value;
            loadDashboardData();
        };

        // Load data when component is mounted
        onMounted(() => {
            loadDashboardData();
        });

        return {
            isLoading,
            dateRanges,
            selectedDateRange,
            filterAdmin,
            statsData,
            topUsersData,
            topDomainsData,
            trendData,
            chartsData,
            handleDateRangeChange,
            handleFilterAdminChange,
            // Icons
            ChartBarIcon,
            UserIcon,
            ChatBubbleLeftRightIcon,
            MagnifyingGlassIcon,
            ExclamationTriangleIcon
        };
    },
    template: `
        <div class="dashboard-demo py-6 md:py-8 space-y-10 w-full">
            <!-- Group 1: Overview with StatsColumn and ChartsGrid -->
            <GroupSection group-id="overview">
                <template #title>
                    <div class="flex flex-col md:flex-row items-start md:items-center justify-between gap-3">
                        <h2 class="text-xl font-bold">统计信息</h2>

                        <div class="flex flex-col sm:flex-row items-end sm:items-center gap-3">
                            <!-- Date Range Selector using Select -->
                            <div class="flex items-center gap-2">
                                <span class="text-sm font-medium text-base-content/70">时间范围</span>
                                <select
                                    class="select select-sm select-bordered w-36 focus:outline-none bg-base-100 border-base-300 text-base-content"
                                    :value="selectedDateRange"
                                    @change="(event) => handleDateRangeChange(event.target.value)"
                                >
                                    <option v-for="range in dateRanges" :key="range.id" :value="range.id">
                                        {{ range.label }}
                                    </option>
                                </select>
                            </div>

                            <!-- Admin Filter Toggle -->
                            <div class="flex items-center gap-2 bg-base-200/50 px-3 py-1.5 rounded-lg border border-base-300/50">
                                <span class="text-sm font-medium text-base-content/80">过滤管理员数据</span>
                                <label class="cursor-pointer inline-flex items-center">
                                    <input
                                        type="checkbox"
                                        class="sr-only peer"
                                        :checked="filterAdmin"
                                        @change="(event) => handleFilterAdminChange(event.target.checked)"
                                    />
                                    <div class="relative w-10 h-5 bg-base-300 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-primary"></div>
                                </label>
                            </div>
                        </div>
                    </div>
                </template>

                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <!-- Left Column - Stats -->
                    <div class="lg:col-span-1">
                        <StatsColumn :stats="statsData" :is-loading="isLoading" />
                    </div>
                    <!-- Right Column - Charts -->
                    <div class="lg:col-span-2">
                        <ChartsGrid :charts="chartsData" :is-loading="isLoading" />
                    </div>
                </div>
            </GroupSection>

            <!-- Group 2: Trend -->
            <GroupSection group-id="trend">
                <template #title>
                    <div class="flex items-center justify-between">
                        <h2 class="text-xl font-bold">趋势分析</h2>
                    </div>
                </template>

                <TrendCard
                    title="查询与问题趋势"
                    subtitle="最近7天数据分析"
                    :chart-data="trendData"
                    :loading="isLoading"
                    :line-colors="[
                        {
                            light: 'rgb(59, 130, 246)',
                            dark: 'rgb(96, 165, 250)'
                        },
                        {
                            light: 'rgb(236, 72, 153)',
                            dark: 'rgb(244, 114, 182)'
                        }
                    ]"
                    size="xl"
                />
            </GroupSection>

            <!-- Group 3: Conversation List (Placeholder) -->
            <GroupSection group-id="conversations">
                <template #title>
                    <div class="flex items-center justify-between">
                        <h2 class="text-xl font-bold">会话列表</h2>
                    </div>
                </template>

                <BaseDashboardCard
                    :loading="isLoading"
                    size="xl"
                >
                    <div class="text-center text-base-content/70 py-8">
                        会话列表组件将在后续实现
                    </div>
                </BaseDashboardCard>
            </GroupSection>
        </div>
    `
};
