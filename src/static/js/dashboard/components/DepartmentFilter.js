/**
 * Department Filter Component
 *
 * 简化的部门筛选器组件，只包含部门选择功能
 * 复用现有的部门服务和UI样式，与ConversationFilters保持一致
 */
import { ref, onMounted } from 'vue';
import { zLayoutHeader } from '../../utils/zIndex.js';
import { fetchDepartmentsWithRetry } from '../services/departmentService.js';

export default {
    name: 'DepartmentFilter',
    props: {
        /**
         * 初始选中的部门
         */
        initialDepartment: {
            type: String,
            default: ''
        }
    },
    emits: ['department-change'],
    setup(props, { emit }) {
        // 部门数据状态
        const departments = ref([]);
        const isLoadingDepartments = ref(false);
        const departmentsLoaded = ref(false);
        const selectedDepartment = ref(props.initialDepartment);

        // 高级筛选器下拉状态
        const showFilters = ref(false);
        const filtersDropdown = ref(null);

        // 加载部门数据
        const loadDepartments = async () => {
            if (departmentsLoaded.value) {
                return;
            }

            try {
                isLoadingDepartments.value = true;
                const departmentsList = await fetchDepartmentsWithRetry();
                departments.value = departmentsList;
                departmentsLoaded.value = true;
            } catch (error) {
                console.error('Error loading departments:', error);
                departments.value = [];
            } finally {
                isLoadingDepartments.value = false;
            }
        };

        // 切换筛选器显示状态
        const toggleFilters = () => {
            showFilters.value = !showFilters.value;
        };

        // 处理部门选择变化
        const handleDepartmentChange = () => {
            emit('department-change', selectedDepartment.value);
        };

        // 重置筛选器
        const resetFilter = () => {
            selectedDepartment.value = '';
            emit('department-change', '');
            showFilters.value = false;
        };

        // 应用筛选器并关闭下拉菜单
        const applyFiltersAndClose = () => {
            emit('department-change', selectedDepartment.value);
            showFilters.value = false;
        };

        // 处理点击外部关闭下拉菜单
        const handleClickOutside = (event) => {
            if (filtersDropdown.value && !filtersDropdown.value.contains(event.target)) {
                showFilters.value = false;
            }
        };

        // 组件挂载时加载部门数据
        onMounted(() => {
            loadDepartments();
            document.addEventListener('click', handleClickOutside);
        });

        // 组件卸载时移除事件监听
        const onUnmounted = () => {
            document.removeEventListener('click', handleClickOutside);
        };

        return {
            departments,
            isLoadingDepartments,
            selectedDepartment,
            showFilters,
            filtersDropdown,
            toggleFilters,
            handleDepartmentChange,
            resetFilter,
            applyFiltersAndClose,
            zLayoutHeader
        };
    },
    template: `
    <div class="dropdown flex-none relative" ref="filtersDropdown">
        <label
            tabindex="0"
            class="btn btn-sm h-9 btn-outline border-base-300 flex items-center gap-2 hover:bg-base-200 hover:border-base-300 hover:text-base-content dark:border-base-content/20 dark:hover:border-base-content/30 dark:border"
            :class="{ 'bg-base-200 dark:bg-base-content/10': showFilters }"
            @click="toggleFilters"
        >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <line x1="4" x2="4" y1="21" y2="14" />
                <line x1="4" x2="4" y1="10" y2="3" />
                <line x1="12" x2="12" y1="21" y2="12" />
                <line x1="12" x2="12" y1="8" y2="3" />
                <line x1="20" x2="20" y1="21" y2="16" />
                <line x1="20" x2="20" y1="12" y2="3" />
                <line x1="2" x2="6" y1="14" y2="14" />
                <line x1="10" x2="14" y1="8" y2="8" />
                <line x1="18" x2="22" y1="16" y2="16" />
            </svg>
            <span>筛选</span>
        </label>
        <div
            v-if="showFilters"
            tabindex="0"
            class="dropdown-content menu p-4 shadow bg-base-100 dark:bg-base-100 rounded-box w-64 mt-2 border border-base-300 dark:border-base-content/20"
            :style="{ zIndex: zLayoutHeader + 1, maxWidth: '90vw', position: 'absolute', right: 0 }"
        >
            <div class="flex flex-col gap-4">
                <!-- Department Filter -->
                <div class="form-control">
                    <label class="label py-1">
                        <span class="label-text">一级部门</span>
                        <span v-if="isLoadingDepartments" class="loading loading-spinner loading-xs ml-2"></span>
                    </label>
                    <select
                        v-model="selectedDepartment"
                        class="select select-bordered select-sm h-9 w-full text-sm dark:border-base-content/20 dark:border"
                        :disabled="isLoadingDepartments"
                    >
                        <option value="">{{ isLoadingDepartments ? '加载中...' : '全部部门' }}</option>
                        <option v-if="!isLoadingDepartments && departments.length === 0" value="" disabled>
                            暂无部门数据
                        </option>
                        <option
                            v-for="department in departments"
                            :key="department"
                            :value="department"
                        >
                            {{ department }}
                        </option>
                    </select>
                </div>

                <!-- Action Buttons -->
                <div class="flex gap-2 mt-4">
                    <!-- Reset Button -->
                    <button
                        class="btn btn-sm btn-outline border-base-300 dark:border-base-content/20 flex-1 h-9 hover:bg-base-200"
                        @click="resetFilter"
                    >
                        重置
                    </button>

                    <!-- Apply Filters Button -->
                    <button
                        class="btn btn-sm flex-1 h-9 bg-blue-500 hover:bg-blue-600 text-white border-blue-600 hover:border-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800 dark:border-blue-800 dark:hover:border-blue-900"
                        @click="applyFiltersAndClose"
                    >
                        应用筛选
                    </button>
                </div>
            </div>
        </div>
    </div>
    `
};
