/**
 * DepartmentTopQuestionsTable Component
 *
 * 部门Top10常问清单表格组件
 * 显示分析结果，支持分页和详情展开
 *
 * Cache-Buster: 20241225-v2
 */
import { ref, computed } from 'vue';
import {
    ChevronDownIcon,
    ChevronRightIcon,
    PlayIcon,
    CheckCircleIcon,
    ExclamationCircleIcon,
    ClockIcon,
    XCircleIcon,
    DownloadIcon,
    ArrowPathIcon
} from '../../utils/Icons.js';

export default {
    name: 'DepartmentTopQuestionsTable',
    components: {
    },
    props: {
        data: {
            type: Array,
            default: () => []
        },
        pagination: {
            type: Object,
            default: () => ({})
        },
        isLoading: {
            type: Boolean,
            default: false
        },
        isTriggering: {
            type: Boolean,
            default: false
        }
    },
    emits: ['page-change', 'page-size-change', 'trigger-department-analysis', 'export-excel'],
    setup(props, { emit }) {
        // 展开状态
        const expandedRows = ref(new Set());
        
        // 计算属性
        const hasData = computed(() => props.data && props.data.length > 0);
        
        // 获取分析状态显示信息
        const getStatusInfo = (status) => {
            const statusMap = {
                0: { text: '待分析', color: 'gray', icon: ClockIcon },
                1: { text: '分析中', color: 'yellow', icon: ClockIcon },
                2: { text: '分析完成', color: 'green', icon: CheckCircleIcon },
                3: { text: '分析失败', color: 'red', icon: XCircleIcon }
            };
            return statusMap[status] || { text: '未知', color: 'gray', icon: ExclamationCircleIcon };
        };
        
        // 切换行展开状态
        const toggleRowExpansion = (rowId) => {
            if (expandedRows.value.has(rowId)) {
                expandedRows.value.delete(rowId);
            } else {
                expandedRows.value.add(rowId);
            }
        };
        
        // 检查行是否展开
        const isRowExpanded = (rowId) => {
            return expandedRows.value.has(rowId);
        };
        
        // 触发部门分析
        const triggerAnalysis = (departmentName) => {
            emit('trigger-department-analysis', departmentName);
        };

        // 触发周度分析
        const triggerWeeklyAnalysis = () => {
            emit('trigger-weekly-analysis');
        };

        // 导出Excel
        const exportExcel = (recordId) => {
            emit('export-excel', recordId);
        };
        
        // 分页事件处理
        const handlePageChange = (page) => {
            emit('page-change', page);
        };
        
        const handlePageSizeChange = (pageSize) => {
            emit('page-size-change', pageSize);
        };
        
        return {
            expandedRows,
            hasData,
            getStatusInfo,
            toggleRowExpansion,
            isRowExpanded,
            triggerAnalysis,
            triggerWeeklyAnalysis,
            exportExcel,
            handlePageChange,
            handlePageSizeChange,

            // 图标
            ChevronDownIcon,
            ChevronRightIcon,
            PlayIcon,
            CheckCircleIcon,
            ExclamationCircleIcon,
            ClockIcon,
            XCircleIcon,
            DownloadIcon,
            ArrowPathIcon
        };
    },
    template: `
        <div class="department-top-questions-table">
            <!-- 加载状态 -->
            <div v-if="isLoading" class="flex justify-center items-center py-12 bg-base-100 border border-base-300/50 rounded-lg">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>

            <!-- 无数据状态 -->
            <div v-else-if="!hasData" class="text-center py-12 bg-base-100 border border-base-300/50 rounded-lg">
                <h3 class="text-sm font-medium text-base-content">暂无数据</h3>
                <p class="mt-1 text-sm text-base-content/60">还没有部门Top10常问清单数据，请先运行分析任务</p>
                <div class="mt-4">
                    <button
                        @click="triggerWeeklyAnalysis"
                        :disabled="isTriggering"
                        class="btn btn-sm h-9 btn-outline border-base-300 hover:bg-base-200 hover:border-base-300 hover:text-base-content disabled:opacity-50"
                    >
                        <span v-html="PlayIcon" class="h-3 w-3"></span>
                        {{ isTriggering ? '分析中...' : '开始分析' }}
                    </button>
                </div>
            </div>

            <!-- 数据表格 -->
            <div v-else-if="hasData" class="bg-base-100 border border-base-300/50 rounded-lg overflow-hidden">
                <div class="overflow-x-auto">
                    <table class="min-w-full">
                        <thead class="bg-base-200/50">
                            <tr>
                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-base-content/70 uppercase tracking-wider">
                                    部门
                                </th>
                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-base-content/70 uppercase tracking-wider">
                                    分析周期
                                </th>
                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-base-content/70 uppercase tracking-wider">
                                    统计数据
                                </th>
                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-base-content/70 uppercase tracking-wider">
                                    分析状态
                                </th>
                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-base-content/70 uppercase tracking-wider">
                                    更新时间
                                </th>
                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-base-content/70 uppercase tracking-wider">
                                    操作
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-base-100 divide-y divide-base-300/50">
                            <template v-for="item in data" :key="item.id">
                                <!-- 主行 -->
                                <tr class="hover:bg-base-200/30 transition-colors">
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <div class="flex items-center gap-2">
                                            <button
                                                @click="toggleRowExpansion(item.id)"
                                                class="p-1 rounded hover:bg-base-200 transition-colors flex items-center justify-center"
                                                :disabled="!item.top_questions || item.top_questions.length === 0"
                                                :title="item.top_questions && item.top_questions.length > 0 ? '展开查看详情' : '暂无数据'"
                                            >
                                                <span
                                                    v-if="!isRowExpanded(item.id)"
                                                    v-html="ChevronRightIcon"
                                                    class="h-4 w-4 text-base-content"
                                                    :class="{ 'text-base-content/40': !item.top_questions || item.top_questions.length === 0 }"
                                                ></span>
                                                <span
                                                    v-else
                                                    v-html="ChevronDownIcon"
                                                    class="h-4 w-4 text-base-content"
                                                ></span>
                                            </button>
                                            <div class="text-sm font-medium text-base-content">
                                                {{ item.department_name }}
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <div class="text-sm text-base-content">{{ item.week_display }}</div>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <div class="text-sm">
                                            <div class="text-base-content">对话: {{ item.total_conversations }}</div>
                                            <div class="text-base-content/60">查询: {{ item.total_user_queries }}</div>
                                        </div>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <div class="flex items-center gap-2">
                                            <span
                                                v-html="getStatusInfo(item.analysis_status).icon"
                                                class="h-3 w-3"
                                                :class="{
                                                    'text-base-content/40': getStatusInfo(item.analysis_status).color === 'gray',
                                                    'text-warning': getStatusInfo(item.analysis_status).color === 'yellow',
                                                    'text-success': getStatusInfo(item.analysis_status).color === 'green',
                                                    'text-error': getStatusInfo(item.analysis_status).color === 'red'
                                                }"
                                            ></span>
                                            <span
                                                class="inline-flex px-2 py-0.5 text-xs font-medium rounded-full"
                                                :class="{
                                                    'bg-base-200 text-base-content/70': getStatusInfo(item.analysis_status).color === 'gray',
                                                    'bg-warning/20 text-warning': getStatusInfo(item.analysis_status).color === 'yellow',
                                                    'bg-success/20 text-success': getStatusInfo(item.analysis_status).color === 'green',
                                                    'bg-error/20 text-error': getStatusInfo(item.analysis_status).color === 'red'
                                                }"
                                            >
                                                {{ getStatusInfo(item.analysis_status).text }}
                                            </span>
                                        </div>
                                        <div v-if="item.error_message" class="text-xs text-error mt-1 max-w-xs truncate">
                                            {{ item.error_message }}
                                        </div>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap text-sm text-base-content/60">
                                        {{ item.updated_at ? new Date(item.updated_at).toLocaleString('zh-CN') : '-' }}
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <!-- 分析完成时显示导出按钮 -->
                                        <button
                                            v-if="item.analysis_status === 2"
                                            @click="exportExcel(item.id)"
                                            class="btn btn-xs btn-outline border-base-300 hover:bg-base-200 hover:border-base-300 hover:text-base-content"
                                            title="导出Excel报告"
                                        >
                                            <span v-html="DownloadIcon" class="h-3 w-3"></span>
                                            导出
                                        </button>
                                        <!-- 分析失败时显示重新分析按钮 -->
                                        <button
                                            v-else-if="item.analysis_status === 3"
                                            @click="triggerAnalysis(item.department_name)"
                                            class="btn btn-xs btn-outline border-base-300 hover:bg-base-200 hover:border-base-300 hover:text-base-content"
                                            title="重新分析"
                                        >
                                            <span v-html="ArrowPathIcon" class="h-3 w-3"></span>
                                            重新分析
                                        </button>
                                        <!-- 其他状态不显示任何操作 -->
                                    </td>
                                </tr>
                                
                                <!-- 展开行 - Top10问题详情 -->
                                <tr v-if="isRowExpanded(item.id) && item.top_questions && item.top_questions.length > 0">
                                    <td colspan="6" class="px-4 py-4 bg-base-200/30">
                                        <div class="space-y-3">
                                            <h4 class="text-sm font-medium text-base-content mb-3">Top10常问问题</h4>
                                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-3">
                                                <div
                                                    v-for="question in item.top_questions"
                                                    :key="question.rank"
                                                    class="bg-base-100 rounded-lg p-3 border border-base-300/50"
                                                >
                                                    <div class="flex items-start justify-between mb-2">
                                                        <div class="flex items-center gap-2">
                                                            <span class="inline-flex items-center justify-center w-5 h-5 bg-primary/20 text-primary text-xs font-medium rounded-full">
                                                                {{ question.rank }}
                                                            </span>
                                                            <span class="text-xs font-medium text-base-content/70 bg-base-200 px-2 py-0.5 rounded">
                                                                {{ question.question_category }}
                                                            </span>
                                                        </div>
                                                        <div class="text-right">
                                                            <div class="text-sm font-medium text-base-content">{{ question.frequency }}次</div>
                                                            <div class="text-xs text-base-content/60">{{ question.percentage }}%</div>
                                                        </div>
                                                    </div>
                                                    <div class="mb-2">
                                                        <p class="text-sm font-medium text-base-content">{{ question.representative_question }}</p>
                                                    </div>
                                                    <div v-if="question.similar_questions && question.similar_questions.length > 0">
                                                        <p class="text-xs text-base-content/60 mb-1">相似问题:</p>
                                                        <div class="flex flex-wrap gap-1">
                                                            <span
                                                                v-for="(similar, index) in question.similar_questions.slice(0, 3)"
                                                                :key="index"
                                                                class="inline-block text-xs text-base-content/70 bg-base-200/50 px-2 py-0.5 rounded"
                                                            >
                                                                {{ similar }}
                                                            </span>
                                                            <span
                                                                v-if="question.similar_questions.length > 3"
                                                                class="inline-block text-xs text-base-content/50 px-2 py-0.5"
                                                            >
                                                                +{{ question.similar_questions.length - 3 }}个
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                </div>

                <!-- 数据统计 -->
                <div v-if="pagination && pagination.total_count > 0" class="px-4 py-3 border-t border-base-300/50 bg-base-200/30">
                    <div class="text-xs text-base-content/60">
                        共 {{ pagination.total_count }} 条记录
                        <span v-if="pagination.total_count > data.length">
                            ，当前显示前 {{ data.length }} 条
                        </span>
                    </div>
                </div>
            </div>
        </div>
    `
};
