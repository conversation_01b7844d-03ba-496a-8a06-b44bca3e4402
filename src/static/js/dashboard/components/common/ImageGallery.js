/**
 * ImageGallery Component
 * 
 * 用于显示图片列表的组件，支持点击放大查看
 * 适用于Dashboard和聊天界面
 */
import { ref } from 'vue';

export default {
    name: 'ImageGallery',
    props: {
        images: {
            type: Array,
            default: () => []
        },
        maxDisplay: {
            type: Number,
            default: 4 // 最多显示4张图片
        },
        size: {
            type: String,
            default: 'medium', // small, medium, large
            validator: (value) => ['small', 'medium', 'large'].includes(value)
        }
    },
    setup(props) {
        const showModal = ref(false);
        const currentImageIndex = ref(0);

        // 获取图片尺寸类名
        const getImageSizeClass = () => {
            switch (props.size) {
                case 'small':
                    return 'w-16 h-16';
                case 'large':
                    return 'w-32 h-32';
                default:
                    return 'w-24 h-24';
            }
        };

        // 获取网格布局类名
        const getGridClass = () => {
            const displayCount = Math.min(props.images.length, props.maxDisplay);
            if (displayCount === 1) return 'grid-cols-1';
            if (displayCount === 2) return 'grid-cols-2';
            if (displayCount <= 4) return 'grid-cols-2';
            return 'grid-cols-3';
        };

        // 打开图片模态框
        const openImageModal = (index) => {
            currentImageIndex.value = index;
            showModal.value = true;
        };

        // 关闭图片模态框
        const closeImageModal = () => {
            showModal.value = false;
        };

        // 切换到上一张图片
        const previousImage = () => {
            if (currentImageIndex.value > 0) {
                currentImageIndex.value--;
            } else {
                currentImageIndex.value = props.images.length - 1;
            }
        };

        // 切换到下一张图片
        const nextImage = () => {
            if (currentImageIndex.value < props.images.length - 1) {
                currentImageIndex.value++;
            } else {
                currentImageIndex.value = 0;
            }
        };

        // 键盘事件处理
        const handleKeydown = (event) => {
            if (!showModal.value) return;
            
            switch (event.key) {
                case 'Escape':
                    closeImageModal();
                    break;
                case 'ArrowLeft':
                    previousImage();
                    break;
                case 'ArrowRight':
                    nextImage();
                    break;
            }
        };

        // 监听键盘事件
        document.addEventListener('keydown', handleKeydown);

        return {
            showModal,
            currentImageIndex,
            getImageSizeClass,
            getGridClass,
            openImageModal,
            closeImageModal,
            previousImage,
            nextImage
        };
    },
    template: `
        <div v-if="images && images.length > 0" class="image-gallery">
            <!-- 图片网格 -->
            <div class="grid gap-2" :class="getGridClass()">
                <!-- 显示的图片 -->
                <div 
                    v-for="(image, index) in images.slice(0, maxDisplay)" 
                    :key="index"
                    class="relative cursor-pointer group"
                    @click="openImageModal(index)"
                >
                    <img
                        :src="image"
                        :alt="'图片 ' + (index + 1)"
                        :class="getImageSizeClass()"
                        class="object-cover rounded-lg border border-base-300 hover:opacity-80 transition-opacity"
                        loading="lazy"
                    />
                    <!-- 悬停效果 -->
                    <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-white opacity-0 group-hover:opacity-100 transition-opacity" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7"></path>
                        </svg>
                    </div>
                </div>
                
                <!-- 更多图片指示器 -->
                <div 
                    v-if="images.length > maxDisplay"
                    :class="getImageSizeClass()"
                    class="bg-base-200 rounded-lg border border-base-300 flex items-center justify-center cursor-pointer hover:bg-base-300 transition-colors"
                    @click="openImageModal(maxDisplay)"
                >
                    <div class="text-center">
                        <div class="text-lg font-semibold text-base-content">+{{ images.length - maxDisplay }}</div>
                        <div class="text-xs text-base-content/70">更多</div>
                    </div>
                </div>
            </div>

            <!-- 图片模态框 -->
            <div v-if="showModal" class="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50" @click="closeImageModal">
                <div class="relative max-w-[90vw] max-h-[90vh]" @click.stop>
                    <!-- 当前图片 -->
                    <img
                        :src="images[currentImageIndex]"
                        :alt="'图片 ' + (currentImageIndex + 1)"
                        class="max-w-full max-h-full object-contain rounded-lg"
                    />
                    
                    <!-- 关闭按钮 -->
                    <button
                        @click="closeImageModal"
                        class="absolute top-4 right-4 w-10 h-10 bg-black bg-opacity-50 hover:bg-opacity-70 rounded-full flex items-center justify-center text-white transition-all"
                    >
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                    
                    <!-- 导航按钮 -->
                    <div v-if="images.length > 1" class="absolute inset-y-0 left-0 flex items-center">
                        <button
                            @click="previousImage"
                            class="ml-4 w-12 h-12 bg-black bg-opacity-50 hover:bg-opacity-70 rounded-full flex items-center justify-center text-white transition-all"
                        >
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                            </svg>
                        </button>
                    </div>
                    
                    <div v-if="images.length > 1" class="absolute inset-y-0 right-0 flex items-center">
                        <button
                            @click="nextImage"
                            class="mr-4 w-12 h-12 bg-black bg-opacity-50 hover:bg-opacity-70 rounded-full flex items-center justify-center text-white transition-all"
                        >
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </button>
                    </div>
                    
                    <!-- 图片计数器 -->
                    <div v-if="images.length > 1" class="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-50 text-white px-3 py-1 rounded-full text-sm">
                        {{ currentImageIndex + 1 }} / {{ images.length }}
                    </div>
                </div>
            </div>
        </div>
    `
};
