/**
 * StatsCard Component
 *
 * Card component for displaying statistics with an optional icon
 * Extends BaseDashboardCard with specific styling for stats display
 */
import { computed } from 'vue';
import BaseDashboardCard from './BaseDashboardCard.js';

export default {
    name: 'StatsCard',
    components: {
        BaseDashboardCard
    },
    props: {
        /**
         * Card title
         */
        title: {
            type: String,
            default: ''
        },
        /**
         * Card subtitle
         */
        subtitle: {
            type: String,
            default: ''
        },
        /**
         * Main statistic value to display
         */
        value: {
            type: [Number, String],
            default: ''
        },
        /**
         * Optional description text
         */
        description: {
            type: String,
            default: ''
        },
        /**
         * SVG icon markup
         */
        icon: {
            type: String,
            default: ''
        },
        /**
         * Background color for icon container
         * Can be a Tailwind class or CSS color
         */
        iconBgColor: {
            type: String,
            default: 'bg-base-200 dark-mode-icon-bg'
        },
        /**
         * Icon color
         * Can be a Tailwind class or CSS color
         */
        iconColor: {
            type: String,
            default: 'text-primary'
        },
        /**
         * Whether the card is in a loading state
         */
        loading: {
            type: Boolean,
            default: false
        },
        /**
         * Additional CSS classes for the card
         */
        cardClass: {
            type: String,
            default: ''
        },
        /**
         * Card size (can be 'sm', 'md', 'lg', 'xl' or a Tailwind class like 'row-span-2')
         */
        size: {
            type: String,
            default: 'md'
        }
    },
    setup(props) {
        // Combine classes for the stats card
        const combinedCardClass = computed(() => {
            return `stats-card ${props.cardClass}`;
        });

        // Combine classes for the icon itself (not its container)
        const iconClass = computed(() => {
            // Reduced icon size for better proportions
            return `w-6 h-6 ${props.iconColor}`;
        });

        // Determine if we have an icon
        const hasIcon = computed(() => {
            return !!props.icon;
        });

        return {
            combinedCardClass,
            iconClass,
            hasIcon
        };
    },
    template: `
        <BaseDashboardCard
            :loading="false"
            :card-class="combinedCardClass"
            :size="size"
        >
            <template #default>
                <!-- Loading State for horizontal layout -->
                <template v-if="loading">
                    <div class="flex items-center gap-2.5 w-full h-full animate-pulse">
                        <!-- Loading Icon -->
                        <div class="flex-shrink-0 w-8 h-8 bg-base-300 rounded-lg"></div>
                        <!-- Loading Content -->
                        <div class="flex-1 min-w-0">
                            <div class="h-2.5 bg-base-300 rounded mb-1.5 w-14"></div>
                            <div class="h-4 bg-base-300 rounded w-10"></div>
                        </div>
                    </div>
                </template>

                <!-- Normal State: Horizontal layout: Icon + Title + Value in one row -->
                <template v-else>
                    <div class="flex items-center gap-2.5 w-full h-full">
                        <!-- Icon Container: Left side -->
                        <div
                            v-if="hasIcon || $slots.icon"
                            class="stats-icon-container flex-shrink-0 flex items-center justify-center p-2 rounded-lg shadow-sm"
                            :class="iconBgColor"
                        >
                            <slot name="icon">
                                <span :class="iconClass" v-html="icon"></span>
                            </slot>
                        </div>

                        <!-- Content: Title and Value -->
                        <div class="flex-1 min-w-0">
                            <!-- Title -->
                            <div v-if="title" class="text-xs font-medium text-base-content/75 truncate mb-0.5">
                                {{ title }}
                            </div>
                            <!-- Value -->
                            <div class="text-lg font-bold text-base-content tracking-tight leading-tight">
                                <slot name="value">{{ value }}</slot>
                            </div>
                            <!-- Subtitle/Description (if needed) -->
                            <div v-if="subtitle" class="text-xs text-base-content/60 truncate">
                                {{ subtitle }}
                            </div>
                            <div v-if="description || $slots.description" class="text-xs text-base-content/60 truncate">
                                <slot name="description">{{ description }}</slot>
                            </div>
                        </div>

                        <!-- Additional content slot -->
                        <div v-if="$slots.default && $slots.default().length > 0" class="flex-shrink-0">
                            <slot></slot>
                        </div>
                    </div>
                </template>
            </template>
        </BaseDashboardCard>
    `
};
