/**
 * AnalysisStatusCard Component
 *
 * 分析状态卡片组件
 * 显示调度器状态和下次执行时间
 */
import { computed } from 'vue';
import {
    CheckCircleIcon,
    XCircleIcon,
    ClockIcon,
    CalendarIcon,
    CogIcon
} from '../../utils/Icons.js';

export default {
    name: 'AnalysisStatusCard',
    props: {
        status: {
            type: Object,
            default: () => ({
                is_running: false,
                jobs: []
            })
        },
        isLoading: {
            type: Boolean,
            default: false
        }
    },
    setup(props) {
        // 计算属性
        const schedulerStatusInfo = computed(() => {
            if (props.isLoading) {
                return {
                    text: '加载中...',
                    color: 'gray',
                    icon: ClockIcon
                };
            }
            
            if (props.status.is_running) {
                return {
                    text: '运行中',
                    color: 'green',
                    icon: CheckCircleIcon
                };
            } else {
                return {
                    text: '已停止',
                    color: 'red',
                    icon: XCircleIcon
                };
            }
        });
        
        const nextRunTime = computed(() => {
            if (!props.status.jobs || props.status.jobs.length === 0) {
                return null;
            }
            
            const weeklyJob = props.status.jobs.find(job => 
                job.id && job.id.includes('weekly')
            );
            
            if (weeklyJob && weeklyJob.next_run_time) {
                try {
                    const date = new Date(weeklyJob.next_run_time);
                    return date.toLocaleString('zh-CN', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit',
                        weekday: 'short'
                    });
                } catch (e) {
                    return weeklyJob.next_run_time;
                }
            }
            
            return null;
        });
        
        const jobsCount = computed(() => {
            return props.status.jobs ? props.status.jobs.length : 0;
        });
        
        return {
            schedulerStatusInfo,
            nextRunTime,
            jobsCount,
            
            // 图标
            CheckCircleIcon,
            XCircleIcon,
            ClockIcon,
            CalendarIcon,
            CogIcon
        };
    },
    template: `
        <div class="bg-white shadow rounded-lg p-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <CogIcon class="h-6 w-6 text-gray-500" />
                    <div>
                        <h3 class="text-lg font-medium text-gray-900">分析调度器状态</h3>
                        <p class="text-sm text-gray-600">自动化周度分析任务调度</p>
                    </div>
                </div>
                
                <div class="flex items-center space-x-4">
                    <!-- 调度器状态 -->
                    <div class="flex items-center space-x-2">
                        <component 
                            :is="schedulerStatusInfo.icon"
                            class="h-5 w-5"
                            :class="{
                                'text-gray-400': schedulerStatusInfo.color === 'gray',
                                'text-green-400': schedulerStatusInfo.color === 'green',
                                'text-red-400': schedulerStatusInfo.color === 'red'
                            }"
                        />
                        <span 
                            class="inline-flex px-2 py-1 text-sm font-semibold rounded-full"
                            :class="{
                                'bg-gray-100 text-gray-800': schedulerStatusInfo.color === 'gray',
                                'bg-green-100 text-green-800': schedulerStatusInfo.color === 'green',
                                'bg-red-100 text-red-800': schedulerStatusInfo.color === 'red'
                            }"
                        >
                            {{ schedulerStatusInfo.text }}
                        </span>
                    </div>
                </div>
            </div>
            
            <!-- 详细信息 -->
            <div class="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
                <!-- 下次执行时间 -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <div class="flex items-center space-x-2 mb-2">
                        <CalendarIcon class="h-4 w-4 text-gray-500" />
                        <span class="text-sm font-medium text-gray-700">下次执行</span>
                    </div>
                    <div class="text-sm text-gray-900">
                        {{ nextRunTime || '未安排' }}
                    </div>
                </div>
                
                <!-- 任务数量 -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <div class="flex items-center space-x-2 mb-2">
                        <ClockIcon class="h-4 w-4 text-gray-500" />
                        <span class="text-sm font-medium text-gray-700">活跃任务</span>
                    </div>
                    <div class="text-sm text-gray-900">
                        {{ jobsCount }} 个任务
                    </div>
                </div>
                
                <!-- 调度器状态 -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <div class="flex items-center space-x-2 mb-2">
                        <CogIcon class="h-4 w-4 text-gray-500" />
                        <span class="text-sm font-medium text-gray-700">调度器</span>
                    </div>
                    <div class="text-sm text-gray-900">
                        {{ status.scheduler_state || '未知' }}
                    </div>
                </div>
            </div>
            
            <!-- 任务列表 -->
            <div v-if="status.jobs && status.jobs.length > 0" class="mt-4">
                <h4 class="text-sm font-medium text-gray-700 mb-2">任务详情</h4>
                <div class="space-y-2">
                    <div 
                        v-for="job in status.jobs" 
                        :key="job.id"
                        class="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                    >
                        <div>
                            <div class="text-sm font-medium text-gray-900">{{ job.name || job.id }}</div>
                            <div class="text-xs text-gray-500">{{ job.trigger }}</div>
                        </div>
                        <div class="text-right">
                            <div class="text-xs text-gray-500">下次执行</div>
                            <div class="text-sm text-gray-900">
                                {{ job.next_run_time ? new Date(job.next_run_time).toLocaleString('zh-CN') : '未安排' }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `
};
