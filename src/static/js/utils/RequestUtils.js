/**
 * 请求工具函数
 * 
 * 提供防抖、缓存和批处理等网络请求优化工具
 */

/**
 * 创建一个防抖请求包装器
 * 防止短时间内发送重复的请求，并合并相同参数的并发请求
 * 
 * @param {Function} requestFn - 原始请求函数，必须返回Promise
 * @param {Object} options - 配置选项
 * @param {number} options.debounceTime - 防抖时间（毫秒），默认300ms
 * @param {Function} options.getRequestKey - 自定义函数，用于从请求参数生成唯一键
 * @param {boolean} options.debug - 是否启用调试日志
 * @returns {Function} - 包装后的请求函数
 */
export function createDebouncedRequest(requestFn, options = {}) {
    // 默认选项
    const {
        debounceTime = 300,
        getRequestKey = (...args) => JSON.stringify(args),
        debug = false
    } = options;

    // 存储正在进行的请求
    const pendingRequests = {};
    
    // 记录最后一次请求时间
    let lastRequestTime = 0;

    // 日志函数
    const log = debug ? console.log : () => {};

    // 返回包装后的函数
    return async function(...args) {
        // 生成请求的唯一键
        const requestKey = getRequestKey(...args);
        
        // 检查是否有相同的请求正在进行中
        if (pendingRequests[requestKey]) {
            log(`[防抖] 重复请求被合并: ${requestKey}`);
            return pendingRequests[requestKey];
        }
        
        // 检查是否在防抖时间内
        const now = Date.now();
        if (now - lastRequestTime < debounceTime) {
            log(`[防抖] 请求被延迟: ${requestKey}`);
            
            // 等待防抖时间
            await new Promise(resolve => setTimeout(resolve, debounceTime));
            
            // 再次检查是否有相同的请求正在进行中
            if (pendingRequests[requestKey]) {
                log(`[防抖] 延迟后发现重复请求: ${requestKey}`);
                return pendingRequests[requestKey];
            }
        }
        
        // 更新最后请求时间
        lastRequestTime = Date.now();
        
        // 创建请求并存储
        const requestPromise = requestFn(...args);
        pendingRequests[requestKey] = requestPromise;
        
        try {
            // 等待请求完成
            const result = await requestPromise;
            return result;
        } finally {
            // 请求完成后，从挂起的请求中删除
            delete pendingRequests[requestKey];
        }
    };
}

/**
 * 创建一个带缓存的请求包装器
 * 缓存相同参数的请求结果，减少重复请求
 * 
 * @param {Function} requestFn - 原始请求函数，必须返回Promise
 * @param {Object} options - 配置选项
 * @param {number} options.cacheTime - 缓存有效期（毫秒），默认60000ms（1分钟）
 * @param {Function} options.getCacheKey - 自定义函数，用于从请求参数生成缓存键
 * @param {boolean} options.debug - 是否启用调试日志
 * @returns {Function} - 包装后的请求函数
 */
export function createCachedRequest(requestFn, options = {}) {
    // 默认选项
    const {
        cacheTime = 60000, // 默认缓存1分钟
        getCacheKey = (...args) => JSON.stringify(args),
        debug = false
    } = options;

    // 缓存存储
    const cache = {};
    
    // 日志函数
    const log = debug ? console.log : () => {};

    // 返回包装后的函数
    return async function(...args) {
        // 生成缓存键
        const cacheKey = getCacheKey(...args);
        
        // 检查缓存是否有效
        const cachedItem = cache[cacheKey];
        if (cachedItem && Date.now() - cachedItem.timestamp < cacheTime) {
            log(`[缓存] 使用缓存数据: ${cacheKey}`);
            return cachedItem.data;
        }
        
        // 执行实际请求
        log(`[缓存] 缓存未命中，发起请求: ${cacheKey}`);
        const result = await requestFn(...args);
        
        // 更新缓存
        cache[cacheKey] = {
            data: result,
            timestamp: Date.now()
        };
        
        return result;
    };
}

/**
 * 组合多个请求包装器
 * 可以将防抖、缓存等功能组合使用
 * 
 * @param {Function} requestFn - 原始请求函数
 * @param {Array<Function>} wrappers - 包装器函数数组，按从内到外的顺序应用
 * @returns {Function} - 组合后的请求函数
 */
export function composeRequestWrappers(requestFn, wrappers = []) {
    return wrappers.reduce((wrapped, wrapper) => wrapper(wrapped), requestFn);
}
