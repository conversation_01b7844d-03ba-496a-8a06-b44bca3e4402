/**
 * Theme management utility
 *
 * Handles theme switching and persistence using localStorage
 * Supports light, dark, and system themes
 */

// Media query for detecting system dark mode preference
const systemDarkModeQuery = window.matchMedia('(prefers-color-scheme: dark)');

// Check if system prefers dark mode
export function systemPrefersDark() {
  return systemDarkModeQuery.matches;
}

// Get the effective theme (resolves 'system' to actual theme)
export function getEffectiveTheme(theme) {
  if (theme === 'system') {
    return systemPrefersDark() ? 'dark' : 'light';
  }
  return theme;
}

// Get the current theme from localStorage (returns raw value including 'system')
export function getCurrentTheme() {
  return localStorage.getItem('theme') || 'system';
}

// Get the resolved theme (system preference is resolved to light/dark)
export function getResolvedTheme() {
  const currentTheme = getCurrentTheme();
  return getEffectiveTheme(currentTheme);
}

// Set the theme on the document
export function applyTheme(theme) {
  // Store the raw theme value (could be 'system')
  localStorage.setItem('theme', theme);

  // Apply the effective theme to the document
  const effectiveTheme = getEffectiveTheme(theme);
  document.documentElement.setAttribute('data-theme', effectiveTheme);

  return effectiveTheme === 'dark';
}

// Initialize theme on page load and set up system theme listener
export function initTheme() {
  const currentTheme = getCurrentTheme();
  const isDark = applyTheme(currentTheme);

  // Set up listener for system theme changes
  systemDarkModeQuery.addEventListener('change', (e) => {
    // Only update if current theme is 'system'
    if (getCurrentTheme() === 'system') {
      document.documentElement.setAttribute('data-theme', e.matches ? 'dark' : 'light');
    }
  });

  return isDark;
}

// Cycle between system, light, and dark themes
export function toggleTheme() {
  const currentTheme = getCurrentTheme();
  let newTheme;

  // Cycle through themes: system -> light -> dark -> system
  if (currentTheme === 'system') {
    newTheme = 'light';
  } else if (currentTheme === 'light') {
    newTheme = 'dark';
  } else {
    newTheme = 'system';
  }

  return applyTheme(newTheme);
}
