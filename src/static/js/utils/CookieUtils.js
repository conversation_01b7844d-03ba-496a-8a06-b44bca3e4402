/**
 * Cookie工具函数
 * 
 * 提供设置、获取、删除cookie的便捷方法
 */

/**
 * 设置cookie
 * @param {string} name - cookie名称
 * @param {string} value - cookie值
 * @param {Object} options - 配置选项
 * @param {number} options.days - 过期天数，默认30天
 * @param {string} options.path - 路径，默认'/'
 * @param {string} options.domain - 域名
 * @param {boolean} options.secure - 是否仅在HTTPS下传输
 * @param {string} options.sameSite - SameSite属性，默认'Lax'
 */
export function setCookie(name, value, options = {}) {
    const {
        days = 30,
        path = '/',
        domain,
        secure,
        sameSite = 'Lax'
    } = options;

    let cookieString = `${encodeURIComponent(name)}=${encodeURIComponent(value)}`;

    // 设置过期时间
    if (days) {
        const date = new Date();
        date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
        cookieString += `; expires=${date.toUTCString()}`;
    }

    // 设置路径
    if (path) {
        cookieString += `; path=${path}`;
    }

    // 设置域名
    if (domain) {
        cookieString += `; domain=${domain}`;
    }

    // 设置安全标志
    if (secure) {
        cookieString += `; secure`;
    }

    // 设置SameSite
    if (sameSite) {
        cookieString += `; samesite=${sameSite}`;
    }

    document.cookie = cookieString;
}

/**
 * 获取cookie值
 * @param {string} name - cookie名称
 * @returns {string|null} cookie值，如果不存在返回null
 */
export function getCookie(name) {
    const nameEQ = encodeURIComponent(name) + '=';
    const cookies = document.cookie.split(';');

    for (let cookie of cookies) {
        cookie = cookie.trim();
        if (cookie.indexOf(nameEQ) === 0) {
            return decodeURIComponent(cookie.substring(nameEQ.length));
        }
    }

    return null;
}

/**
 * 删除cookie
 * @param {string} name - cookie名称
 * @param {Object} options - 配置选项
 * @param {string} options.path - 路径，默认'/'
 * @param {string} options.domain - 域名
 */
export function deleteCookie(name, options = {}) {
    const { path = '/', domain } = options;
    
    let cookieString = `${encodeURIComponent(name)}=; expires=Thu, 01 Jan 1970 00:00:00 UTC`;

    if (path) {
        cookieString += `; path=${path}`;
    }

    if (domain) {
        cookieString += `; domain=${domain}`;
    }

    document.cookie = cookieString;
}

/**
 * 检查cookie是否存在
 * @param {string} name - cookie名称
 * @returns {boolean} 是否存在
 */
export function hasCookie(name) {
    return getCookie(name) !== null;
}

/**
 * 获取所有cookie
 * @returns {Object} 包含所有cookie的对象
 */
export function getAllCookies() {
    const cookies = {};
    const cookieArray = document.cookie.split(';');

    for (let cookie of cookieArray) {
        cookie = cookie.trim();
        if (cookie) {
            const [name, value] = cookie.split('=');
            if (name && value) {
                cookies[decodeURIComponent(name)] = decodeURIComponent(value);
            }
        }
    }

    return cookies;
}

/**
 * 清除所有cookie（仅限当前路径和域名）
 * @param {Object} options - 配置选项
 * @param {string} options.path - 路径，默认'/'
 * @param {string} options.domain - 域名
 */
export function clearAllCookies(options = {}) {
    const cookies = getAllCookies();
    
    for (let name in cookies) {
        deleteCookie(name, options);
    }
}
