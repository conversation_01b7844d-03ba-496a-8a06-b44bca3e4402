/**
 * Code highlighting utility
 *
 * Handles highlight.js initialization and theme switching
 * Provides safe highlighting for dynamically added code blocks
 */

// 初始化highlight.js
export function initializeHighlighter() {
    if (!window.hljs) {
        console.error('Highlight.js is not loaded');
        return;
    }

    // 代码复制功能
    window.copyCodeToClipboard = function(button, codeBlockId) {
        const codeBlock = document.getElementById(codeBlockId);
        if (!codeBlock) return;

        const code = codeBlock.querySelector('code').textContent;
        navigator.clipboard.writeText(code)
            .then(() => {
                // 更改文本以指示复制成功
                const originalText = button.textContent.trim();
                button.textContent = '已复制';

                // 2秒后恢复原始状态
                setTimeout(() => {
                    button.textContent = originalText;
                }, 2000);
            })
            .catch(err => {
                console.error('复制失败:', err);
            });
    };

    // 安全地应用高亮，避免重复高亮
    function safelyHighlightElement(element) {
        // 跳过处于流式打字区域下的代码块，减少频繁重绘
        if (element.closest && element.closest('[data-skip-hljs="true"]')) {
            return;
        }
        // 检查元素是否已经高亮
        if (element.hasAttribute('data-highlighted') &&
            element.getAttribute('data-highlighted') === 'yes') {
            return; // 已经高亮过，跳过
        }

        try {
            // 确保元素内容存在
            if (!element.textContent && !element.innerText) {
                // 如果没有文本内容，标记为已高亮并跳过
                element.setAttribute('data-highlighted', 'yes');
                return;
            }

            // 获取语言
            let language = '';
            if (element.className) {
                const match = element.className.match(/language-(\w+)/);
                if (match) {
                    language = match[1];
                }
            }

            // 应用高亮
            // 统一使用 highlightElement（无语言时会自动检测）
            window.hljs.highlightElement(element);

            // 标记为已高亮
            element.setAttribute('data-highlighted', 'yes');
        } catch (e) {
            console.error('高亮代码块时出错:', e);
            // 出错时也标记为已处理，避免重复尝试
            element.setAttribute('data-highlighted', 'yes');
        }
    }

    // 处理新添加的代码块
    function processNewCodeBlocks(node) {
        if (!node || node.nodeType !== 1) return; // 不是元素节点

        // 查找所有未高亮的代码块
        // 若该节点属于流式区域，直接跳过
        if (node.closest && node.closest('[data-skip-hljs="true"]')) {
            return;
        }
        const codeBlocks = node.querySelectorAll('pre code:not([data-highlighted="yes"])');
        codeBlocks.forEach(safelyHighlightElement);

        // 递归处理子节点
        if (node.childNodes && node.childNodes.length) {
            node.childNodes.forEach(processNewCodeBlocks);
        }
    }

    // 监听DOM变化
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.addedNodes && mutation.addedNodes.length > 0) {
                mutation.addedNodes.forEach(processNewCodeBlocks);
            }
        });
    });

    // 观察整个文档的变化
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });

    // 初始处理页面上已有的代码块
    processNewCodeBlocks(document.body);
}

// 更新代码高亮主题
export function updateCodeHighlightTheme(theme) {
    const lightTheme = document.getElementById('light-hljs-theme');
    const darkTheme = document.getElementById('dark-hljs-theme');

    if (!lightTheme || !darkTheme) {
        return;
    }

    if (theme === 'dark') {
        lightTheme.media = 'not all';
        darkTheme.media = 'all';
    } else {
        lightTheme.media = 'all';
        darkTheme.media = 'not all';
    }
}
