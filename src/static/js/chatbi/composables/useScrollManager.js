/**
 * Scroll Manager Composable
 *
 * 管理聊天界面的滚动行为，包括智能滚动到底部、检测用户滚动行为等
 * 遵循单一职责原则，专注于滚动行为管理
 */
import { ref, onMounted, onUnmounted } from 'vue';

// 创建响应式状态（在模块级别创建，确保单例）
const userHasScrolledUp = ref(false);
let resizeListener = null;
let scrollListeners = [];

/**
 * 使用滚动管理器
 * @returns {Object} 滚动管理相关的状态和方法
 */
export function useScrollManager() {
    // 获取当前活动的滚动容器
    const getActiveScrollContainer = () => {
        // 检查是否在桌面视图
        const isDesktop = window.innerWidth >= 1024; // lg断点是1024px

        // 如果是桌面视图，使用桌面滚动容器
        if (isDesktop) {
            // 更新选择器以匹配新的布局结构
            const container = document.querySelector('.desktop-content .chat-scroll-container');
            if (container) return container;

            // 兼容旧版选择器，以防万一
            const legacyContainer = document.querySelector('.lg\\:flex .chat-scroll-container');
            return legacyContainer;
        } else {
            // 更新选择器以匹配新的布局结构
            const container = document.querySelector('.custom-drawer-content .chat-scroll-container');
            if (container) return container;

            // 兼容旧版选择器，以防万一
            const legacyContainer = document.querySelector('.drawer-content .chat-scroll-container');
            return legacyContainer;
        }
    };

    // 检查用户是否在滚动容器底部
    const isUserAtBottom = () => {
        const container = getActiveScrollContainer();
        if (!container) return false;

        // 检查滚动位置是否接近底部（允许30px的误差，考虑到不同浏览器和设备的差异）
        const isAtBottom = container.scrollHeight - container.scrollTop - container.clientHeight < 30;
        return isAtBottom;
    };

    // 智能滚动到底部，考虑到内容可能在滚动后继续渲染
    const smartScrollToBottom = () => {
        const container = getActiveScrollContainer();
        if (!container) {
            console.warn('[ChatScroll] 无法执行智能滚动：未找到滚动容器');
            // 尝试重新设置滚动监听器，可能会找到容器
            setupScrollListeners();
            return;
        }

        // 记录当前滚动高度
        const initialScrollHeight = container.scrollHeight;

        // 立即滚动到底部
        container.scrollTo({
            top: container.scrollHeight,
            behavior: 'auto'
        });

        // 设置一个观察器，监视内容高度变化
        let lastKnownScrollHeight = container.scrollHeight;
        let scrollAttempts = 0;
        const maxScrollAttempts = 15; // 增加尝试次数，确保所有内容都能被滚动到

        // 使用 requestAnimationFrame 确保在下一帧再次尝试滚动
        // 这有助于处理布局变化和动画
        requestAnimationFrame(() => {
            // 检查容器是否仍然存在
            if (!container) return;

            // 立即再次滚动，处理可能的布局变化
            container.scrollTo({
                top: container.scrollHeight,
                behavior: 'auto'
            });
        });

        const checkAndScrollInterval = setInterval(() => {
            // 检查容器是否仍然存在
            if (!container) {
                clearInterval(checkAndScrollInterval);
                return;
            }

            // 如果高度变化了，再次滚动到底部
            if (container.scrollHeight > lastKnownScrollHeight) {
                container.scrollTo({
                    top: container.scrollHeight,
                    behavior: 'auto'
                });
                lastKnownScrollHeight = container.scrollHeight;
            }

            scrollAttempts++;
            if (scrollAttempts >= maxScrollAttempts) {
                clearInterval(checkAndScrollInterval);

                // 最后再次尝试滚动，确保所有内容都能被看到
                setTimeout(() => {
                    if (container) {
                        container.scrollTo({
                            top: container.scrollHeight,
                            behavior: 'auto'
                        });
                    }
                }, 50);
            }
        }, 100); // 每100ms检查一次

        // 最多运行1.5秒，增加时间以确保所有内容都能被滚动到
        setTimeout(() => {
            clearInterval(checkAndScrollInterval);
            // 最后再次尝试滚动
            if (container) {
                container.scrollTo({
                    top: container.scrollHeight,
                    behavior: 'auto'
                });
            }
        }, 1500);

        return initialScrollHeight;
    };

    // 简单滚动到底部（用于流式消息更新，性能更好）
    const simpleScrollToBottom = () => {
        // 使用requestAnimationFrame来优化性能
        requestAnimationFrame(() => {
            const scrollContainer = getActiveScrollContainer();
            if (scrollContainer) {
                scrollContainer.scrollTo({
                    top: scrollContainer.scrollHeight,
                    behavior: 'auto' // 使用auto而不是smooth，避免滚动动画堆叠
                });
            } else {
                // 尝试重新设置滚动监听器，可能会找到容器
                setupScrollListeners();
            }
        });
    };

    // 添加滚动事件监听器，检测用户是否向上滚动
    const setupScrollListeners = () => {
        // 清除之前的监听器
        cleanupScrollListeners();

        // 创建一个重试机制，确保能找到滚动容器
        const maxRetries = 5;
        let retryCount = 0;

        const attemptSetup = () => {
            const container = getActiveScrollContainer();

            if (!container) {
                // 如果找不到容器，且未超过最大重试次数，则重试
                if (retryCount < maxRetries) {
                    retryCount++;
                    setTimeout(attemptSetup, 300);
                }
                return;
            }

            // 找到容器后设置滚动处理函数
            const scrollHandler = () => {
                // 如果用户不在底部，标记为已向上滚动
                if (!isUserAtBottom()) {
                    userHasScrolledUp.value = true;
                } else {
                    // 如果用户滚动到底部，重置标记
                    userHasScrolledUp.value = false;
                }
            };

            // 添加滚动事件监听器
            container.addEventListener('scroll', scrollHandler);
            scrollListeners.push({ element: container, handler: scrollHandler, type: 'scroll' });

            // 初始化时检查一次滚动位置
            scrollHandler();
        };

        // 开始尝试设置
        attemptSetup();
    };

    // 清理滚动监听器
    const cleanupScrollListeners = () => {
        scrollListeners.forEach(({ element, handler, type }) => {
            if (element) {
                element.removeEventListener(type, handler);
            }
        });
        scrollListeners = [];
    };

    // 组件挂载时设置监听器
    onMounted(() => {
        // 设置滚动监听器，使用短延迟确保DOM已完全加载
        setTimeout(() => {
            setupScrollListeners();
        }, 100);

        // 监听窗口大小变化，重新设置滚动监听器
        resizeListener = () => {
            // 使用短延迟确保DOM已更新
            setTimeout(() => {
                setupScrollListeners();
            }, 100);
        };
        window.addEventListener('resize', resizeListener);
    });

    // 组件卸载时清理监听器
    onUnmounted(() => {
        cleanupScrollListeners();
        if (resizeListener) {
            window.removeEventListener('resize', resizeListener);
            resizeListener = null;
        }
    });

    return {
        // 状态
        userHasScrolledUp,

        // 方法
        isUserAtBottom,
        smartScrollToBottom,
        simpleScrollToBottom,
        setupScrollListeners,
        cleanupScrollListeners,
        getActiveScrollContainer
    };
}
