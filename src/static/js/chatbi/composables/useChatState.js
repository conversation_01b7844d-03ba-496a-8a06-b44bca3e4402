/**
 * Chat State Composable
 *
 * Manages the state and logic for the chat functionality
 * 使用单例模式确保状态在组件间共享
 */
import { ref, computed, nextTick } from 'vue';
import { useHistoryState } from './useHistoryState.js';
import { useLogState } from './useLogState.js';
import { useMessageFormatter } from './useMessageFormatter.js?v=1753410700';
import { usePollingManager } from './usePollingManager.js';
import { renderMarkdown } from '../../utils/MarkdownRenderer.js';

// 在模块级别创建状态，确保单例
// 聊天状态
const messages = ref([]);
const isLoadingMessages = ref(false);
const messageError = ref(null);
const streamingMessageId = ref(null); // 当前正在流式传输的消息ID
const streamingMessageRef = ref(null); // 当前流式消息对象引用

// 预设的conversation ID（用于新对话）
const presetConversationId = ref(null);

// 获取历史状态
const historyState = useHistoryState();

// 获取日志状态
const logState = useLogState();

// 获取消息格式化器
const messageFormatter = useMessageFormatter();

// 获取轮询管理器
const pollingManager = usePollingManager();

// 从历史状态获取活动会话ID，如果没有则使用预设ID
const activeConversationId = computed(() =>
    historyState.activeConversationId.value || presetConversationId.value
);

// 加载会话消息
const loadConversationMessages = async (conversationId) => {
    console.log('=== loadConversationMessages 被调用 ===', conversationId);
    if (!conversationId) return;

    try {
        // 设置加载状态
        isLoadingMessages.value = true;
        messageError.value = null;

        // 清空当前消息，以便在加载新消息时不显示旧消息
        messages.value = [];

        // 清空当前日志
        logState.clearLogs();

        // 直接从历史状态获取会话消息，无需额外API调用
        const conversationMessages = historyState.getConversationMessages(conversationId);

        if (conversationMessages.length === 0) {
            // 如果没有找到消息，可能需要加载更多历史记录
            await historyState.loadHistory(1);
            // 再次尝试获取消息
            const retryMessages = historyState.getConversationMessages(conversationId);

            if (retryMessages.length > 0) {
                // 格式化消息并设置
                const formattedMessages = retryMessages.map(messageFormatter.formatMessage);
                messages.value = formattedMessages;

                // 处理历史消息中的日志
                logState.processHistoryLogs(formattedMessages);
            } else {
                messageError.value = '未找到会话消息';
            }
        } else {
            // 格式化消息并设置
            const formattedMessages = conversationMessages.map(messageFormatter.formatMessage);
            messages.value = formattedMessages;

            // 处理历史消息中的日志
            logState.processHistoryLogs(formattedMessages);
        }

        // 在下一个tick中检查是否有需要恢复的流式消息
        nextTick(() => {
            recoverStreamingMessages();
        });
    } catch (err) {
        messageError.value = err.message || '获取会话消息失败';
        console.error('[ChatState] 加载会话消息失败:', err);
    } finally {
        // 无论成功还是失败，都要重置加载状态
        isLoadingMessages.value = false;
    }
};

// 选择会话
const selectConversation = (conversationId) => {
    console.log('=== useChatState.selectConversation 被调用 ===', conversationId);
    // 设置新的活动对话ID
    historyState.selectConversation(conversationId);

    // 始终加载消息
    messages.value = [];
    loadConversationMessages(conversationId);
};

// 生成新的conversation ID
const generateConversationId = () => {
    return crypto.randomUUID ? crypto.randomUUID() :
           'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
               const r = Math.random() * 16 | 0;
               const v = c == 'x' ? r : (r & 0x3 | 0x8);
               return v.toString(16);
           });
};

// 新建对话
const newConversation = () => {
    // 生成新的conversation ID，但不立即设置为活动对话
    const newConversationId = generateConversationId();
    console.log('=== 新建对话，生成conversation ID ===', newConversationId);

    // 预设conversation ID，但不设置为活动对话（避免触发不必要的API请求）
    presetConversationId.value = newConversationId;

    // 清除当前会话状态（不设置活动对话ID）
    historyState.newConversation();
    messages.value = [];
    logState.clearLogs();
};

// 注意：删除会话的功能已移至 historyState.openDeleteConfirmModal 和 historyState.confirmDeleteConversation

// 添加用户消息
const addUserMessage = (userMessage) => {
    const newMessage = messageFormatter.createUserMessage(userMessage);
    messages.value.push(newMessage);

    // 将用户消息添加到 rawHistoryData 中，确保切换对话后再切回来能看到新消息
    if (activeConversationId.value) {
        // 创建一个新的用户消息对象，用于存储在 rawHistoryData 中
        const userMessageForHistory = {
            id: newMessage.id,
            role: 'user',
            content: userMessage.content,
            timestamp: userMessage.timestamp,
            // 保存图片信息以支持AI回复后的图片显示
            images: userMessage.images || [],
            resource_url: userMessage.images && userMessage.images.length > 0 ? userMessage.images.join(',') : null
        };

        // 确保当前会话在 rawHistoryData 中存在
        if (!historyState.rawHistoryData.value[activeConversationId.value]) {
            historyState.rawHistoryData.value[activeConversationId.value] = [];
        }

        // 将消息添加到历史记录中
        historyState.rawHistoryData.value[activeConversationId.value].push(userMessageForHistory);
    }

    return newMessage.id;
};

// 添加AI消息（初始空消息，用于流式更新）
const addAiMessage = () => {
    const newMessage = messageFormatter.createAiMessage();
    messages.value.push(newMessage);
    streamingMessageId.value = newMessage.id;
    streamingMessageRef.value = newMessage;
    return newMessage.id;
};

// 更新流式消息内容 - 使用requestAnimationFrame实现流畅更新
const updateStreamingMessage = (() => {
    let isUpdatePending = false;
    let pendingData = null;

    return (data) => {
        if (!streamingMessageId.value || !streamingMessageRef.value || streamingMessageRef.value.id !== streamingMessageId.value) return;

        // 保存最新的数据
        pendingData = data;

        // 如果没有等待的更新，安排一个
        if (!isUpdatePending) {
            isUpdatePending = true;

            // 使用requestAnimationFrame与浏览器的渲染周期同步
            requestAnimationFrame(() => {
                // 确保消息引用仍然有效
                if (!streamingMessageId.value || !streamingMessageRef.value || streamingMessageRef.value.id !== streamingMessageId.value) {
                    isUpdatePending = false;
                    pendingData = null;
                    return;
                }

                const msg = streamingMessageRef.value;
                const currentData = pendingData;

                // 处理数据
                if (typeof currentData === 'object') {
                    // 如果有fullMessage属性
                    if (currentData.fullMessage !== undefined) {
                        msg.content = currentData.fullMessage;
                    }

                    // 如果有state属性
                    if (currentData.state) {
                        if (currentData.state.rawContent !== undefined) {
                            msg.content = currentData.state.rawContent;
                        }
                        if (currentData.state.renderedContent) {
                            msg.renderedContent = currentData.state.renderedContent;
                        }

                        // 如果收到了真实的chat_history_id，设置到消息的chatHistoryId属性
                        if (currentData.state.chatHistoryId) {
                            const chatHistoryId = parseInt(currentData.state.chatHistoryId);
                            console.log('=== 设置AI消息的chatHistoryId (from state) ===', {
                                messageId: msg.id,
                                chatHistoryId: chatHistoryId
                            });
                            // 设置chatHistoryId属性，用于好坏案例标记
                            msg.chatHistoryId = chatHistoryId;
                        }

                        // 使用日志状态组合式API处理日志
                        streamingMessageRef.value = logState.updateStreamingLogs(currentData, msg);
                    }

                    // 检查是否直接在currentData上有chatHistoryId（来自queryService）
                    if (currentData.chatHistoryId) {
                        const chatHistoryId = parseInt(currentData.chatHistoryId);
                        console.log('=== 设置AI消息的chatHistoryId (from currentData) ===', {
                            messageId: msg.id,
                            chatHistoryId: chatHistoryId
                        });
                        // 设置chatHistoryId属性，用于好坏案例标记
                        msg.chatHistoryId = chatHistoryId;
                    }

                    // 如果有预渲染的内容
                    if (currentData.renderedContent) {
                        msg.renderedContent = currentData.renderedContent;
                    }
                }
                // 兼容字符串形式
                else if (typeof currentData === 'string') {
                    msg.content = currentData;
                }

                // 重置状态
                isUpdatePending = false;
                pendingData = null;
            });
        }
    };
})();

// 完成流式消息
const completeStreamingMessage = (messageData) => {
    if (!streamingMessageId.value || !streamingMessageRef.value || streamingMessageRef.value.id !== streamingMessageId.value) return;

    const msg = streamingMessageRef.value;
    // 重新设置消息内容，确保内容是最终的完整内容
    msg.content = messageData.content;
    // 如果有预渲染的内容，也更新
    if (messageData.renderedContent) {
        msg.renderedContent = messageData.renderedContent;
    }
    msg.isStreaming = false;

    // 使用当前客户端时间作为完成时间（因为这是真正的完成时刻）
    msg.timestamp = new Date().toLocaleTimeString();

    // 如果消息被中断，添加中断标记
    if (messageData.isInterrupted) {
        msg.isInterrupted = true;
    }

    // 如果返回了新的会话ID，更新当前活动会话
    if (messageData.conversationId && !activeConversationId.value) {
        // 找到用户消息
        const userMessage = messages.value.find(msg => msg.role === 'user');
        if (userMessage) {
            // 直接将新会话添加到历史记录中，传递完整的用户消息对象以保留图片信息
            historyState.addNewConversation(messageData.conversationId, {
                content: userMessage.content,
                images: userMessage.images || []
            });
        } else {
            // 如果找不到用户消息，则使用默认标题
            historyState.addNewConversation(messageData.conversationId, '新对话');
        }

        // 清除预设的conversation ID，因为现在有了真实的ID
        presetConversationId.value = null;
    }

    // 将完成的消息添加到 rawHistoryData 中，确保切换对话后再切回来能看到新消息
    if (activeConversationId.value) {
        // 创建一个新的助手消息对象，用于存储在 rawHistoryData 中
        const assistantMessage = messageFormatter.createHistoryAiMessage(
            msg,
            messageData
        );

        // 确保当前会话在 rawHistoryData 中存在
        if (!historyState.rawHistoryData.value[activeConversationId.value]) {
            historyState.rawHistoryData.value[activeConversationId.value] = [];
        }

        // 将消息添加到历史记录中
        historyState.rawHistoryData.value[activeConversationId.value].push(assistantMessage);
    }
    // 清除流式消息ID和引用
    streamingMessageId.value = null;
    streamingMessageRef.value = null;
};

// 处理消息错误
const handleMessageError = (errorData) => {
    // 如果有正在流式传输的消息，标记为错误
    if (streamingMessageId.value && streamingMessageRef.value && streamingMessageRef.value.id === streamingMessageId.value) {
        const msg = streamingMessageRef.value;
        // 处理错误对象或字符串
        const errorMessage = typeof errorData === 'object' ? errorData.message : errorData;

        // 检查是否是中断错误
        const isInterruptError = errorMessage === '查询已被用户中断';

        // 如果是中断错误，不清除内容，而是添加中断标记
        if (isInterruptError) {
            // 如果消息内容为空，添加中断提示
            if (!msg.content) {
                msg.content = '*回复已被中断*';
            } else {
                // 如果已有内容，添加中断提示
                msg.content += '\n\n*回复已被中断*';
            }
            msg.isStreaming = false;
            msg.isInterrupted = true;

            // 重新渲染内容以确保中断消息正确显示
            if (msg.content) {
                msg.renderedContent = renderMarkdown(msg.content);
            }

            // 如果有错误状态对象，保存错误日志
            if (typeof errorData === 'object' && errorData.state && errorData.state.logsHTML) {
                msg.errorLogs = errorData.state.logsHTML;
            }
        } else {
            // 如果是其他错误，正常处理
            // 如果消息内容为空，添加错误信息
            if (!msg.content) {
                msg.content = `错误: ${errorMessage}`;
            } else {
                // 如果已有内容，在末尾添加错误信息
                msg.content += `\n\n**错误**: ${errorMessage}`;
            }
            msg.isStreaming = false;
            msg.isError = true;

            // 重新渲染内容以确保错误消息正确显示
            if (msg.content) {
                msg.renderedContent = renderMarkdown(msg.content);
            }

            // 如果有错误状态对象，保存错误日志
            if (typeof errorData === 'object' && errorData.state && errorData.state.logsHTML) {
                msg.errorLogs = errorData.state.logsHTML;
            }
        }

        // 清除流式消息ID和引用
        streamingMessageId.value = null;
        streamingMessageRef.value = null;
    } else {
        // 如果没有正在流式传输的消息，设置全局错误
        const errorMessage = typeof errorData === 'object' ? errorData.message : errorData;
        messageError.value = errorMessage;
    }
};

// 恢复流式消息状态
const recoverStreamingMessages = () => {
    console.log('[ChatState] 检查是否有需要恢复的流式消息');

    // 遍历当前消息，查找正在处理中的AI消息
    const streamingMessages = messages.value.filter(msg =>
        msg.role === 'assistant' &&
        msg.chatHistoryId &&
        msg.isInProcess === true
    );

    if (streamingMessages.length === 0) {
        console.log('[ChatState] 没有发现需要恢复的流式消息');
        return;
    }

    console.log(`[ChatState] 发现 ${streamingMessages.length} 个需要恢复的流式消息`);

    // 为每个未完成的消息启动轮询
    streamingMessages.forEach(msg => {
        console.log(`[ChatState] 开始恢复消息 ${msg.chatHistoryId} 的轮询`);

        pollingManager.startPolling(msg.chatHistoryId, {
            onUpdate: (data) => {
                console.log(`[ChatState] 轮询更新消息 ${msg.chatHistoryId}:`, data);

                // 更新消息内容
                msg.content = data.content;
                msg.isStreaming = true;

                // 如果有日志，更新日志
                if (data.logs) {
                    logState.updateStreamingLogs({ state: { logsHTML: data.logs } }, msg);
                }

                // 重新渲染内容
                msg.renderedContent = renderMarkdown(data.content);
            },

            onComplete: (data) => {
                console.log(`[ChatState] 轮询完成消息 ${msg.chatHistoryId}:`, data);

                // 更新最终内容
                msg.content = data.content;
                msg.isStreaming = false;
                msg.isInProcess = false;

                // 设置完成时间
                msg.timestamp = new Date().toLocaleTimeString();

                // 如果有日志，更新日志
                if (data.logs) {
                    logState.updateStreamingLogs({ state: { logsHTML: data.logs } }, msg);
                }

                // 重新渲染内容
                msg.renderedContent = renderMarkdown(data.content);

                console.log(`[ChatState] 消息 ${msg.chatHistoryId} 恢复完成`);
            },

            onError: (error) => {
                console.error(`[ChatState] 轮询消息 ${msg.chatHistoryId} 失败:`, error);

                // 标记消息为错误状态
                msg.isStreaming = false;
                msg.isError = true;
                msg.content += '\n\n⚠️ 消息恢复失败，请刷新页面重试';
                msg.renderedContent = renderMarkdown(msg.content);
            }
        });
    });
};

// 分享会话状态
const isShareModalOpen = ref(false);
const shareUrl = ref('');
const isGeneratingShareLink = ref(false);

// 分享会话
const shareConversation = async (conversationId) => {
    if (!conversationId) {
        console.error('无法分享：会话ID为空');
        return;
    }

    try {
        // 打开模态框并显示加载状态
        isGeneratingShareLink.value = true;
        isShareModalOpen.value = true;
        shareUrl.value = '';

        // 调用API生成分享链接
        const response = await fetch('/api/share_conversation', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ conversation_id: conversationId }),
        });

        if (!response.ok) {
            throw new Error(`分享失败: ${response.status}`);
        }

        const data = await response.json();

        // 设置分享URL
        if (data.share_url) {
            shareUrl.value = data.share_url;
        } else {
            throw new Error('服务器未返回有效的分享链接');
        }
    } catch (error) {
        console.error('分享会话失败:', error);
        // 可以在这里添加错误处理，例如显示错误消息
        alert(`分享失败: ${error.message}`);
        // 关闭模态框
        isShareModalOpen.value = false;
    } finally {
        // 无论成功或失败，都结束加载状态
        isGeneratingShareLink.value = false;
    }
};

// 关闭分享模态框
const closeShareModal = () => {
    isShareModalOpen.value = false;
};

// 导出 useChatState 函数
export function useChatState() {
    // 初始化时，如果有活动对话ID，尝试加载消息
    if (activeConversationId.value) {
        loadConversationMessages(activeConversationId.value);
    }

    return {
        // 状态
        messages,
        isLoadingMessages,
        messageError,
        activeConversationId,
        streamingMessageId,
        historyState,
        currentLogs: logState.currentLogs, // 使用日志状态的日志

        // 分享相关状态
        isShareModalOpen,
        shareUrl,
        isGeneratingShareLink,

        // 方法
        loadConversationMessages,
        selectConversation,
        newConversation,
        shareConversation,
        closeShareModal,
        clearLogs: logState.clearLogs, // 使用日志状态的清空方法

        // 流式消息处理方法
        addUserMessage,
        addAiMessage,
        updateStreamingMessage,
        completeStreamingMessage,
        handleMessageError,

        // 轮询和状态恢复方法
        recoverStreamingMessages,
        pollingManager
    };
}
