/**
 * Layout State Composable
 *
 * Manages the state and logic for the layouts (sidebar, responsive behavior, etc.)
 * 使用单例模式确保状态在组件间共享
 */
import { ref, computed, onMounted } from 'vue';
import { initTheme } from '../../utils/ThemeManager.js';

// 创建响应式状态（在模块级别创建，确保单例）
const sidebarOpen = ref(true);
const isDarkTheme = ref(false);
const windowWidth = ref(window.innerWidth);

// 计算属性：是否为桌面视图（宽度 >= 1024px）
const isDesktopView = computed(() => {
    return windowWidth.value >= 1024;
});

// 切换侧边栏
const toggleSidebar = () => {
    sidebarOpen.value = !sidebarOpen.value;
};

// 切换主题
const toggleTheme = (isDark) => {
    isDarkTheme.value = isDark;
};

// 初始化标志，确保只初始化一次
let initialized = false;

// 初始化函数
const initialize = () => {
    if (initialized) return;

    // 初始化主题
    isDarkTheme.value = initTheme();

    // 初始设置：桌面视图默认打开侧边栏，移动视图默认关闭
    const mediaQuery = window.matchMedia('(min-width: 1024px)');
    sidebarOpen.value = mediaQuery.matches;

    // 使用 MediaQueryList 监听视图变化
    const handleMediaChange = (e) => {
        // 桌面视图：打开侧边栏，移动视图：关闭侧边栏
        // 只在视图切换时自动调整，不影响用户手动操作
        if (e.matches) {
            // 切换到桌面视图时打开侧边栏
            sidebarOpen.value = true;
        }
        // 切换到移动视图时不自动关闭侧边栏，让用户手动控制
    };

    // 添加媒体查询监听
    mediaQuery.addEventListener('change', handleMediaChange);

    // 监听窗口大小变化，更新窗口宽度
    window.addEventListener('resize', () => {
        windowWidth.value = window.innerWidth;
    });

    initialized = true;
};

export function useLayoutState() {
    // 在组件挂载时初始化
    onMounted(() => {
        initialize();
    });

    return {
        sidebarOpen,
        isDarkTheme,
        windowWidth,
        isDesktopView,
        toggleSidebar,
        toggleTheme
    };
}
