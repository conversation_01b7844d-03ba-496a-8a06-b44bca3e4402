/**
 * Share Header Component
 *
 * A simplified header for shared conversation view
 * Styled with Apple/OpenAI-inspired aesthetics
 */
import BaseHeader from '../../common/components/BaseHeader.js';
import { TerminalIcon } from '../../utils/Icons.js';
import { computed } from 'vue';

export default {
    name: 'ShareHeader',
    components: {
        BaseHeader
    },
    props: {
        userInfo: {
            type: Object,
            required: true
        },
        isDarkTheme: {
            type: Boolean,
            default: false
        },
        ownerName: {
            type: String,
            required: true
        },
        isDevLogVisible: {
            type: Boolean,
            default: false
        }
    },
    emits: ['toggleTheme', 'toggleDevLog', 'viewShareLink'],
    setup(props, { emit }) {
        // 计算属性：是否为开发人员
        const isDeveloper = computed(() => {
            return props.userInfo && props.userInfo.isAdmin;
        });

        // 切换开发日志显示
        const toggleDevLog = () => {
            emit('toggleDevLog');
        };

        return {
            toggleDevLog,
            isDeveloper,
            // Icons
            TerminalIcon
        };
    },
    template: `
        <div class="relative">
            <BaseHeader
                :user-info="userInfo"
                :is-dark-theme="isDarkTheme"
                :title="'ChatBI'"
                :home-url="'/'"
                :is-dashboard="false"
                :sidebar-open="false"
                :is-desktop-view="true"
                @toggle-theme="$emit('toggleTheme', $event)"
            >
                <template #start>
                    <!-- 左侧不需要任何内容 -->
                </template>

                <template #actions>
                    <!-- 分享信息显示在header右侧，日志按钮的左边 -->
                    <div class="text-xs sm:text-sm text-base-content/70 mr-2">
                        查看由 <span class="font-medium text-base-content" style="margin-right: 5px;">{{ ownerName }}</span>
                        <button
                            class="text-primary hover:text-primary/80 transition-colors duration-200 font-medium inline-block"
                            @click="$emit('viewShareLink')"
                        >分享</button> 的对话
                    </div>

                    <!-- 开发日志按钮 - 仅对开发人员显示 -->
                    <button
                        v-if="isDeveloper"
                        class="btn btn-ghost btn-sm gap-1"
                        @click="toggleDevLog"
                        title="查看日志"
                    >
                        <span v-html="TerminalIcon" class="w-4 h-4"></span>
                        <span>{{ isDevLogVisible ? '隐藏' : '日志' }}</span>
                    </button>
                </template>
            </BaseHeader>
        </div>
    `
};
