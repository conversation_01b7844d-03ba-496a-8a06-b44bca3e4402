/**
 * Delete Confirmation Modal Component
 *
 * A reusable modal for confirming deletion actions
 * Styled with Apple/OpenAI-inspired aesthetics
 */
export default {
    name: 'DeleteConfirmModal',
    props: {
        isOpen: {
            type: Boolean,
            required: true
        },
        title: {
            type: String,
            default: '此项目'
        },
        confirmButtonText: {
            type: String,
            default: '删除'
        },
        cancelButtonText: {
            type: String,
            default: '取消'
        }
    },
    emits: ['confirm', 'cancel'],
    setup(_, { emit }) {
        const handleConfirm = () => {
            emit('confirm');
        };

        const handleCancel = () => {
            emit('cancel');
        };

        return {
            handleConfirm,
            handleCancel
        };
    },
    template: `
        <dialog :open="isOpen" class="modal modal-middle">
            <div class="modal-box rounded-lg shadow-md">
                <h3 class="font-semibold text-lg">确认删除</h3>
                <p class="py-4 opacity-90">您确定要删除"{{ title }}"吗？此操作无法撤销。</p>
                <div class="modal-action">
                    <button class="btn-cancel" @click="handleCancel">
                        {{ cancelButtonText }}
                    </button>
                    <button class="btn-delete" @click="handleConfirm">
                        {{ confirmButtonText }}
                    </button>
                </div>
            </div>

            <!-- 修复暗色模式下背景穿透问题 -->
            <form method="dialog" class="modal-backdrop bg-black/40" @click="handleCancel">
                <button class="sr-only">关闭</button>
            </form>
        </dialog>
    `
};
