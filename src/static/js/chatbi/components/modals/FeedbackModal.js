/**
 * Feedback Modal Component
 *
 * 反馈收集模态框组件
 * 用于收集用户对AI回复的详细反馈
 */
import { ref, computed, watch } from 'vue';

export default {
    name: 'FeedbackModal',
    props: {
        isOpen: {
            type: Boolean,
            required: true
        },
        feedbackType: {
            type: String, // 'positive' | 'negative'
            default: null
        },
        isSubmitting: {
            type: Boolean,
            default: false
        }
    },
    emits: ['close', 'submit'],
    setup(props, { emit }) {
        // 响应式状态
        const selectedTags = ref([]);
        const customFeedback = ref('');
        
        // 预定义反馈标签
        const POSITIVE_TAGS = ['准确', '清晰', '有用', '完整'];
        const NEGATIVE_TAGS = ['不准确', '不清晰', '不完整', '偏题'];
        
        // 计算当前类型的标签
        const currentTags = computed(() => {
            return props.feedbackType === 'positive' ? POSITIVE_TAGS : NEGATIVE_TAGS;
        });
        
        // 计算模态框标题
        const modalTitle = computed(() => {
            return props.feedbackType === 'positive' ? '给出好评' : '给出差评';
        });
        
        // 重置状态
        const resetState = () => {
            selectedTags.value = [];
            customFeedback.value = '';
        };
        
        // 监听模态框打开状态，重置数据
        watch(() => props.isOpen, (newValue) => {
            if (newValue) {
                resetState();
            }
        });
        
        // 处理关闭
        const handleClose = () => {
            emit('close');
        };
        
        // 处理背景点击关闭
        const handleBackdropClick = (event) => {
            if (event.target === event.currentTarget) {
                handleClose();
            }
        };
        
        // 切换标签选择
        const toggleTag = (tag) => {
            const index = selectedTags.value.indexOf(tag);
            if (index > -1) {
                selectedTags.value.splice(index, 1);
            } else {
                selectedTags.value.push(tag);
            }
        };
        
        // 更新自定义反馈
        const updateCustomFeedback = (event) => {
            customFeedback.value = event.target.value;
        };
        
        // 提交反馈
        const handleSubmit = () => {
            const feedbackData = {
                selectedTags: selectedTags.value,
                customFeedback: customFeedback.value.trim()
            };
            
            emit('submit', feedbackData);
        };
        
        // 跳过反馈
        const handleSkip = () => {
            handleClose();
        };
        
        // 检查是否有反馈内容
        const hasFeedbackContent = computed(() => {
            return selectedTags.value.length > 0 || customFeedback.value.trim().length > 0;
        });
        
        return {
            selectedTags,
            customFeedback,
            currentTags,
            modalTitle,
            handleClose,
            handleBackdropClick,
            toggleTag,
            updateCustomFeedback,
            handleSubmit,
            handleSkip,
            hasFeedbackContent
        };
    },
    template: `
        <div v-if="isOpen" class="feedback-modal" @click="handleBackdropClick">
            <div class="feedback-modal-content">
                <h3 class="text-base font-semibold mb-3 text-gray-900 dark:text-gray-100">
                    {{ modalTitle }}
                </h3>

                <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
                    请选择原因或添加详细说明
                </p>

                <!-- 预定义标签 -->
                <div class="mb-4">
                    <div class="flex flex-wrap">
                        <button
                            v-for="tag in currentTags"
                            :key="tag"
                            class="feedback-tag"
                            :class="{ 'selected': selectedTags.includes(tag) }"
                            @click="toggleTag(tag)"
                        >
                            {{ tag }}
                        </button>
                    </div>
                </div>

                <!-- 自定义反馈文本 -->
                <div class="mb-5">
                    <textarea
                        :value="customFeedback"
                        @input="updateCustomFeedback"
                        class="w-full p-3 border border-gray-200 dark:border-gray-600 rounded-lg resize-none focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                        rows="3"
                        maxlength="200"
                        placeholder="补充说明 (可选)"
                    ></textarea>
                    <div class="text-xs text-gray-500 dark:text-gray-400 mt-1 text-right">
                        {{ customFeedback.length }}/200
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="flex justify-end">
                    <button
                        class="btn-cancel"
                        @click="handleSkip"
                        :disabled="isSubmitting"
                    >
                        跳过
                    </button>
                    <button
                        class="btn-primary"
                        @click="handleSubmit"
                        :disabled="isSubmitting"
                    >
                        <span v-if="isSubmitting" class="inline-block w-3 h-3 border border-white border-t-transparent rounded-full animate-spin mr-2"></span>
                        {{ isSubmitting ? '提交中' : '提交' }}
                    </button>
                </div>
            </div>
        </div>
    `
};
