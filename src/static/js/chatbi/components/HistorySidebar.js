import { onMounted } from 'vue';
import { PencilIcon, ChevronLeftIcon, MoreHorizontalIcon, ShareIcon, TrashIcon } from '../../utils/Icons.js';
import { zDropdownContent } from '../../utils/zIndex.js';
import { useHistoryState } from '../composables/useHistoryState.js';
import BaseSidebar from '../../common/components/BaseSidebar.js';

export default {
    name: 'HistorySidebar',
    components: {
        BaseSidebar
    },
    props: {
        isOpen: {
            type: Boolean,
            default: true
        },
        userInfo: {
            type: Object,
            required: true
        }
    },
    emits: ['close-sidebar', 'new-conversation', 'toggle-sidebar', 'share-conversation', 'delete-conversation', 'select-conversation'],
    setup(props, { emit }) {
        // Use the history state composable
        const {
            isLoading,
            error,
            conversationGroups,
            activeConversationId,
            hasMorePages,
            deleteConfirmModal,
            loadHistory,
            loadMoreHistory,
            selectConversation,
            openDeleteConfirmModal,
            closeDeleteConfirmModal,
            confirmDeleteConversation,
            newConversation
        } = useHistoryState();

        // Wrapper functions to emit events to parent
        const handleSelectConversation = (conversationId) => {
            // 选择对话
            selectConversation(conversationId);

            // 触发 select-conversation 事件
            emit('select-conversation', conversationId);
        };

        const handleNewConversation = () => {
            newConversation();
            emit('new-conversation');
        };

        // UI-specific functions
        const closeSidebar = () => {
            emit('close-sidebar');
        };

        const toggleSidebar = () => {
            emit('toggle-sidebar');
        };

        const shareConversation = (id) => {
            emit('share-conversation', id);
        };

        // 组件挂载时加载历史记录
        onMounted(() => {
            loadHistory();
        });

        return {
            // State
            conversationGroups,
            isLoading,
            error,
            deleteConfirmModal,
            hasMorePages,

            // Methods
            newConversation: handleNewConversation,
            closeSidebar,
            toggleSidebar,
            shareConversation,
            deleteConversation: openDeleteConfirmModal,
            selectConversation: handleSelectConversation,
            loadMoreHistory,
            confirmDeleteConversation,
            closeDeleteConfirmModal,

            // Icons
            PencilIcon,
            ChevronLeftIcon,
            MoreHorizontalIcon,
            ShareIcon,
            TrashIcon,
            zDropdownContent
        };
    },
    template: `
        <BaseSidebar
            :is-open="isOpen"
            :user-info="userInfo"
            @close-sidebar="closeSidebar"
            @toggle-sidebar="toggleSidebar"
            @scroll="(e) => {
                // 检测滚动到底部，加载更多历史记录
                const el = e.target;
                if (el.scrollTop + el.clientHeight >= el.scrollHeight - 100 && !isLoading && hasMorePages) {
                    loadMoreHistory();
                }
            }"
        >
            <template #header-actions>
                <!-- 新建对话按钮 -->
                <button class="btn btn-ghost" @click="newConversation" title="新建对话">
                    <span v-html="PencilIcon"></span>
                    <span class="ml-2 hidden md:inline-block">新建对话</span>
                </button>
            </template>

            <!-- 历史记录内容 -->
            <div>
                <div class="p-2">
                    <!-- 加载中状态 -->
                    <div v-if="isLoading && conversationGroups.length === 0" class="text-center py-8">
                        <span class="loading loading-spinner loading-md"></span>
                    </div>

                    <!-- 错误状态 -->
                    <div v-else-if="error && conversationGroups.length === 0" class="text-center py-8 text-error">
                        <div>{{ error }}</div>
                        <button class="btn btn-sm btn-outline mt-2" @click="loadHistory()">重试</button>
                    </div>

                    <!-- 空状态 -->
                    <div v-else-if="conversationGroups.length === 0" class="text-center py-8 text-base-content/50">
                        暂无历史记录
                    </div>

                    <!-- 按时间分组显示对话 -->
                    <div v-else class="space-y-4">
                        <div v-for="group in conversationGroups" :key="group.id" class="conversation-group">
                            <!-- 分组标题 -->
                            <div class="text-xs font-medium text-base-content/60 px-3 py-2">
                                {{ group.title }}
                            </div>

                            <!-- 分组内的对话列表 -->
                            <ul class="menu menu-sm p-0">
                                <li v-for="conversation in group.conversations" :key="conversation.id" class="relative conversation-item">
                                    <div
                                        class="grid grid-cols-[1fr,auto] items-center mb-1 rounded-md transition-colors duration-200 py-2 px-3 cursor-pointer"
                                        :class="{ 'menu-active': conversation.active }"
                                        @click="selectConversation(conversation.id)"
                                    >
                                        <!-- 标题 - 自动占据剩余空间并在必要时截断 -->
                                        <div class="truncate font-medium" :title="conversation.title">{{ conversation.title }}</div>

                                        <!-- 三点菜单 - 仅在悬停时显示 -->
                                        <div class="opacity-0 menu-actions">
                                            <div class="dropdown dropdown-end">
                                                <div tabindex="0" role="button" class="btn btn-ghost btn-xs btn-circle menu-button" @click.stop>
                                                    <span v-html="MoreHorizontalIcon"></span>
                                                </div>
                                                <ul tabindex="0" class="dropdown-content menu p-2 shadow-lg bg-base-100 rounded-box min-w-max" :style="{ zIndex: zDropdownContent }">
                                                    <li>
                                                        <a @click.stop="shareConversation(conversation.id)" class="text-sm py-2">
                                                            <span v-html="ShareIcon"></span>
                                                            分享
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a @click.stop="deleteConversation(conversation.id, conversation.title)" class="text-sm text-error py-2">
                                                            <span v-html="TrashIcon"></span>
                                                            删除
                                                        </a>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                            </ul>
                        </div>

                        <!-- 底部加载更多指示器 -->
                        <div v-if="isLoading && conversationGroups.length > 0" class="text-center py-4">
                            <span class="loading loading-spinner loading-sm"></span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 用户信息 - 底部区域 -->
            <template #footer v-if="userInfo.name">
                <div class="flex items-center">
                    <div class="avatar mr-3">
                        <div class="w-8 rounded-full">
                            <img v-if="userInfo.avatar" :src="userInfo.avatar" alt="用户头像" />
                            <div v-else class="bg-primary text-primary-content flex items-center justify-center h-full text-xl font-bold">
                                {{ userInfo.name ? userInfo.name.charAt(0) : '?' }}
                            </div>
                        </div>
                    </div>
                    <div class="flex-1 min-w-0">
                        <div class="font-medium truncate">{{ userInfo.name }}</div>
                        <div class="text-xs truncate text-base-content/60">{{ userInfo.jobTitle || '未设置职位' }}</div>
                    </div>
                </div>
            </template>
        </BaseSidebar>
    `
};
