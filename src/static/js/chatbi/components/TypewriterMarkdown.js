import { ref, watch, onBeforeUnmount } from 'vue';
import { renderMarkdown, renderMarkdownLite } from '../../utils/MarkdownRenderer.js';

export default {
    name: 'TypewriterMarkdown',
    props: {
        content: { type: String, required: true },
        renderedContent: { type: String, default: '' },
        isStreaming: { type: Boolean, default: false },
        // 速度配置
        typewriterBaseCps: { type: Number, default: 24 },
        typewriterFinishCps: { type: Number, default: 240 },
        // 容器类
        containerClass: { type: String, default: 'markdown-content min-w-0' }
    },
    setup(props) {
        const renderedHtml = ref('');

        // 动画状态
        const targetContent = ref('');
        const visibleCount = ref(0);
        const isAnimating = ref(false);
        let rafId = null;
        let lastTs = 0;
        // 使用浮点累积以避免“每帧至少一个字符”的现象，确保按cps平滑推进
        let progressCount = 0;

        const getCps = (forceFast) => {
            const base = typeof props.typewriterBaseCps === 'number' ? props.typewriterBaseCps : 24;
            const fast = typeof props.typewriterFinishCps === 'number' ? props.typewriterFinishCps : Math.max(base * 3, base + 60);
            return (forceFast || !props.isStreaming) ? fast : base;
        };

        const stopAnimation = () => {
            if (rafId) cancelAnimationFrame(rafId);
            rafId = null;
            isAnimating.value = false;
        };

        const startAnimation = (forceFast = false) => {
            if (rafId) cancelAnimationFrame(rafId);
            isAnimating.value = true;
            lastTs = 0;
            // 保持已展示的字符数量，重置累积器为当前可见值
            progressCount = visibleCount.value;

            const step = (ts) => {
                if (!lastTs) lastTs = ts;
                const dt = (ts - lastTs) / 1000;
                lastTs = ts;

                const fullText = targetContent.value || '';
                const total = fullText.length;

                if (visibleCount.value < total) {
                    const cps = getCps(forceFast);
                    const lengthFactor = Math.min(1.2, 1 + total / 10000); // 温和的长度调节
                    progressCount += cps * dt * lengthFactor;
                    const nextCount = Math.min(total, Math.floor(progressCount));

                    if (nextCount > visibleCount.value) {
                        visibleCount.value = nextCount;
                        const partial = fullText.slice(0, visibleCount.value);
                        try {
                            // 流式阶段用轻量渲染，完成后再做完整高亮
                            renderedHtml.value = props.isStreaming ? renderMarkdownLite(partial) : renderMarkdown(partial);
                        } catch (e) {
                            renderedHtml.value = (partial || '').replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/\n/g, '<br>');
                        }
                    }

                    rafId = requestAnimationFrame(step);
                } else {
                    isAnimating.value = false;
                    rafId = null;
                    if (!props.isStreaming && props.renderedContent) {
                        renderedHtml.value = props.renderedContent;
                    } else if (!props.isStreaming) {
                        renderedHtml.value = renderMarkdown(fullText);
                    }
                }
            };

            rafId = requestAnimationFrame(step);
        };

        const sync = () => {
            const contentStr = typeof props.content === 'string' ? props.content : (props.content ? JSON.stringify(props.content) : '');
            targetContent.value = contentStr;

            if (!props.isStreaming) {
                stopAnimation();
                visibleCount.value = contentStr.length;
                progressCount = visibleCount.value;
                renderedHtml.value = props.renderedContent || renderMarkdown(contentStr);
                return;
            }

            // 流式：推进到新目标
            if (visibleCount.value > contentStr.length) {
                visibleCount.value = contentStr.length;
                progressCount = visibleCount.value;
            }
            startAnimation(false);
        };

        watch(() => props.content, () => sync(), { immediate: true });
        watch(() => props.renderedContent, () => { if (!props.isStreaming) sync(); });
        watch(() => props.isStreaming, (nowStreaming) => {
            if (!nowStreaming) {
                if (targetContent.value) startAnimation(true); else stopAnimation();
            }
        });

        onBeforeUnmount(() => stopAnimation());

        return { renderedHtml };
    },
    template: `
        <div :data-skip-hljs="isStreaming ? 'true' : null" :class="containerClass" v-html="renderedHtml"></div>
    `
};
