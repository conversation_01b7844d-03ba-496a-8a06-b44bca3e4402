/**
 * 图片上传服务
 * 处理图片文件上传到七牛云
 */

/**
 * 上传图片文件到服务器
 * @param {File} file - 要上传的图片文件
 * @param {Function} onProgress - 上传进度回调函数 (可选)
 * @returns {Promise<Object>} 上传结果
 */
export async function uploadImage(file, onProgress = null) {
    try {
        // 验证文件类型
        if (!file.type.startsWith('image/')) {
            throw new Error('只支持上传图片文件');
        }

        // 验证文件大小 (10MB)
        const maxSize = 10 * 1024 * 1024;
        if (file.size > maxSize) {
            throw new Error('图片文件大小不能超过10MB');
        }

        // 创建FormData
        const formData = new FormData();
        formData.append('file', file);

        // 创建XMLHttpRequest以支持进度监听
        return new Promise((resolve, reject) => {
            const xhr = new XMLHttpRequest();

            // 监听上传进度
            if (onProgress && xhr.upload) {
                xhr.upload.addEventListener('progress', (event) => {
                    if (event.lengthComputable) {
                        const percentComplete = (event.loaded / event.total) * 100;
                        onProgress(percentComplete);
                    }
                });
            }

            // 监听请求完成
            xhr.addEventListener('load', () => {
                if (xhr.status === 200) {
                    try {
                        const result = JSON.parse(xhr.responseText);
                        if (result.success) {
                            resolve(result.data);
                        } else {
                            reject(new Error(result.error || '上传失败'));
                        }
                    } catch (e) {
                        reject(new Error('解析响应失败'));
                    }
                } else {
                    try {
                        const errorResult = JSON.parse(xhr.responseText);
                        reject(new Error(errorResult.error || `上传失败 (${xhr.status})`));
                    } catch (e) {
                        reject(new Error(`上传失败 (${xhr.status})`));
                    }
                }
            });

            // 监听请求错误
            xhr.addEventListener('error', () => {
                reject(new Error('网络错误，上传失败'));
            });

            // 监听请求超时
            xhr.addEventListener('timeout', () => {
                reject(new Error('上传超时'));
            });

            // 配置请求
            xhr.open('POST', '/api/upload');
            xhr.timeout = 30000; // 30秒超时
            xhr.withCredentials = true; // 包含认证cookie

            // 发送请求
            xhr.send(formData);
        });

    } catch (error) {
        throw error;
    }
}

/**
 * 从粘贴事件中提取图片文件
 * @param {ClipboardEvent} event - 粘贴事件
 * @returns {File|null} 图片文件或null
 */
export function extractImageFromPaste(event) {
    console.log('extractImageFromPaste 被调用', event);

    // 检查事件对象
    if (!event) {
        console.warn('粘贴事件对象为空');
        return null;
    }

    // 检查剪贴板数据
    const clipboardData = event.clipboardData;
    console.log('剪贴板数据:', clipboardData);

    if (!clipboardData) {
        console.warn('剪贴板数据不存在');
        return null;
    }

    const items = clipboardData.items;
    console.log('剪贴板项目:', items, '数量:', items ? items.length : 0);

    if (!items) {
        console.warn('剪贴板项目不存在');
        return null;
    }

    for (let i = 0; i < items.length; i++) {
        const item = items[i];
        console.log(`项目 ${i}:`, {
            type: item.type,
            kind: item.kind
        });

        // 检查是否是图片类型
        if (item.type.startsWith('image/')) {
            console.log('发现图片类型项目:', item.type);
            const file = item.getAsFile();
            console.log('获取的文件对象:', file);

            if (file) {
                console.log('成功提取图片文件:', {
                    name: file.name,
                    type: file.type,
                    size: file.size
                });
                return file;
            }
        }
    }

    console.log('未找到图片文件');
    return null;
}

/**
 * 创建图片预览URL
 * @param {File} file - 图片文件
 * @returns {string} 预览URL
 */
export function createImagePreviewUrl(file) {
    return URL.createObjectURL(file);
}

/**
 * 释放图片预览URL
 * @param {string} url - 预览URL
 */
export function revokeImagePreviewUrl(url) {
    URL.revokeObjectURL(url);
}

/**
 * 验证图片文件
 * @param {File} file - 要验证的文件
 * @returns {Object} 验证结果 {valid: boolean, error?: string}
 */
export function validateImageFile(file) {
    // 检查文件类型
    if (!file.type.startsWith('image/')) {
        return {
            valid: false,
            error: '只支持上传图片文件'
        };
    }

    // 检查文件大小 (10MB)
    const maxSize = 10 * 1024 * 1024;
    if (file.size > maxSize) {
        return {
            valid: false,
            error: '图片文件大小不能超过10MB'
        };
    }

    // 检查支持的图片格式
    const supportedTypes = [
        'image/jpeg',
        'image/jpg',
        'image/png',
        'image/gif',
        'image/webp'
    ];

    if (!supportedTypes.includes(file.type)) {
        return {
            valid: false,
            error: '不支持的图片格式，请使用 JPEG、PNG、GIF 或 WebP 格式'
        };
    }

    return { valid: true };
}

/**
 * 格式化文件大小
 * @param {number} bytes - 字节数
 * @returns {string} 格式化后的大小
 */
export function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
