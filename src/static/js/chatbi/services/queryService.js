/**
 * Query Service (Polling mode)
 *
 * 仅负责向后端发起查询请求并返回必要的ID，
 * 前端根据 chat_history_id 统一走 /query/poll 轮询。
 */

/**
 * 发起查询请求（非流式）
 * @param {Object} options
 * @param {string} options.query - 用户问题
 * @param {Array<string>} options.images - 图片URL集合
 * @param {string|null} options.conversationId - 会话ID（可为空，后端会生成）
 * @param {string|null} options.agent - 指定agent（可选）
 * @returns {Promise<{conversationId: string, chatHistoryId: number|null}>}
 */
export async function requestQuery({ query, images = [], conversationId = null, agent = null }) {
    const payload = {
        query,
        timestamp: Date.now()
    };

    if (images && images.length > 0) payload.images = images;
    if (conversationId) payload.conversation_id = conversationId;
    if (agent) payload.agent = agent;

    // 添加用户信息（包括位置信息）
    if (window.userInfo) {
        payload.user_info = {
            name: window.userInfo.name || '访客',
            location: window.userInfo.location || null
        };
    }

    const resp = await fetch('/query', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
    });

    if (!resp.ok) {
        let msg = `请求失败: ${resp.status}`;
        try {
            const err = await resp.json();
            if (err && err.error) msg = err.error;
        } catch (_) {}
        throw new Error(msg);
    }

    const data = await resp.json();
    return {
        conversationId: data.conversation_id,
        chatHistoryId: data.chat_history_id || null
    };
}

