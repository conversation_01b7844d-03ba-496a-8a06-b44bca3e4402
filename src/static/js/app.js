import { createApp } from 'vue';
import AppLayout from './chatbi/layouts/ChatbiLayout.js';
import ShareLayout from './chatbi/layouts/ShareLayout.js';

// 检查是否是共享会话
const isSharedConversation = window.sharedConversation && window.sharedConversation.isShared;

// 创建Vue应用实例
const app = createApp({
    components: {
        AppLayout,
        ShareLayout
    },
    setup() {
        return {
            isSharedConversation
        };
    },
    template: `
        <ShareLayout v-if="isSharedConversation" />
        <AppLayout v-else />
    `
});

// 挂载应用到DOM
app.mount('#app');
