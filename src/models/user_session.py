"""
用户Session领域模型

定义用户登录会话的核心业务实体和值对象
"""

from dataclasses import dataclass
from datetime import datetime
from typing import Optional
import secrets


@dataclass
class UserSession:
    """
    用户Session领域实体
    
    表示用户的登录会话，包含认证信息和状态管理
    """
    session_id: str
    open_id: str
    refresh_token: str
    access_token: Optional[str] = None
    access_token_expires_at: Optional[datetime] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    last_active_at: Optional[datetime] = None
    is_active: bool = True
    
    @classmethod
    def create_new_session(cls, open_id: str, refresh_token: str, 
                          access_token: Optional[str] = None,
                          access_token_expires_at: Optional[datetime] = None) -> 'UserSession':
        """
        创建新的用户session
        
        Args:
            open_id: 飞书用户open_id
            refresh_token: 飞书refresh token
            access_token: 当前access token
            access_token_expires_at: access token过期时间
            
        Returns:
            UserSession: 新创建的session实例
        """
        now = datetime.now()
        return cls(
            session_id=cls._generate_session_id(),
            open_id=open_id,
            refresh_token=refresh_token,
            access_token=access_token,
            access_token_expires_at=access_token_expires_at,
            created_at=now,
            updated_at=now,
            last_active_at=now,
            is_active=True
        )
    
    @staticmethod
    def _generate_session_id() -> str:
        """生成安全的session ID"""
        return secrets.token_urlsafe(32)
    
    def update_access_token(self, access_token: str, expires_at: datetime) -> None:
        """
        更新access token
        
        Args:
            access_token: 新的access token
            expires_at: 过期时间
        """
        self.access_token = access_token
        self.access_token_expires_at = expires_at
        self.updated_at = datetime.now()
        self.last_active_at = datetime.now()
    
    def mark_active(self) -> None:
        """标记session为活跃状态"""
        self.last_active_at = datetime.now()
        self.updated_at = datetime.now()
    
    def deactivate(self) -> None:
        """停用session"""
        self.is_active = False
        self.updated_at = datetime.now()
    
    def is_expired(self, max_inactive_days: int = 30) -> bool:
        """
        检查session是否过期
        
        Args:
            max_inactive_days: 最大非活跃天数
            
        Returns:
            bool: 是否过期
        """
        if not self.is_active:
            return True
        
        if not self.last_active_at:
            return True
        
        inactive_days = (datetime.now() - self.last_active_at).days
        return inactive_days > max_inactive_days
    
    def is_access_token_expired(self) -> bool:
        """
        检查access token是否过期
        
        Returns:
            bool: 是否过期
        """
        if not self.access_token_expires_at:
            return True
        return datetime.now() >= self.access_token_expires_at
    
    def to_dict(self) -> dict:
        """转换为字典格式"""
        return {
            'session_id': self.session_id,
            'open_id': self.open_id,
            'refresh_token': self.refresh_token,
            'access_token': self.access_token,
            'access_token_expires_at': self.access_token_expires_at,
            'created_at': self.created_at,
            'updated_at': self.updated_at,
            'last_active_at': self.last_active_at,
            'is_active': self.is_active
        }


@dataclass
class SessionToken:
    """
    Session Token值对象
    
    封装token相关的业务逻辑
    """
    access_token: str
    refresh_token: str
    expires_at: datetime
    
    def is_access_token_expired(self) -> bool:
        """检查access token是否过期"""
        return datetime.now() >= self.expires_at
    
    def is_near_expiry(self, minutes_threshold: int = 10) -> bool:
        """
        检查access token是否即将过期
        
        Args:
            minutes_threshold: 过期阈值（分钟）
            
        Returns:
            bool: 是否即将过期
        """
        time_diff = (self.expires_at - datetime.now()).total_seconds() / 60
        return time_diff <= minutes_threshold
