"""
推荐领域模型

定义推荐相关的核心业务实体和值对象
"""

from dataclasses import dataclass
from datetime import datetime, timedelta
from typing import List, Optional
from enum import Enum
import json


class RecommendationStatus(Enum):
    """推荐生成状态枚举"""
    PENDING = 0      # 待生成
    GENERATING = 1   # 生成中
    COMPLETED = 2    # 生成完成
    FAILED = 3       # 生成失败


@dataclass
class RecommendationQuery:
    """推荐查询值对象"""
    content: str
    priority: int = 3  # 优先级，1最高，3最低
    
    def __post_init__(self):
        """初始化后验证"""
        if not self.content or not self.content.strip():
            raise ValueError("推荐查询内容不能为空")
        
        if self.priority < 1 or self.priority > 3:
            raise ValueError("优先级必须在1-3之间")
    
    def get_clean_content(self) -> str:
        """获取清理后的内容"""
        return self.content.strip()


@dataclass
class UserRecommendation:
    """
    用户推荐领域实体
    
    表示为特定用户生成的推荐问题集合
    """
    user_email: str
    user_open_id: str
    recommendations: List[str]
    generated_at: datetime
    expires_at: datetime
    status: RecommendationStatus
    error_message: Optional[str] = None
    id: Optional[int] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    def __post_init__(self):
        """初始化后验证"""
        if not self.user_email or not self.user_email.strip():
            raise ValueError("用户邮箱不能为空")
        
        if not self.user_open_id or not self.user_open_id.strip():
            raise ValueError("用户open_id不能为空")
        
        if not isinstance(self.recommendations, list):
            raise ValueError("推荐列表必须是list类型")
        
        if self.expires_at <= self.generated_at:
            raise ValueError("过期时间必须晚于生成时间")
    
    @classmethod
    def create_new_recommendation(
        cls, 
        user_email: str, 
        user_open_id: str, 
        recommendations: List[str],
        expire_hours: int = 24
    ) -> 'UserRecommendation':
        """
        创建新的用户推荐
        
        Args:
            user_email: 用户邮箱
            user_open_id: 用户open_id
            recommendations: 推荐问题列表
            expire_hours: 过期小时数，默认24小时
            
        Returns:
            UserRecommendation: 新创建的推荐实例
        """
        now = datetime.now()
        expires_at = now + timedelta(hours=expire_hours)
        
        return cls(
            user_email=user_email.strip(),
            user_open_id=user_open_id.strip(),
            recommendations=recommendations,
            generated_at=now,
            expires_at=expires_at,
            status=RecommendationStatus.COMPLETED,
            created_at=now,
            updated_at=now
        )
    
    @classmethod
    def create_pending_recommendation(
        cls,
        user_email: str,
        user_open_id: str,
        expire_hours: int = 24
    ) -> 'UserRecommendation':
        """
        创建待生成的推荐记录
        
        Args:
            user_email: 用户邮箱
            user_open_id: 用户open_id
            expire_hours: 过期小时数，默认24小时
            
        Returns:
            UserRecommendation: 待生成的推荐实例
        """
        now = datetime.now()
        expires_at = now + timedelta(hours=expire_hours)
        
        return cls(
            user_email=user_email.strip(),
            user_open_id=user_open_id.strip(),
            recommendations=[],  # 空列表，待生成
            generated_at=now,
            expires_at=expires_at,
            status=RecommendationStatus.PENDING,
            created_at=now,
            updated_at=now
        )
    
    def is_expired(self) -> bool:
        """检查推荐是否已过期"""
        return datetime.now() > self.expires_at
    
    def is_valid(self) -> bool:
        """检查推荐是否有效（未过期且状态为完成）"""
        return not self.is_expired() and self.status == RecommendationStatus.COMPLETED
    
    def mark_as_generating(self):
        """标记为生成中状态"""
        self.status = RecommendationStatus.GENERATING
        self.updated_at = datetime.now()
        self.error_message = None
    
    def mark_as_completed(self, recommendations: List[str]):
        """标记为完成状态"""
        if not recommendations:
            raise ValueError("完成状态的推荐列表不能为空")
        
        self.recommendations = recommendations
        self.status = RecommendationStatus.COMPLETED
        self.updated_at = datetime.now()
        self.error_message = None
    
    def mark_as_failed(self, error_message: str):
        """标记为失败状态"""
        if not error_message or not error_message.strip():
            raise ValueError("失败状态必须提供错误信息")
        
        self.status = RecommendationStatus.FAILED
        self.error_message = error_message.strip()
        self.updated_at = datetime.now()
    
    def get_recommendations_subset(self, count: int) -> List[str]:
        """
        获取推荐的子集
        
        Args:
            count: 需要的推荐数量
            
        Returns:
            List[str]: 推荐问题列表
        """
        if count <= 0:
            return []
        
        return self.recommendations[:count]
    
    def to_json_dict(self) -> dict:
        """转换为JSON字典格式"""
        return {
            'user_email': self.user_email,
            'user_open_id': self.user_open_id,
            'recommendations': self.recommendations,
            'generated_at': self.generated_at.isoformat() if self.generated_at else None,
            'expires_at': self.expires_at.isoformat() if self.expires_at else None,
            'status': self.status.value,
            'error_message': self.error_message,
            'id': self.id
        }
    
    def get_recommendations_json(self) -> str:
        """获取推荐列表的JSON字符串"""
        return json.dumps(self.recommendations, ensure_ascii=False)


@dataclass
class ActiveUser:
    """活跃用户值对象"""
    email: str
    open_id: str
    name: str
    last_activity_time: Optional[datetime] = None
    
    def __post_init__(self):
        """初始化后验证"""
        if not self.email or not self.email.strip():
            raise ValueError("用户邮箱不能为空")
        
        if not self.open_id or not self.open_id.strip():
            raise ValueError("用户open_id不能为空")
        
        if not self.name or not self.name.strip():
            raise ValueError("用户姓名不能为空")
    
    def get_user_info_dict(self) -> dict:
        """获取用户信息字典"""
        return {
            'email': self.email,
            'open_id': self.open_id,
            'name': self.name
        }


@dataclass
class RecommendationGenerationTask:
    """推荐生成任务值对象"""
    user: ActiveUser
    current_user_queries: List[RecommendationQuery]
    other_users_queries: List[RecommendationQuery]
    recommendations_count: int = 10
    word_limit: int = 50
    
    def __post_init__(self):
        """初始化后验证"""
        if self.recommendations_count <= 0:
            raise ValueError("推荐数量必须大于0")
        
        if self.word_limit <= 0:
            raise ValueError("字数限制必须大于0")
    
    def has_sufficient_data(self) -> bool:
        """检查是否有足够的数据生成推荐"""
        return len(self.current_user_queries) > 0 or len(self.other_users_queries) > 0
    
    def get_current_user_contents(self) -> List[str]:
        """获取当前用户查询内容列表"""
        return [query.get_clean_content() for query in self.current_user_queries]
    
    def get_other_users_contents(self) -> List[str]:
        """获取其他用户查询内容列表"""
        return [query.get_clean_content() for query in self.other_users_queries]
