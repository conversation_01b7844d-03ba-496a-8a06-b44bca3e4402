"""
部门Top10常问清单领域模型

定义部门周度Top10问题分析的核心业务实体和值对象
"""

from dataclasses import dataclass
from datetime import datetime, date
from typing import Optional, List, Dict, Any
from enum import Enum
import json


class AnalysisStatus(Enum):
    """分析状态枚举"""
    PENDING = 0      # 待分析
    PROCESSING = 1   # 分析中
    COMPLETED = 2    # 分析完成
    FAILED = 3       # 分析失败


@dataclass
class TopQuestion:
    """
    Top问题值对象
    
    表示一个归类后的热门问题
    """
    rank: int                           # 排名 (1-10)
    question_category: str              # 问题类别
    representative_question: str        # 代表性问题
    frequency: int                      # 出现频次
    percentage: float                   # 占比
    similar_questions: List[str]        # 相似问题列表
    
    def __post_init__(self):
        """初始化后验证"""
        if self.rank < 1 or self.rank > 10:
            raise ValueError("排名必须在1-10之间")
        
        if not self.question_category or not self.question_category.strip():
            raise ValueError("问题类别不能为空")
        
        if not self.representative_question or not self.representative_question.strip():
            raise ValueError("代表性问题不能为空")
        
        if self.frequency < 0:
            raise ValueError("频次不能为负数")
        
        if self.percentage < 0 or self.percentage > 100:
            raise ValueError("占比必须在0-100之间")
        
        if not isinstance(self.similar_questions, list):
            raise ValueError("相似问题必须是列表类型")

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "rank": self.rank,
            "question_category": self.question_category,
            "representative_question": self.representative_question,
            "frequency": self.frequency,
            "percentage": self.percentage,
            "similar_questions": self.similar_questions
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TopQuestion':
        """从字典创建实例"""
        return cls(
            rank=data["rank"],
            question_category=data["question_category"],
            representative_question=data["representative_question"],
            frequency=data["frequency"],
            percentage=data["percentage"],
            similar_questions=data["similar_questions"]
        )


@dataclass
class DepartmentTopQuestionsWeekly:
    """
    部门Top10常问清单周度分析领域实体
    
    表示某个部门在特定周的Top10问题分析结果
    """
    department_name: str
    week_start_date: date
    week_end_date: date
    top_questions: List[TopQuestion]
    total_conversations: int
    total_user_queries: int
    analysis_status: AnalysisStatus
    error_message: Optional[str] = None
    id: Optional[int] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    def __post_init__(self):
        """初始化后验证"""
        if not self.department_name or not self.department_name.strip():
            raise ValueError("部门名称不能为空")
        
        if self.week_end_date <= self.week_start_date:
            raise ValueError("周结束日期必须晚于开始日期")
        
        if not isinstance(self.top_questions, list):
            raise ValueError("Top问题列表必须是list类型")
        
        if len(self.top_questions) > 10:
            raise ValueError("Top问题列表最多包含10个问题")
        
        if self.total_conversations < 0:
            raise ValueError("总对话数不能为负数")
        
        if self.total_user_queries < 0:
            raise ValueError("总用户查询数不能为负数")
        
        # 验证排名的唯一性
        ranks = [q.rank for q in self.top_questions]
        if len(ranks) != len(set(ranks)):
            raise ValueError("Top问题的排名必须唯一")

    def get_top_questions_json(self) -> str:
        """获取Top问题的JSON字符串"""
        return json.dumps([q.to_dict() for q in self.top_questions], ensure_ascii=False)

    def set_top_questions_from_json(self, json_str: str):
        """从JSON字符串设置Top问题"""
        try:
            data = json.loads(json_str)
            self.top_questions = [TopQuestion.from_dict(item) for item in data]
        except (json.JSONDecodeError, KeyError, TypeError) as e:
            raise ValueError(f"无效的Top问题JSON格式: {e}")

    def is_analysis_completed(self) -> bool:
        """检查分析是否已完成"""
        return self.analysis_status == AnalysisStatus.COMPLETED

    def is_analysis_failed(self) -> bool:
        """检查分析是否失败"""
        return self.analysis_status == AnalysisStatus.FAILED

    def is_analysis_in_progress(self) -> bool:
        """检查分析是否正在进行"""
        return self.analysis_status == AnalysisStatus.PROCESSING

    def mark_analysis_started(self):
        """标记分析开始"""
        self.analysis_status = AnalysisStatus.PROCESSING
        self.error_message = None

    def mark_analysis_completed(self, top_questions: List[TopQuestion]):
        """标记分析完成"""
        self.analysis_status = AnalysisStatus.COMPLETED
        self.top_questions = top_questions
        self.error_message = None

    def mark_analysis_failed(self, error_message: str):
        """标记分析失败"""
        self.analysis_status = AnalysisStatus.FAILED
        self.error_message = error_message

    def get_week_display(self) -> str:
        """获取周的显示格式"""
        return f"{self.week_start_date.strftime('%Y-%m-%d')} 至 {self.week_end_date.strftime('%Y-%m-%d')}"

    def get_analysis_status_display(self) -> str:
        """获取分析状态的显示文本"""
        status_map = {
            AnalysisStatus.PENDING: "待分析",
            AnalysisStatus.PROCESSING: "分析中",
            AnalysisStatus.COMPLETED: "已完成",
            AnalysisStatus.FAILED: "分析失败"
        }
        return status_map.get(self.analysis_status, "未知状态")


@dataclass
class DepartmentTopQuestionsQuery:
    """
    部门Top10问题查询条件值对象
    """
    department_name: Optional[str] = None
    week_start_date: Optional[date] = None
    week_end_date: Optional[date] = None
    analysis_status: Optional[AnalysisStatus] = None
    page: int = 1
    page_size: int = 20
    
    def __post_init__(self):
        """初始化后验证"""
        if self.page < 1:
            raise ValueError("页码必须大于0")
        
        if self.page_size < 1 or self.page_size > 100:
            raise ValueError("页面大小必须在1-100之间")
        
        if (self.week_start_date and self.week_end_date and 
            self.week_end_date <= self.week_start_date):
            raise ValueError("结束日期必须晚于开始日期")

    def get_offset(self) -> int:
        """获取查询偏移量"""
        return (self.page - 1) * self.page_size


@dataclass
class DepartmentTopQuestionsResult:
    """
    部门Top10问题查询结果值对象
    """
    items: List[DepartmentTopQuestionsWeekly]
    total_count: int
    page: int
    page_size: int
    total_pages: int
    
    def __post_init__(self):
        """初始化后验证"""
        if not isinstance(self.items, list):
            raise ValueError("查询结果必须是列表类型")
        
        if self.total_count < 0:
            raise ValueError("总数量不能为负数")
        
        if self.page < 1:
            raise ValueError("页码必须大于0")
        
        if self.page_size < 1:
            raise ValueError("页面大小必须大于0")

    def has_next_page(self) -> bool:
        """是否有下一页"""
        return self.page < self.total_pages

    def has_previous_page(self) -> bool:
        """是否有上一页"""
        return self.page > 1
