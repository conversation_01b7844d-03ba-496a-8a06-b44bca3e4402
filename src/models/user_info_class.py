from dataclasses import dataclass
from typing import Optional, Dict, Any

@dataclass
class UserInfo:
    """用户信息数据传输对象 - 用于API层和应用服务层之间的数据传输"""
    user_name: str
    email: str
    access_token: str
    union_id: str
    summerfarm_api_token: str
    open_id: str
    conversation_id: Optional[str] = None
    location: Optional[Dict[str, Any]] = None  # 用户位置信息，包含latitude, longitude等


@dataclass
class User:
    """用户领域实体 - 包含用户的核心属性和业务逻辑"""
    name: str
    email: str
    open_id: str
    user_id: Optional[str] = None
    avatar_url: Optional[str] = None
    job_title: Optional[str] = None
    department: Optional[str] = None
    phone: Optional[str] = None

    def __post_init__(self):
        """初始化后的验证逻辑"""
        if not self.name or not self.name.strip():
            raise ValueError("用户姓名不能为空")
        if not self.email or not self.email.strip():
            raise ValueError("用户邮箱不能为空")
        if not self.open_id or not self.open_id.strip():
            raise ValueError("用户open_id不能为空")

    def is_email_valid(self) -> bool:
        """验证邮箱格式是否有效"""
        return '@' in self.email and '.' in self.email.split('@')[1]

    def get_display_name(self) -> str:
        """获取用户显示名称"""
        return self.name.strip()

    def get_department_display(self) -> str:
        """获取部门显示名称"""
        return self.department if self.department and self.department != '未指定' else '未指定'

    def get_job_title_display(self) -> str:
        """获取职位显示名称"""
        return self.job_title if self.job_title and self.job_title != '未指定' else '未指定'

    def to_dict(self) -> Dict[str, str]:
        """转换为字典格式，保持与原有接口兼容"""
        return {
            "name": self.name,
            "email": self.email,
            "open_id": self.open_id,
            "avatar_url": self.avatar_url,
            "user_id": self.user_id,
            "job_title": self.get_job_title_display(),
            "department": self.get_department_display(),
            "phone": self.phone or "未指定"
        }
    