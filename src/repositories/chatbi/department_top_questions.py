"""
部门Top10常问清单数据访问层

提供部门周度Top10问题分析的数据库操作
"""

from typing import List, Optional, Tuple
from datetime import datetime, date
import json

from src.db.connection import execute_db_query
from src.models.department_top_questions import (
    DepartmentTopQuestionsWeekly, 
    TopQuestion, 
    AnalysisStatus,
    DepartmentTopQuestionsQuery,
    DepartmentTopQuestionsResult
)
from src.utils.logger import logger


class DepartmentTopQuestionsRepository:
    """部门Top10常问清单数据仓储"""

    @staticmethod
    def create(entity: DepartmentTopQuestionsWeekly) -> Optional[int]:
        """
        创建新的部门Top10问题分析记录
        
        Args:
            entity: 部门Top10问题实体
            
        Returns:
            Optional[int]: 创建成功返回记录ID，失败返回None
        """
        try:
            sql = """
                INSERT INTO department_top_questions_weekly 
                (department_name, week_start_date, week_end_date, top_questions, 
                 total_conversations, total_user_queries, analysis_status, error_message)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            """
            
            values = (
                entity.department_name,
                entity.week_start_date,
                entity.week_end_date,
                entity.get_top_questions_json(),
                entity.total_conversations,
                entity.total_user_queries,
                entity.analysis_status.value,
                entity.error_message
            )
            
            result = execute_db_query(sql, values, fetch='lastrowid')
            if result:
                logger.info(f"成功创建部门Top10问题记录: 部门={entity.department_name}, 周={entity.get_week_display()}")
                return result
            else:
                logger.exception(f"创建部门Top10问题记录失败: 部门={entity.department_name}")
                return None
                
        except Exception as e:
            logger.exception(f"创建部门Top10问题记录时发生错误: {e}", exc_info=True)
            return None

    @staticmethod
    def update(entity: DepartmentTopQuestionsWeekly) -> bool:
        """
        更新部门Top10问题分析记录
        
        Args:
            entity: 部门Top10问题实体
            
        Returns:
            bool: 更新是否成功
        """
        if not entity.id:
            logger.exception("更新记录时缺少ID")
            return False
            
        try:
            sql = """
                UPDATE department_top_questions_weekly 
                SET top_questions = %s, total_conversations = %s, total_user_queries = %s,
                    analysis_status = %s, error_message = %s, updated_at = CURRENT_TIMESTAMP
                WHERE id = %s
            """
            
            values = (
                entity.get_top_questions_json(),
                entity.total_conversations,
                entity.total_user_queries,
                entity.analysis_status.value,
                entity.error_message,
                entity.id
            )
            
            result = execute_db_query(sql, values, fetch='rowcount')
            if result and result > 0:
                logger.info(f"成功更新部门Top10问题记录: ID={entity.id}")
                return True
            else:
                logger.warning(f"更新部门Top10问题记录未影响任何行: ID={entity.id}")
                return False
                
        except Exception as e:
            logger.exception(f"更新部门Top10问题记录时发生错误: {e}", exc_info=True)
            return False

    @staticmethod
    def find_by_id(record_id: int) -> Optional[DepartmentTopQuestionsWeekly]:
        """
        根据ID查找记录
        
        Args:
            record_id: 记录ID
            
        Returns:
            Optional[DepartmentTopQuestionsWeekly]: 找到的记录，不存在返回None
        """
        try:
            sql = """
                SELECT id, department_name, week_start_date, week_end_date, top_questions,
                       total_conversations, total_user_queries, analysis_status, error_message,
                       created_at, updated_at
                FROM department_top_questions_weekly
                WHERE id = %s
            """
            
            result = execute_db_query(sql, (record_id,), fetch='one')
            if result:
                return DepartmentTopQuestionsRepository._row_to_entity(result)
            else:
                logger.warning(f"未找到ID为{record_id}的部门Top10问题记录")
                return None
                
        except Exception as e:
            logger.exception(f"根据ID查找部门Top10问题记录时发生错误: {e}", exc_info=True)
            return None

    @staticmethod
    def find_by_department_and_week(department_name: str, week_start_date: date) -> Optional[DepartmentTopQuestionsWeekly]:
        """
        根据部门和周查找记录
        
        Args:
            department_name: 部门名称
            week_start_date: 周开始日期
            
        Returns:
            Optional[DepartmentTopQuestionsWeekly]: 找到的记录，不存在返回None
        """
        try:
            sql = """
                SELECT id, department_name, week_start_date, week_end_date, top_questions,
                       total_conversations, total_user_queries, analysis_status, error_message,
                       created_at, updated_at
                FROM department_top_questions_weekly
                WHERE department_name = %s AND week_start_date = %s
            """
            
            result = execute_db_query(sql, (department_name, week_start_date), fetch='one')
            if result:
                return DepartmentTopQuestionsRepository._row_to_entity(result)
            else:
                return None
                
        except Exception as e:
            logger.exception(f"根据部门和周查找记录时发生错误: {e}", exc_info=True)
            return None

    @staticmethod
    def query(query_params: DepartmentTopQuestionsQuery) -> DepartmentTopQuestionsResult:
        """
        分页查询部门Top10问题记录
        
        Args:
            query_params: 查询参数
            
        Returns:
            DepartmentTopQuestionsResult: 查询结果
        """
        try:
            # 构建WHERE条件
            where_conditions = []
            params = []
            
            if query_params.department_name:
                where_conditions.append("department_name = %s")
                params.append(query_params.department_name)
            
            # 时间范围筛选：查找与用户选择的时间范围有交集的自然周
            # 一个自然周与时间范围有交集的条件是：
            # week_end_date >= 用户选择的开始日期 AND week_start_date <= 用户选择的结束日期
            if query_params.week_start_date:
                where_conditions.append("week_end_date >= %s")
                params.append(query_params.week_start_date)

            if query_params.week_end_date:
                where_conditions.append("week_start_date <= %s")
                params.append(query_params.week_end_date)
            
            if query_params.analysis_status:
                where_conditions.append("analysis_status = %s")
                params.append(query_params.analysis_status.value)
            
            where_clause = "WHERE " + " AND ".join(where_conditions) if where_conditions else ""
            
            # 查询总数
            count_sql = f"""
                SELECT COUNT(*) as total_count
                FROM department_top_questions_weekly
                {where_clause}
            """
            
            count_result = execute_db_query(count_sql, tuple(params), fetch='one')
            total_count = count_result['total_count'] if count_result else 0
            
            # 计算总页数
            total_pages = (total_count + query_params.page_size - 1) // query_params.page_size
            
            # 查询数据
            data_sql = f"""
                SELECT id, department_name, week_start_date, week_end_date, top_questions,
                       total_conversations, total_user_queries, analysis_status, error_message,
                       created_at, updated_at
                FROM department_top_questions_weekly
                {where_clause}
                ORDER BY week_start_date DESC, department_name ASC
                LIMIT %s OFFSET %s
            """
            
            data_params = params + [query_params.page_size, query_params.get_offset()]
            data_results = execute_db_query(data_sql, tuple(data_params), fetch='all')
            
            # 转换为实体对象
            items = []
            if data_results:
                for row in data_results:
                    entity = DepartmentTopQuestionsRepository._row_to_entity(row)
                    if entity:
                        items.append(entity)
            
            return DepartmentTopQuestionsResult(
                items=items,
                total_count=total_count,
                page=query_params.page,
                page_size=query_params.page_size,
                total_pages=total_pages
            )
            
        except Exception as e:
            logger.exception(f"查询部门Top10问题记录时发生错误: {e}", exc_info=True)
            return DepartmentTopQuestionsResult(
                items=[],
                total_count=0,
                page=query_params.page,
                page_size=query_params.page_size,
                total_pages=0
            )

    @staticmethod
    def get_departments_with_data() -> List[str]:
        """
        获取有数据的部门列表
        
        Returns:
            List[str]: 部门名称列表
        """
        try:
            sql = """
                SELECT DISTINCT department_name
                FROM department_top_questions_weekly
                WHERE department_name IS NOT NULL AND department_name != ''
                ORDER BY department_name ASC
            """
            
            results = execute_db_query(sql, (), fetch='all')
            if results:
                return [row['department_name'] for row in results]
            else:
                return []
                
        except Exception as e:
            logger.exception(f"获取部门列表时发生错误: {e}", exc_info=True)
            return []

    @staticmethod
    def _row_to_entity(row: dict) -> Optional[DepartmentTopQuestionsWeekly]:
        """
        将数据库行转换为实体对象
        
        Args:
            row: 数据库查询结果行
            
        Returns:
            Optional[DepartmentTopQuestionsWeekly]: 转换后的实体对象
        """
        try:
            # 解析Top问题JSON
            top_questions = []
            if row['top_questions']:
                questions_data = json.loads(row['top_questions'])
                top_questions = [TopQuestion.from_dict(q) for q in questions_data]
            
            return DepartmentTopQuestionsWeekly(
                id=row['id'],
                department_name=row['department_name'],
                week_start_date=row['week_start_date'],
                week_end_date=row['week_end_date'],
                top_questions=top_questions,
                total_conversations=row['total_conversations'],
                total_user_queries=row['total_user_queries'],
                analysis_status=AnalysisStatus(row['analysis_status']),
                error_message=row['error_message'],
                created_at=row['created_at'],
                updated_at=row['updated_at']
            )
            
        except Exception as e:
            logger.exception(f"转换数据库行为实体对象时发生错误: {e}", exc_info=True)
            return None
