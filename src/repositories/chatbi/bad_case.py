"""
Chat bad case repository module.

This module provides data access functions for managing bad case conversations.
"""

import json
from datetime import datetime
from mysql.connector import Error

from src.utils.logger import logger
from src.db.connection import execute_db_query

def mark_conversation_as_bad_case(conversation_id: str, is_bad_case: bool = True, marked_by: str = None,
                                feedback_tags: list = None, custom_feedback: str = None,
                                chat_history_id: int = None) -> bool:
    """
    Mark or unmark a conversation as a bad case using the new bad_case table.

    注意：此函数保持向后兼容，但推荐使用新的mark_chat_history_as_bad_case函数

    Args:
        conversation_id (str): The ID of the conversation to mark
        is_bad_case (bool, optional): Whether to mark as bad case (True) or unmark (False). Defaults to True.
        marked_by (str, optional): The username of the person marking the bad case. Defaults to None.
        feedback_tags (list, optional): List of selected feedback tags. Defaults to None.
        custom_feedback (str, optional): Custom feedback text from user. Defaults to None.
        chat_history_id (int, optional): The specific chat history ID to mark. Defaults to None.

    Returns:
        bool: True if the operation was successful, False otherwise
    """
    # 检查是否提供了chat_history_id，如果提供了就直接使用消息级API
    if chat_history_id is not None:
        # 使用新的消息级API
        from src.repositories.chatbi.case_repository import CaseRepository
        if is_bad_case:
            return CaseRepository.mark_chat_history_as_case(
                chat_history_id=chat_history_id,
                case_type='bad',
                marked_by=marked_by,
                feedback_tags=feedback_tags,
                custom_feedback=custom_feedback
            )
        else:
            return CaseRepository.unmark_chat_history_as_case(
                chat_history_id=chat_history_id,
                case_type='bad'
            )
    else:
        # 向后兼容：如果没有chat_history_id，查找会话中最近的消息
        from src.services.chatbot.history_service import get_latest_assistant_message_id
        chat_history_id = get_latest_assistant_message_id(conversation_id, marked_by)
        
        if not chat_history_id:
            logger.exception(f"找不到会话 {conversation_id} 的任何assistant消息，无法标记")
            return False
            
        # 使用找到的消息ID进行标记
        from src.repositories.chatbi.case_repository import CaseRepository
        if is_bad_case:
            return CaseRepository.mark_chat_history_as_case(
                chat_history_id=chat_history_id,
                case_type='bad',
                marked_by=marked_by,
                feedback_tags=feedback_tags,
                custom_feedback=custom_feedback
            )
        else:
            return CaseRepository.unmark_chat_history_as_case(
                chat_history_id=chat_history_id,
                case_type='bad'
            )

def get_conversation_bad_case_status(conversation_id: str) -> bool:
    """
    Check if a conversation is marked as a bad case using the new bad_case table.

    Args:
        conversation_id (str): The ID of the conversation to check

    Returns:
        bool: True if the conversation is marked as a bad case, False otherwise
    """
    if not conversation_id:
        logger.warning("Conversation ID is required to check bad case status")
        return False

    # Query the bad_case table
    sql = "SELECT id FROM bad_case WHERE conversation_id = %s"
    params = [conversation_id]

    # Note: username and email filters are no longer needed since we removed those fields
    # The conversation_id is unique per bad case, and user info can be obtained from chat_history
    # if username or email filters are needed, we can join with chat_history table

    # Limit to one row since we just need to check if the record exists
    sql += " LIMIT 1"

    try:
        result = execute_db_query(sql, tuple(params), fetch='one')
        # If result exists, the conversation is marked as a bad case
        return bool(result)
    except Error as e:
        # Error already logged
        return False
    except Exception as e:
        logger.exception(f"Unexpected error checking bad case status: {e}", exc_info=True)
        return False





def update_bad_case_repair_status(conversation_id: str, repair_status: int, repair_note: str = None) -> (bool, dict):
    """
    Update the repair status of a bad case and retrieve bad case info.

    Args:
        conversation_id (str): The ID of the conversation
        repair_status (int): The new repair status (0=未修复, 1=已修复, 2=暂不修复)
        repair_note (str, optional): The repair note explaining how the issue was fixed

    Returns:
        tuple[bool, dict]: A tuple containing a boolean indicating success and a dictionary with bad case info.
    """
    if not conversation_id:
        logger.warning("Conversation ID is required to update repair status")
        return False, None

    if repair_status not in [0, 1, 2]:
        logger.warning(f"Invalid repair status: {repair_status}. Must be 0, 1, or 2")
        return False, None

    try:
        # 更新修复状态和修复说明
        if repair_note is not None:
            update_sql = "UPDATE bad_case SET repair_status = %s, repair_note = %s, updated_at = CURRENT_TIMESTAMP WHERE conversation_id = %s"
            update_params = (repair_status, repair_note, conversation_id)
        else:
            update_sql = "UPDATE bad_case SET repair_status = %s, updated_at = CURRENT_TIMESTAMP WHERE conversation_id = %s"
            update_params = (repair_status, conversation_id)
        affected_rows = execute_db_query(update_sql, update_params, fetch='count', commit=True)

        if affected_rows > 0:
            logger.info(f"Successfully updated repair status for conversation {conversation_id} to {repair_status}")

            # 查询bad case信息，包括标记人和会话所有者的open_id
            # 使用分离的查询避免连接问题
            try:
                # 先查询基本信息，包括repair_note
                basic_sql = "SELECT marked_by, repair_note FROM bad_case WHERE conversation_id = %s"
                basic_result = execute_db_query(basic_sql, (conversation_id,), fetch='one')

                if not basic_result:
                    logger.warning(f"No bad_case record found for conversation {conversation_id}")
                    return True, None

                marked_by = basic_result.get('marked_by')
                repair_note = basic_result.get('repair_note')
                logger.info(f"Bad case marked by: '{marked_by}'")

                # 查询标记人的 open_id（如果存在）
                marked_by_open_id = None
                if marked_by:
                    marker_sql = "SELECT open_id FROM users WHERE name = %s LIMIT 1"
                    marker_result = execute_db_query(marker_sql, (marked_by,), fetch='one')
                    if marker_result:
                        marked_by_open_id = marker_result.get('open_id')
                        logger.info(f"Marker open_id: {marked_by_open_id}")
                    else:
                        logger.warning(f"No user found with name '{marked_by}' for marker")

                # 查询会话所有者的 open_id
                owner_open_id = None
                owner_sql = """
                    SELECT u.open_id
                    FROM chat_history ch
                    JOIN users u ON ch.username = u.name AND ch.email = u.email
                    WHERE ch.conversation_id = %s
                    LIMIT 1
                """
                owner_result = execute_db_query(owner_sql, (conversation_id,), fetch='one')
                if owner_result:
                    owner_open_id = owner_result.get('open_id')
                    logger.info(f"Owner open_id: {owner_open_id}")
                else:
                    logger.warning(f"No owner open_id found for conversation {conversation_id}")

                # 构建返回结果
                bad_case_info = {
                    'marked_by': marked_by,
                    'marked_by_open_id': marked_by_open_id,
                    'owner_open_id': owner_open_id,
                    'repair_note': repair_note
                }

                logger.info(f"Bad case info retrieved: {bad_case_info}")

                if marked_by_open_id or owner_open_id:
                    logger.info(f"Found open_ids - marker: {marked_by_open_id}, owner: {owner_open_id}")
                else:
                    logger.warning(f"No open_ids found for conversation {conversation_id}")

                return True, bad_case_info

            except Exception as query_error:
                logger.exception(f"Error querying bad case info: {query_error}", exc_info=True)
                return True, None  # 更新成功，但查询信息失败
        else:
            logger.warning(f"No bad case found for conversation {conversation_id} to update repair status")
            return False, None
    except Error as e:
        return False, None
    except Exception as e:
        logger.exception(f"Unexpected error updating repair status: {e}", exc_info=True)
        return False, None


def get_conversation_bad_case_feedback(conversation_id: str) -> dict:
    """
    Get feedback data for a bad case conversation.

    Args:
        conversation_id (str): The ID of the conversation to get feedback for

    Returns:
        dict: Feedback data including tags, custom text, and submission time, or None if no feedback exists
    """
    if not conversation_id:
        logger.warning("Conversation ID is required to get bad case feedback")
        return None

    sql = """
        SELECT feedback_tags, custom_feedback, feedback_submitted_at
        FROM bad_case
        WHERE conversation_id = %s AND feedback_submitted_at IS NOT NULL
        LIMIT 1
    """

    try:
        result = execute_db_query(sql, (conversation_id,), fetch='one')

        if not result:
            return None

        feedback_data = {
            'feedback_tags': [],
            'custom_feedback': result.get('custom_feedback', ''),
            'feedback_submitted_at': result.get('feedback_submitted_at')
        }

        # 解析JSON格式的标签数据
        feedback_tags_json = result.get('feedback_tags')
        if feedback_tags_json:
            try:
                feedback_data['feedback_tags'] = json.loads(feedback_tags_json)
            except (json.JSONDecodeError, TypeError) as e:
                logger.warning(f"Failed to parse feedback tags JSON for conversation {conversation_id}: {e}")
                feedback_data['feedback_tags'] = []

        return feedback_data

    except Error as e:
        logger.exception(f"Database error getting bad case feedback for {conversation_id}: {e}")
        return None
    except Exception as e:
        logger.exception(f"Unexpected error getting bad case feedback: {e}", exc_info=True)
        return None


def get_bulk_bad_case_feedback(conversation_ids: list) -> dict:
    """
    批量获取多个对话的坏案例反馈信息

    Args:
        conversation_ids (list): 对话ID列表

    Returns:
        dict: 以conversation_id为key，反馈数据为value的字典
    """
    if not conversation_ids:
        return {}

    # 移除重复的ID并过滤掉空值
    conversation_ids = list(set(filter(None, conversation_ids)))
    if not conversation_ids:
        return {}

    # 构建查询参数占位符
    placeholders = ','.join(['%s'] * len(conversation_ids))
    sql = f"""
        SELECT conversation_id, feedback_tags, custom_feedback, feedback_submitted_at
        FROM bad_case
        WHERE conversation_id IN ({placeholders}) AND feedback_submitted_at IS NOT NULL
    """

    try:
        results = execute_db_query(sql, tuple(conversation_ids), fetch='all')
        
        feedback_map = {}
        for result in results:
            conversation_id = result.get('conversation_id')
            if not conversation_id:
                continue
                
            feedback_data = {
                'feedback_tags': [],
                'custom_feedback': result.get('custom_feedback', ''),
                'feedback_submitted_at': result.get('feedback_submitted_at')
            }

            # 解析JSON格式的标签数据
            feedback_tags_json = result.get('feedback_tags')
            if feedback_tags_json:
                try:
                    feedback_data['feedback_tags'] = json.loads(feedback_tags_json)
                except (json.JSONDecodeError, TypeError) as e:
                    logger.warning(f"Failed to parse feedback tags JSON for conversation {conversation_id}: {e}")
                    feedback_data['feedback_tags'] = []

            feedback_map[conversation_id] = feedback_data

        return feedback_map

    except Error as e:
        logger.exception(f"Database error getting bulk bad case feedback: {e}")
        return {}
    except Exception as e:
        logger.exception(f"Unexpected error getting bulk bad case feedback: {e}", exc_info=True)
        return {}


def mark_chat_history_as_bad_case(chat_history_id: int, is_bad_case: bool = True, marked_by: str = None,
                                feedback_tags: list = None, custom_feedback: str = None) -> bool:
    """
    Mark or unmark a specific chat history message as a bad case.

    Args:
        chat_history_id (int): The ID of the chat history message to mark
        is_bad_case (bool, optional): Whether to mark as bad case (True) or unmark (False). Defaults to True.
        marked_by (str, optional): The username of the person marking the bad case. Defaults to None.
        feedback_tags (list, optional): List of selected feedback tags. Defaults to None.
        custom_feedback (str, optional): Custom feedback text from user. Defaults to None.

    Returns:
        bool: True if the operation was successful, False otherwise
    """
    # 直接使用新的CaseRepository接口，避免废弃警告
    from src.repositories.chatbi.case_repository import CaseRepository

    if is_bad_case:
        return CaseRepository.mark_chat_history_as_case(
            chat_history_id=chat_history_id,
            case_type='bad',
            marked_by=marked_by,
            feedback_tags=feedback_tags,
            custom_feedback=custom_feedback
        )
    else:
        return CaseRepository.unmark_chat_history_as_case(
            chat_history_id=chat_history_id,
            case_type='bad'
        )
