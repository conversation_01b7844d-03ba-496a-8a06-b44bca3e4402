"""
Chat history repository module.

This module provides data access functions for managing chatbot history records.
"""

from datetime import datetime
import json
import re
from typing import List, Dict, Any, Optional, Tuple
from mysql.connector import Error

from src.utils.logger import logger
from src.db.connection import execute_db_query, get_db_connection

def extract_agent_name_from_logs(logs: Optional[str]) -> str:
    """
    Extract agent name from logs field (for backward compatibility).

    在Agent as Tool架构下，agent信息现在直接通过agent参数传递，
    这个函数主要用于向后兼容旧的handoff模式。

    Looks for patterns like:
    - "Handoff from 主控制器 to [agent_name]"
    - "handoff to [agent_name]"

    Args:
        logs (str, optional): The logs content to parse

    Returns:
        str: The extracted agent name, or None if not found
    """
    if not logs:
        return None

    # Pattern 1: "Handoff from X to Y" - extract Y
    handoff_pattern = r'Handoff from .+ to (.+)'
    match = re.search(handoff_pattern, logs, re.IGNORECASE)
    if match:
        agent_name = match.group(1).strip()
        logger.debug(f"Extracted agent name from handoff pattern: {agent_name}")
        return agent_name

    # Pattern 2: "handoff to X" - extract X
    handoff_to_pattern = r'handoff to (.+)'
    match = re.search(handoff_to_pattern, logs, re.IGNORECASE)
    if match:
        agent_name = match.group(1).strip()
        logger.debug(f"Extracted agent name from handoff_to pattern: {agent_name}")
        return agent_name

    # Default to None if no handoff found
    return None

def save_message(username: str, email: str, conversation_id: str, role: str, content: str,
                timestamp: Optional[int] = None, logs: Optional[str] = None,
                output_as_input: Optional[str] = None, agent: Optional[str] = None, 
                resource_url: Optional[str] = None, time_spend: Optional[int] = None) -> bool:
    """
    Save a single chatbot message to the MySQL database.

    Args:
        username (str): The username of the message sender
        email (str): The email of the message sender
        conversation_id (str): The ID of the conversation
        role (str): The role of the message sender ('user', 'assistant', 'prompt', 'error')
        content (str): The content of the message
        timestamp (int, optional): The timestamp of the message in milliseconds. If None, current time is used.
        logs (str, optional): Additional logs associated with the message.
        output_as_input (str, optional): Structured output intended as input for the next turn (JSON format).
        agent (str, optional): The name of the agent that processed this message. If None, will be extracted from logs.
        resource_url (str, optional): Resource URLs (images, documents, etc.), multiple URLs separated by commas.
        time_spend (int, optional): AI响应耗时(秒)，从发起Agent请求开始到AI响应完成，只对role=assistant有意义.

    Returns:
        bool: True if the operation was successful, False otherwise
    """
    if not username or not email:
        logger.exception("Username and email are required to save a message")
        return False
    if not conversation_id:
        logger.exception("Conversation ID is required to save a message")
        return False
    if not role or role not in ('user', 'assistant', 'prompt', 'error'):
        logger.exception(f"Invalid role: {role}")
        return False
    if content is None:
        logger.exception("Message content cannot be None")
        return False

    # If no timestamp is provided, use current time in milliseconds
    current_timestamp = timestamp if timestamp is not None else int(datetime.now().timestamp() * 1000)

    # Extract agent name from logs if not explicitly provided
    agent_name = agent if agent is not None else extract_agent_name_from_logs(logs)

    # If still no agent name found, use default
    if agent_name is None:
        agent_name = 'master_controller_bot'

    # 对于非assistant角色的消息，time_spend应该为NULL
    if role != 'assistant':
        time_spend = None

    sql = """
        INSERT INTO chat_history (username, email, conversation_id, role, content, logs, timestamp, output_as_input, agent, resource_url, time_spend)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
    """
    values = (username, email, conversation_id, role, content, logs, current_timestamp, output_as_input, agent_name, resource_url, time_spend)

    try:
        execute_db_query(sql, values, commit=True)
        if time_spend is not None and role == 'assistant':
            logger.debug(f"Saved assistant message for user {username} ({email}) in conversation {conversation_id} with agent {agent_name}, time_spend: {time_spend}s")
        else:
            logger.debug(f"Saved message for user {username} ({email}) in conversation {conversation_id} with agent {agent_name}")
        return True
    except Error as e:
        # Error already logged
        return False
    except Exception as e:
        logger.exception(f"Unexpected error saving message: {e}", exc_info=True)
        return False



def load_history(username: str, email: str, limit: int = 20, offset: int = 0) -> Dict[str, List[Dict[str, Any]]]:
    """
    分页加载用户的聊天记录，按对话分组，并按每个对话的最新消息时间降序排列对话。

    实现逻辑:
    1. 查找用户在指定分页范围内的对话ID。
       - 按 `conversation_id` 分组。
       - 计算每个对话的最大时间戳 `MAX(timestamp)`。
       - 按 `MAX(timestamp)` 降序排序。
       - 应用 `LIMIT` 和 `OFFSET`。
    2. 获取这些选定对话ID的所有消息。
       - 使用 `WHERE conversation_id IN (...)`。
       - 按 `conversation_id` 和 `timestamp` 升序排序，以确保对话内消息有序。
    3. 将获取的消息按 `conversation_id` 组织成字典结构。
    4. 按照步骤1中获取的对话ID顺序，构建最终返回的有序字典。
    """
    if not username or not email:
        logger.exception("加载历史记录需要用户名和邮箱")
        return {}

    history: Dict[str, List[Dict[str, Any]]] = {}
    conn = None
    cursor = None

    try:
        conn = get_db_connection()
        # 使用buffered字典游标避免"Unread result found"错误
        cursor = conn.cursor(dictionary=True, buffered=True)

        # 步骤 1: 获取分页的对话ID列表
        convo_sql = """
            SELECT ch.conversation_id, MAX(ch.timestamp) as last_message_time
            FROM chat_history ch
            WHERE ch.username = %s AND ch.email = %s
            GROUP BY ch.conversation_id
            ORDER BY last_message_time DESC
            LIMIT %s OFFSET %s
        """
        logger.debug(f"执行对话ID查询: {convo_sql} | 参数: {(username, email, limit, offset)}")
        cursor.execute(convo_sql, (username, email, limit, offset))
        paginated_convos = cursor.fetchall()
        paginated_convo_ids = [row['conversation_id'] for row in paginated_convos]

        if not paginated_convo_ids:
            logger.info(f"用户 {username} ({email}) 在范围 offset={offset}, limit={limit} 内未找到对话")
            return {}

        # 步骤 2: 获取这些对话的所有消息，包含消息级别和对话级别的反馈信息
        placeholders = ','.join(['%s'] * len(paginated_convo_ids))
        messages_sql = f"""
            SELECT ch.id, ch.conversation_id, ch.role, ch.content, ch.logs, ch.timestamp, ch.updated_at, ch.resource_url, ch.agent, ch.time_spend, ch.is_in_process,
                   ch.time_to_first_token,
                   bc_msg.feedback_tags as msg_bad_feedback_tags,
                   bc_msg.custom_feedback as msg_bad_custom_feedback,
                   bc_msg.feedback_submitted_at as msg_bad_feedback_submitted_at,
                   gc_msg.feedback_tags as msg_good_feedback_tags,
                   gc_msg.custom_feedback as msg_good_custom_feedback,
                   gc_msg.feedback_submitted_at as msg_good_feedback_submitted_at,
                   bc_conv.feedback_tags as conv_bad_feedback_tags,
                   bc_conv.custom_feedback as conv_bad_custom_feedback,
                   bc_conv.feedback_submitted_at as conv_bad_feedback_submitted_at,
                   gc_conv.feedback_tags as conv_good_feedback_tags,
                   gc_conv.custom_feedback as conv_good_custom_feedback,
                   gc_conv.feedback_submitted_at as conv_good_feedback_submitted_at
            FROM chat_history ch
            LEFT JOIN bad_case bc_msg ON ch.id = bc_msg.chat_history_id
            LEFT JOIN good_case gc_msg ON ch.id = gc_msg.chat_history_id
            LEFT JOIN bad_case bc_conv ON ch.conversation_id = bc_conv.conversation_id AND bc_conv.chat_history_id IS NULL
            LEFT JOIN good_case gc_conv ON ch.conversation_id = gc_conv.conversation_id AND gc_conv.chat_history_id IS NULL
            WHERE ch.username = %s AND ch.email = %s AND ch.conversation_id IN ({placeholders})
            ORDER BY ch.id ASC -- 按消息ID升序排列单个对话内的消息
        """
        query_params = [username, email] + paginated_convo_ids
        logger.debug(f"执行消息查询: {messages_sql} | 参数数量: {len(query_params)}")
        cursor.execute(messages_sql, tuple(query_params))
        rows = cursor.fetchall()

        # 步骤 3: 构建临时历史字典，包含详细的反馈信息处理
        temp_history: Dict[str, List[Dict[str, Any]]] = {}
        for row in rows:
            convo_id = row['conversation_id']
            if convo_id not in temp_history:
                temp_history[convo_id] = []

            # 处理消息级别的好评反馈信息
            msg_good_feedback = None
            if row.get('msg_good_feedback_submitted_at'):
                msg_good_feedback = {
                    'feedback_tags': [],
                    'custom_feedback': row.get('msg_good_custom_feedback', ''),
                    'feedback_submitted_at': row.get('msg_good_feedback_submitted_at')
                }
                # 解析JSON格式的标签
                if row.get('msg_good_feedback_tags'):
                    try:
                        import json
                        msg_good_feedback['feedback_tags'] = json.loads(row['msg_good_feedback_tags'])
                    except (json.JSONDecodeError, TypeError):
                        msg_good_feedback['feedback_tags'] = []

            # 处理消息级别的差评反馈信息
            msg_bad_feedback = None
            if row.get('msg_bad_feedback_submitted_at'):
                msg_bad_feedback = {
                    'feedback_tags': [],
                    'custom_feedback': row.get('msg_bad_custom_feedback', ''),
                    'feedback_submitted_at': row.get('msg_bad_feedback_submitted_at')
                }
                # 解析JSON格式的标签
                if row.get('msg_bad_feedback_tags'):
                    try:
                        import json
                        msg_bad_feedback['feedback_tags'] = json.loads(row['msg_bad_feedback_tags'])
                    except (json.JSONDecodeError, TypeError):
                        msg_bad_feedback['feedback_tags'] = []

            # 处理对话级别的好评反馈信息（当chat_history_id为空时）
            conv_good_feedback = None
            if row.get('conv_good_feedback_submitted_at'):
                conv_good_feedback = {
                    'feedback_tags': [],
                    'custom_feedback': row.get('conv_good_custom_feedback', ''),
                    'feedback_submitted_at': row.get('conv_good_feedback_submitted_at')
                }
                # 解析JSON格式的标签
                if row.get('conv_good_feedback_tags'):
                    try:
                        import json
                        conv_good_feedback['feedback_tags'] = json.loads(row['conv_good_feedback_tags'])
                    except (json.JSONDecodeError, TypeError):
                        conv_good_feedback['feedback_tags'] = []

            # 处理对话级别的差评反馈信息（当chat_history_id为空时）
            conv_bad_feedback = None
            if row.get('conv_bad_feedback_submitted_at'):
                conv_bad_feedback = {
                    'feedback_tags': [],
                    'custom_feedback': row.get('conv_bad_custom_feedback', ''),
                    'feedback_submitted_at': row.get('conv_bad_feedback_submitted_at')
                }
                # 解析JSON格式的标签
                if row.get('conv_bad_feedback_tags'):
                    try:
                        import json
                        conv_bad_feedback['feedback_tags'] = json.loads(row['conv_bad_feedback_tags'])
                    except (json.JSONDecodeError, TypeError):
                        conv_bad_feedback['feedback_tags'] = []

            # 根据规则决定消息的good/bad case状态和反馈
            final_good_feedback = None
            final_bad_feedback = None
            is_good_case = False
            is_bad_case = False

            if msg_good_feedback or msg_bad_feedback:
                # 如果有消息级别的标记，使用消息级别的
                final_good_feedback = msg_good_feedback
                final_bad_feedback = msg_bad_feedback
                is_good_case = msg_good_feedback is not None
                is_bad_case = msg_bad_feedback is not None
            elif conv_good_feedback or conv_bad_feedback:
                # 如果有对话级别的标记且当前消息不是user角色，使用对话级别的
                if row['role'] != 'user':
                    final_good_feedback = conv_good_feedback
                    final_bad_feedback = conv_bad_feedback
                    is_good_case = conv_good_feedback is not None
                    is_bad_case = conv_bad_feedback is not None

            temp_history[convo_id].append({
                "id": row['id'],  # 添加消息ID
                "role": row['role'],
                "content": row['content'],
                "logs": row['logs'],
                "timestamp": row['timestamp'], # 保持 BIGINT，消息创建时间
                "updated_at": row['updated_at'], # 消息完成/更新时间
                "resource_url": row['resource_url'],
                "agent": row['agent'],
                "time_spend": row['time_spend'],
                "is_in_process": row['is_in_process'],
                "time_to_first_token": row['time_to_first_token'],
                "is_bad_case": is_bad_case,  # 根据规则确定的bad case状态
                "is_good_case": is_good_case,  # 根据规则确定的good case状态
                "isBadCase": is_bad_case,  # 保持向后兼容
                "isGoodCase": is_good_case,  # 保持向后兼容
                "goodCaseFeedback": final_good_feedback,  # 最终的good case反馈
                "badCaseFeedback": final_bad_feedback  # 最终的bad case反馈
            })

        # 步骤 4: 按分页顺序构建最终历史字典
        for convo_id in paginated_convo_ids:
            if convo_id in temp_history:
                history[convo_id] = temp_history[convo_id]
            else:
                # 这通常不应该发生，除非在两个查询之间数据被删除
                logger.warning(f"在消息查询中未找到预期的对话ID {convo_id}")


        logger.debug(f"为用户 {username} ({email}) 加载了 {len(history)} 个对话 (limit={limit}, offset={offset})")
        return history

    except Error as e:
        logger.exception(f"从MySQL加载分页历史记录失败，用户 {username} ({email}): {e}", exc_info=True)
        return {} # 出错时返回空字典
    except Exception as e:
        logger.exception(f"加载分页历史记录时发生意外错误，用户 {username} ({email}): {e}", exc_info=True)
        return {}
    finally:
        if cursor:
            cursor.close()
        if conn and conn.is_connected():
            conn.close()

def load_conversation(conversation_id: str, username: Optional[str] = None, email: Optional[str] = None) -> List[Dict[str, Any]]:
    """
    从 MySQL 加载指定对话的所有消息，包含 bad case 和 good case 状态。

    Args:
        conversation_id (str): 要加载的对话ID
        username (str, optional): 用户名过滤条件
        email (str, optional): 邮箱过滤条件

    Returns:
        List[Dict[str, Any]]: 对话消息列表，包含 is_bad_case 和 is_good_case 状态
    """
    if not conversation_id:
        logger.warning("对话ID是必需的")
        return []

    # 构建 SQL - 包含消息级别和对话级别的 bad case 和 good case 状态以及反馈信息
    sql = """
        SELECT ch.id, ch.conversation_id, ch.role, ch.content, ch.logs, ch.timestamp, ch.updated_at, ch.output_as_input, ch.resource_url, ch.time_to_first_token,
               bc_msg.feedback_tags as msg_bad_feedback_tags,
               bc_msg.custom_feedback as msg_bad_custom_feedback,
               bc_msg.feedback_submitted_at as msg_bad_feedback_submitted_at,
               gc_msg.feedback_tags as msg_good_feedback_tags,
               gc_msg.custom_feedback as msg_good_custom_feedback,
               gc_msg.feedback_submitted_at as msg_good_feedback_submitted_at,
               bc_conv.feedback_tags as conv_bad_feedback_tags,
               bc_conv.custom_feedback as conv_bad_custom_feedback,
               bc_conv.feedback_submitted_at as conv_bad_feedback_submitted_at,
               gc_conv.feedback_tags as conv_good_feedback_tags,
               gc_conv.custom_feedback as conv_good_custom_feedback,
               gc_conv.feedback_submitted_at as conv_good_feedback_submitted_at
        FROM chat_history ch
        LEFT JOIN bad_case bc_msg ON ch.id = bc_msg.chat_history_id
        LEFT JOIN good_case gc_msg ON ch.id = gc_msg.chat_history_id
        LEFT JOIN bad_case bc_conv ON ch.conversation_id = bc_conv.conversation_id AND bc_conv.chat_history_id IS NULL
        LEFT JOIN good_case gc_conv ON ch.conversation_id = gc_conv.conversation_id AND gc_conv.chat_history_id IS NULL
        WHERE ch.conversation_id = %s
    """
    values = (conversation_id,)

    # 如果提供了用户名和邮箱，添加过滤条件
    if username and email:
        sql += " AND ch.username = %s AND ch.email = %s"
        values = (conversation_id, username, email, )

    sql += " ORDER BY ch.id ASC"

    try:
        results = execute_db_query(sql, values, fetch='all')
        if not results:
            return []

        # 处理消息级别和对话级别的反馈信息
        processed_results = []
        for row in results:
            # 复制原始数据
            processed_row = dict(row)

            # 处理消息级别的负面反馈
            msg_bad_feedback = None
            if row.get('msg_bad_feedback_submitted_at'):
                msg_bad_feedback = {
                    'feedback_tags': [],
                    'custom_feedback': row.get('msg_bad_custom_feedback', ''),
                    'feedback_submitted_at': row.get('msg_bad_feedback_submitted_at')
                }
                # 解析JSON格式的标签
                if row.get('msg_bad_feedback_tags'):
                    try:
                        msg_bad_feedback['feedback_tags'] = json.loads(row['msg_bad_feedback_tags'])
                    except (json.JSONDecodeError, TypeError):
                        msg_bad_feedback['feedback_tags'] = []

            # 处理消息级别的正面反馈
            msg_good_feedback = None
            if row.get('msg_good_feedback_submitted_at'):
                msg_good_feedback = {
                    'feedback_tags': [],
                    'custom_feedback': row.get('msg_good_custom_feedback', ''),
                    'feedback_submitted_at': row.get('msg_good_feedback_submitted_at')
                }
                # 解析JSON格式的标签
                if row.get('msg_good_feedback_tags'):
                    try:
                        msg_good_feedback['feedback_tags'] = json.loads(row['msg_good_feedback_tags'])
                    except (json.JSONDecodeError, TypeError):
                        msg_good_feedback['feedback_tags'] = []

            # 处理对话级别的负面反馈
            conv_bad_feedback = None
            if row.get('conv_bad_feedback_submitted_at'):
                conv_bad_feedback = {
                    'feedback_tags': [],
                    'custom_feedback': row.get('conv_bad_custom_feedback', ''),
                    'feedback_submitted_at': row.get('conv_bad_feedback_submitted_at')
                }
                # 解析JSON格式的标签
                if row.get('conv_bad_feedback_tags'):
                    try:
                        conv_bad_feedback['feedback_tags'] = json.loads(row['conv_bad_feedback_tags'])
                    except (json.JSONDecodeError, TypeError):
                        conv_bad_feedback['feedback_tags'] = []

            # 处理对话级别的正面反馈
            conv_good_feedback = None
            if row.get('conv_good_feedback_submitted_at'):
                conv_good_feedback = {
                    'feedback_tags': [],
                    'custom_feedback': row.get('conv_good_custom_feedback', ''),
                    'feedback_submitted_at': row.get('conv_good_feedback_submitted_at')
                }
                # 解析JSON格式的标签
                if row.get('conv_good_feedback_tags'):
                    try:
                        conv_good_feedback['feedback_tags'] = json.loads(row['conv_good_feedback_tags'])
                    except (json.JSONDecodeError, TypeError):
                        conv_good_feedback['feedback_tags'] = []

            # 根据规则决定消息的good/bad case状态和反馈
            final_good_feedback = None
            final_bad_feedback = None
            is_good_case = False
            is_bad_case = False

            if msg_good_feedback or msg_bad_feedback:
                # 如果有消息级别的标记，使用消息级别的
                final_good_feedback = msg_good_feedback
                final_bad_feedback = msg_bad_feedback
                is_good_case = msg_good_feedback is not None
                is_bad_case = msg_bad_feedback is not None
            elif conv_good_feedback or conv_bad_feedback:
                # 如果有对话级别的标记且当前消息不是user角色，使用对话级别的
                if row['role'] != 'user':
                    final_good_feedback = conv_good_feedback
                    final_bad_feedback = conv_bad_feedback
                    is_good_case = conv_good_feedback is not None
                    is_bad_case = conv_bad_feedback is not None

            # 添加处理后的反馈信息
            processed_row['badCaseFeedback'] = final_bad_feedback
            processed_row['goodCaseFeedback'] = final_good_feedback
            processed_row['is_bad_case'] = is_bad_case  # 根据规则确定的bad case状态
            processed_row['is_good_case'] = is_good_case  # 根据规则确定的good case状态
            processed_row['isBadCase'] = is_bad_case  # 保持向后兼容
            processed_row['isGoodCase'] = is_good_case  # 保持向后兼容

            # 移除原始的反馈字段以保持数据清洁
            for key in ['msg_bad_feedback_tags', 'msg_bad_custom_feedback', 'msg_bad_feedback_submitted_at',
                       'msg_good_feedback_tags', 'msg_good_custom_feedback', 'msg_good_feedback_submitted_at',
                       'conv_bad_feedback_tags', 'conv_bad_custom_feedback', 'conv_bad_feedback_submitted_at',
                       'conv_good_feedback_tags', 'conv_good_custom_feedback', 'conv_good_feedback_submitted_at']:
                processed_row.pop(key, None)

            processed_results.append(processed_row)

        return processed_results
    except Error as e:
        logger.exception(f"加载对话 {conversation_id} 时发生数据库错误: {e}", exc_info=True)
        return []
    except Exception as e:
        logger.exception(f"加载对话 {conversation_id} 时发生意外错误: {e}", exc_info=True)
        return []

def delete_conversation(username: str, email: str, conversation_id: str) -> bool:
    """
    Delete all messages in a specific conversation from MySQL.

    Args:
        username (str): The username of the conversation owner
        email (str): The email of the conversation owner
        conversation_id (str): The ID of the conversation to delete

    Returns:
        bool: True if the operation was successful, False otherwise
    """
    if not username or not email or not conversation_id:
        logger.warning("Username, email, and conversation_id are required to delete a conversation")
        return False

    sql = """
        DELETE FROM chat_history
        WHERE username = %s AND email = %s AND conversation_id = %s
    """
    values = (username, email, conversation_id)

    try:
        affected_rows = execute_db_query(sql, values, fetch='count', commit=True)
        logger.info(f"Deleted conversation {conversation_id} for user {username} ({email}), {affected_rows} messages removed")
        return True
    except Error as e:
        # Error already logged
        return False
    except Exception as e:
        logger.exception(f"Unexpected error deleting conversation {conversation_id} for user {username} ({email}): {e}", exc_info=True)
        return False

def get_history_conversation_count(username: str, email: str) -> int:
    """
    Get the count of unique conversations for a specific user from MySQL.

    Args:
        username (str): The username to get count for
        email (str): The email to get count for

    Returns:
        int: The count of unique conversations
    """
    if not username or not email:
        logger.warning("Username and email are required to get conversation count")
        return 0

    sql = """
        SELECT COUNT(DISTINCT conversation_id) as count
        FROM chat_history
        WHERE username = %s AND email = %s
    """
    values = (username, email)

    try:
        result = execute_db_query(sql, values, fetch='one')
        return result.get('count', 0) if result else 0
    except Error as e:
        # Error already logged
        return 0
    except Exception as e:
        logger.exception(f"Unexpected error getting conversation count for user {username} ({email}): {e}", exc_info=True)
        return 0


def check_conversation_owner(conversation_id: str) -> Optional[Dict[str, str]]:
    """
    检查对话的所有者信息

    Args:
        conversation_id (str): 对话ID

    Returns:
        Optional[Dict[str, str]]: 包含 username 和 email 的字典，如果对话不存在则返回 None
    """
    if not conversation_id:
        logger.warning("对话ID不能为空")
        return None

    sql = """
        SELECT username, email
        FROM chat_history
        WHERE conversation_id = %s
        ORDER BY timestamp ASC
        LIMIT 1
    """
    values = (conversation_id,)

    try:
        result = execute_db_query(sql, values, fetch='one')
        if not result:
            logger.warning(f"对话 {conversation_id} 不存在")
            return None

        return {
            'username': result.get('username'),
            'email': result.get('email')
        }
    except Error as e:
        # Error already logged
        return None
    except Exception as e:
        logger.exception(f"检查对话所有者时发生意外错误: {e}", exc_info=True)
        return None


def get_user_latest_queries_from_db(user_email: str, limit: int = 10) -> List[str]:
    """
    获取指定用户最近的查询消息内容列表，优先获取good case和已修复的bad case，
    按 email 和 conversation_id 分组，将 content 使用 GROUP_CONCAT 聚合，并返回前 limit 条。

    Args:
        user_email (str): 用户邮箱
        limit (int): 最大返回数量，默认10

    Returns:
        List[str]: 聚合后的用户查询消息内容列表
    """
    if not user_email:
        logger.warning("用户邮箱不能为空")
        return []

    sql = """
        SELECT GROUP_CONCAT(ch.content ORDER BY ch.timestamp ASC SEPARATOR ' ') AS aggregated_content,
               CASE
                   WHEN gc.conversation_id IS NOT NULL THEN 1  -- good case 优先级最高
                   WHEN bc.conversation_id IS NOT NULL AND bc.repair_status = 1 THEN 2  -- 已修复的 bad case 次之
                   ELSE 3  -- 其他情况优先级最低
               END as priority_level
        FROM chat_history ch
        LEFT JOIN good_case gc ON ch.conversation_id = gc.conversation_id
        LEFT JOIN bad_case bc ON ch.conversation_id = bc.conversation_id
        WHERE ch.email = %s AND ch.role = 'user' AND ch.content IS NOT NULL AND ch.content != ''
        GROUP BY ch.email, ch.conversation_id, gc.conversation_id, bc.conversation_id, bc.repair_status
        ORDER BY priority_level ASC, MAX(ch.timestamp) DESC
        LIMIT %s
    """
    values = (user_email, limit)

    try:
        results = execute_db_query(sql, values, fetch='all')
        if not results:
            logger.info(f"用户 {user_email} 没有找到任何查询记录")
            return []

        # 提取聚合后的消息内容
        queries = [row['aggregated_content'] for row in results if row['aggregated_content']]
        logger.info(f"为用户 {user_email} 获取到 {len(queries)} 条聚合查询记录（优先good case和已修复bad case）")
        return queries

    except Error as e:
        logger.exception(f"获取用户 {user_email} 查询记录时发生数据库错误: {e}", exc_info=True)
        return []
    except Exception as e:
        logger.exception(f"获取用户 {user_email} 查询记录时发生意外错误: {e}", exc_info=True)
        return []


def get_other_users_latest_queries_from_db(current_user_email: str, limit: int = 10) -> List[str]:
    """
    获取除当前用户外其他用户最近的查询消息内容列表，优先获取good case和已修复的bad case，
    按 email 和 conversation_id 分组，将 content 使用 GROUP_CONCAT 聚合，并返回前 limit 条。

    Args:
        current_user_email (str): 当前用户邮箱（将被排除）
        limit (int): 最大返回数量，默认10

    Returns:
        List[str]: 聚合后的其他用户查询消息内容列表
    """
    if not current_user_email:
        logger.warning("当前用户邮箱不能为空")
        return []

    sql = """
        SELECT GROUP_CONCAT(ch.content ORDER BY ch.timestamp ASC SEPARATOR ' ') AS aggregated_content,
               CASE
                   WHEN gc.conversation_id IS NOT NULL THEN 1  -- good case 优先级最高
                   WHEN bc.conversation_id IS NOT NULL AND bc.repair_status = 1 THEN 2  -- 已修复的 bad case 次之
                   ELSE 3  -- 其他情况优先级最低
               END as priority_level
        FROM chat_history ch
        LEFT JOIN good_case gc ON ch.conversation_id = gc.conversation_id
        LEFT JOIN bad_case bc ON ch.conversation_id = bc.conversation_id
        WHERE ch.email != %s AND ch.role = 'user' AND ch.content IS NOT NULL AND ch.content != ''
        GROUP BY ch.email, ch.conversation_id, gc.conversation_id, bc.conversation_id, bc.repair_status
        ORDER BY priority_level ASC, MAX(ch.timestamp) DESC
        LIMIT %s
    """
    values = (current_user_email, limit)

    try:
        results = execute_db_query(sql, values, fetch='all')
        if not results:
            logger.info(f"除用户 {current_user_email} 外没有找到其他用户的查询记录")
            return []

        # 提取聚合后的消息内容
        queries = [row['aggregated_content'] for row in results if row['aggregated_content']]
        logger.info(f"为用户 {current_user_email} 获取到 {len(queries)} 条其他用户聚合查询记录（优先good case和已修复bad case）")
        return queries

    except Error as e:
        logger.exception(f"获取其他用户查询记录时发生数据库错误（当前用户: {current_user_email}): {e}", exc_info=True)
        return []
    except Exception as e:
        logger.exception(f"获取其他用户查询记录时发生意外错误（当前用户: {current_user_email}): {e}", exc_info=True)
        return []


def create_streaming_assistant_message(username: str, email: str, conversation_id: str, 
                                     timestamp: Optional[int] = None, agent: Optional[str] = None) -> Optional[int]:
    """
    创建一条流式assistant消息记录，初始内容为空，is_in_process=1
    
    Args:
        username (str): 用户名
        email (str): 用户邮箱
        conversation_id (str): 对话ID
        timestamp (int, optional): 时间戳，如果为None则使用当前时间
        agent (str, optional): agent名称，如果为None则使用默认值
        
    Returns:
        Optional[int]: 创建的消息记录ID，失败时返回None
    """
    if not username or not email or not conversation_id:
        logger.exception("创建流式assistant消息需要用户名、邮箱和对话ID")
        return None
        
    # 如果没有提供时间戳，使用当前时间
    current_timestamp = timestamp if timestamp is not None else int(datetime.now().timestamp() * 1000)
    
    # 如果没有提供agent名称，使用默认值
    agent_name = agent if agent is not None else 'master_controller_bot'
    
    sql = """
        INSERT INTO chat_history (username, email, conversation_id, role, content, timestamp, agent, is_in_process)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
    """
    values = (username, email, conversation_id, 'assistant', '', current_timestamp, agent_name, 1)
    
    try:
        result = execute_db_query(sql, values, commit=True, fetch='lastrowid')
        message_id = result if result else None
        if message_id:
            logger.debug(f"创建流式assistant消息记录成功，ID: {message_id} (Convo ID: {conversation_id})")
        return message_id
    except Error as e:
        logger.exception(f"创建流式assistant消息记录失败: {e}", exc_info=True)
        return None
    except Exception as e:
        logger.exception(f"创建流式assistant消息记录时发生意外错误: {e}", exc_info=True)
        return None


def update_streaming_assistant_message(message_id: int, content: str, logs: Optional[str] = None,
                                     agent: Optional[str] = None, output_as_input: Optional[str] = None,
                                     time_spend: Optional[int] = None, uploaded_feishu_docs: Optional[str] = None,
                                     is_completed: bool = False) -> bool:
    if not message_id:
        logger.exception("更新流式assistant消息需要消息ID")
        return False

    if content is None:
        logger.exception("更新流式assistant消息的内容不能为None")
        return False

    try:
        check_done_sql = "SELECT is_in_process FROM chat_history WHERE id = %s AND role = 'assistant'"
        done_row = execute_db_query(check_done_sql, (message_id,), fetch='one')
        if done_row is not None:
            current_in_process = done_row.get('is_in_process')
            if current_in_process == 0 and not is_completed:
                logger.info(f"消息 {message_id} 已完成，忽略后续未完成状态的更新（尊重取消/完成）")
                return False
    except Exception as e:
        logger.warning(f"检查消息完成状态失败（不影响主流程）: {e}")

    time_to_first_token = None
    if content.strip():
        try:
            check_sql = """
                SELECT content, created_at, time_to_first_token 
                FROM chat_history 
                WHERE id = %s AND role = 'assistant'
            """
            result = execute_db_query(check_sql, (message_id,), fetch='one')
            if result:
                current_content = result.get('content', '')
                created_at = result.get('created_at')
                existing_time_to_first_token = result.get('time_to_first_token')
                if (not current_content or current_content.strip() == '') and (existing_time_to_first_token is None or existing_time_to_first_token == 0):
                    if created_at:
                        current_time = datetime.now()
                        time_diff = (current_time - created_at).total_seconds()
                        time_to_first_token = int(time_diff)
                        logger.debug(f"首次更新assistant消息 (ID: {message_id})，time_to_first_token: {time_to_first_token}秒")
        except Exception as e:
            logger.warning(f"计算time_to_first_token时出错: {e}")

    update_fields = ["content = %s"]
    values = [content]

    if logs is not None:
        update_fields.append("logs = %s")
        values.append(logs)

    if agent is not None:
        update_fields.append("agent = %s")
        values.append(agent)

    if output_as_input is not None:
        update_fields.append("output_as_input = %s")
        values.append(output_as_input)

    if time_spend is not None:
        update_fields.append("time_spend = %s")
        values.append(time_spend)

    if uploaded_feishu_docs is not None:
        update_fields.append("uploaded_feishu_docs = %s")
        values.append(uploaded_feishu_docs)

    if time_to_first_token is not None:
        update_fields.append("time_to_first_token = %s")
        values.append(time_to_first_token)

    is_in_process = 0 if is_completed else 1
    update_fields.append("is_in_process = %s")
    values.append(is_in_process)

    update_fields.append("updated_at = CURRENT_TIMESTAMP")

    sql = f"""
        UPDATE chat_history
        SET {', '.join(update_fields)}
        WHERE id = %s AND role = 'assistant'
    """
    values.append(message_id)

    try:
        affected_rows = execute_db_query(sql, tuple(values), commit=True, fetch='count')
        if affected_rows > 0:
            status = "完成" if is_completed else "更新"
            logger.debug(f"流式assistant消息{status}成功，ID: {message_id}，内容长度: {len(content)}")
            return True
        else:
            logger.warning(f"未找到要更新的assistant消息记录，ID: {message_id}")
            return False
    except Error as e:
        logger.exception(f"更新流式assistant消息失败，ID: {message_id}: {e}", exc_info=True)
        return False
    except Exception as e:
        logger.exception(f"更新流式assistant消息时发生意外错误，ID: {message_id}: {e}", exc_info=True)
        return False


def get_streaming_assistant_message_id(username: str, email: str, conversation_id: str) -> Optional[int]:
    """
    获取指定对话中最新的正在处理中的assistant消息ID
    
    Args:
        username (str): 用户名
        email (str): 用户邮箱
        conversation_id (str): 对话ID
        
    Returns:
        Optional[int]: 消息ID，如果没有找到则返回None
    """
    if not username or not email or not conversation_id:
        logger.exception("获取流式assistant消息ID需要用户名、邮箱和对话ID")
        return None
    
    sql = """
        SELECT id FROM chat_history 
        WHERE username = %s AND email = %s AND conversation_id = %s 
        AND role = 'assistant' AND is_in_process = 1
        ORDER BY timestamp DESC 
        LIMIT 1
    """
    values = (username, email, conversation_id)
    
    try:
        result = execute_db_query(sql, values, fetch='one')
        if result:
            message_id = result.get('id')
            logger.debug(f"找到正在处理中的assistant消息ID: {message_id} (Convo ID: {conversation_id})")
            return message_id
        else:
            logger.debug(f"未找到正在处理中的assistant消息 (Convo ID: {conversation_id})")
            return None
    except Error as e:
        logger.exception(f"获取流式assistant消息ID失败: {e}", exc_info=True)
        return None
    except Exception as e:
        logger.exception(f"获取流式assistant消息ID时发生意外错误: {e}", exc_info=True)
        return None


def get_chat_history_by_id(chat_history_id: int) -> Optional[dict]:
    """
    根据chat_history_id获取单条聊天记录

    Args:
        chat_history_id (int): chat history记录的ID

    Returns:
        Optional[dict]: 聊天记录信息，如果不存在则返回None
    """
    if not chat_history_id:
        logger.warning("Chat history ID is required")
        return None

    try:
        sql = """
            SELECT id, conversation_id, role, content, timestamp, username, email,
                   logs, agent, time_spend, is_in_process, updated_at
            FROM chat_history
            WHERE id = %s
            LIMIT 1
        """
        result = execute_db_query(sql, (chat_history_id,), fetch='one')

        if result:
            logger.debug(f"Found chat history {chat_history_id}")
            return result
        else:
            logger.warning(f"Chat history {chat_history_id} not found")
            return None

    except Exception as e:
        logger.exception(f"Error getting chat history {chat_history_id}: {str(e)}", exc_info=True)
        return None


def get_previous_user_message_by_chat_history_id(chat_history_id: int) -> Optional[dict]:
    """
    获取指定chat_history_id之前的最近一条用户消息

    Args:
        chat_history_id (int): 当前chat history记录的ID

    Returns:
        Optional[dict]: 前一条用户消息，如果不存在则返回None
    """
    if not chat_history_id:
        logger.warning("Chat history ID is required")
        return None

    try:
        # 首先获取当前记录的conversation_id和timestamp
        current_sql = """
            SELECT conversation_id, timestamp
            FROM chat_history
            WHERE id = %s
        """
        current_result = execute_db_query(current_sql, (chat_history_id,), fetch='one')

        if not current_result:
            logger.warning(f"Chat history {chat_history_id} not found")
            return None

        conversation_id = current_result['conversation_id']
        current_timestamp = current_result['timestamp']

        # 查找同一对话中，时间戳小于当前记录的最近一条用户消息
        sql = """
            SELECT id, conversation_id, role, content, timestamp, username, email
            FROM chat_history
            WHERE conversation_id = %s
                AND role = 'user'
                AND timestamp < %s
            ORDER BY timestamp DESC, id DESC
            LIMIT 1
        """
        result = execute_db_query(sql, (conversation_id, current_timestamp), fetch='one')

        if result:
            logger.debug(f"Found previous user message for chat history {chat_history_id}")
            return result
        else:
            logger.debug(f"No previous user message found for chat history {chat_history_id}")
            return None

    except Exception as e:
        logger.exception(f"Error getting previous user message for chat history {chat_history_id}: {str(e)}", exc_info=True)
        return None


def get_next_assistant_message_by_chat_history_id(chat_history_id: int) -> Optional[dict]:
    """
    获取指定chat_history_id之后的最近一条assistant消息

    Args:
        chat_history_id (int): 当前chat history记录的ID

    Returns:
        Optional[dict]: 后一条assistant消息，如果不存在则返回None
    """
    if not chat_history_id:
        logger.warning("Chat history ID is required")
        return None

    try:
        # 首先获取当前记录的conversation_id和timestamp
        current_sql = """
            SELECT conversation_id, timestamp
            FROM chat_history
            WHERE id = %s
        """
        current_result = execute_db_query(current_sql, (chat_history_id,), fetch='one')

        if not current_result:
            logger.warning(f"Chat history {chat_history_id} not found")
            return None

        conversation_id = current_result['conversation_id']
        current_timestamp = current_result['timestamp']

        # 查找同一对话中，时间戳大于当前记录的最近一条assistant消息
        sql = """
            SELECT id, conversation_id, role, content, timestamp, username, email
            FROM chat_history
            WHERE conversation_id = %s
                AND role = 'assistant'
                AND timestamp > %s
            ORDER BY timestamp ASC, id ASC
            LIMIT 1
        """
        result = execute_db_query(sql, (conversation_id, current_timestamp), fetch='one')

        if result:
            logger.debug(f"Found next assistant message for chat history {chat_history_id}")
            return result
        else:
            logger.debug(f"No next assistant message found for chat history {chat_history_id}")
            return None

    except Exception as e:
        logger.exception(f"Error getting next assistant message for chat history {chat_history_id}: {str(e)}", exc_info=True)
        return None


def check_chat_history_ownership(username: str, email: str, chat_history_id: int) -> Tuple[bool, bool]:
    """
    检查用户是否拥有指定的chat_history记录

    Args:
        username (str): 用户名
        email (str): 用户邮箱
        chat_history_id (int): chat history记录的ID

    Returns:
        tuple[bool, bool]: (是否拥有, 记录是否存在)
    """
    if not username or not email or not chat_history_id:
        logger.warning("Username, email and chat_history_id are required")
        return False, False

    try:
        sql = """
            SELECT COUNT(*) as count
            FROM chat_history
            WHERE id = %s AND username = %s AND email = %s
        """
        result = execute_db_query(sql, (chat_history_id, username, email), fetch='one')

        if result and result['count'] > 0:
            logger.debug(f"User {username} owns chat history {chat_history_id}")
            return True, True
        else:
            # 检查记录是否存在（但不属于该用户）
            check_sql = "SELECT COUNT(*) as count FROM chat_history WHERE id = %s"
            check_result = execute_db_query(check_sql, (chat_history_id,), fetch='one')
            exists = check_result and check_result['count'] > 0

            logger.debug(f"User {username} does not own chat history {chat_history_id}, exists: {exists}")
            return False, exists

    except Exception as e:
        logger.exception(f"Error checking chat history ownership for {chat_history_id}: {str(e)}", exc_info=True)
        return False, False


def get_department_chat_history_for_week(department_name: str, week_start_date: str, week_end_date: str) -> List[Dict[str, Any]]:
    """
    获取指定部门在指定周的所有聊天记录（用于LLM分析）

    Args:
        department_name: 部门名称
        week_start_date: 周开始日期 (YYYY-MM-DD格式)
        week_end_date: 周结束日期 (YYYY-MM-DD格式)

    Returns:
        List[Dict[str, Any]]: 聊天记录列表，包含用户问题和AI回复
    """
    if not department_name or not week_start_date or not week_end_date:
        logger.exception("获取部门聊天记录需要部门名称和时间范围")
        return []

    try:
        # 将日期转换为时间戳（毫秒）
        from datetime import datetime
        start_timestamp = int(datetime.strptime(week_start_date, '%Y-%m-%d').timestamp() * 1000)
        end_timestamp = int(datetime.strptime(f"{week_end_date} 23:59:59", '%Y-%m-%d %H:%M:%S').timestamp() * 1000)

        sql = """
            SELECT ch.id, ch.conversation_id, ch.role, ch.content, ch.timestamp,
                   ch.username, ch.email, ch.agent, u.first_level_department
            FROM chat_history ch
            LEFT JOIN users u ON ch.email = u.email
            WHERE u.first_level_department = %s
                AND ch.timestamp >= %s
                AND ch.timestamp <= %s
                AND ch.role IN ('user')
                AND ch.content IS NOT NULL
                AND ch.content != ''
            ORDER BY ch.conversation_id, ch.timestamp ASC
        """

        results = execute_db_query(sql, (department_name, start_timestamp, end_timestamp), fetch='all')

        if results:
            logger.info(f"获取到部门 {department_name} 在 {week_start_date} 至 {week_end_date} 的 {len(results)} 条聊天记录")
            return results
        else:
            logger.info(f"部门 {department_name} 在 {week_start_date} 至 {week_end_date} 没有聊天记录")
            return []

    except Exception as e:
        logger.exception(f"获取部门聊天记录时发生错误: {e}", exc_info=True)
        return []


def get_department_conversation_stats_for_week(department_name: str, week_start_date: str, week_end_date: str) -> Dict[str, int]:
    """
    获取指定部门在指定周的对话统计信息

    Args:
        department_name: 部门名称
        week_start_date: 周开始日期 (YYYY-MM-DD格式)
        week_end_date: 周结束日期 (YYYY-MM-DD格式)

    Returns:
        Dict[str, int]: 包含总对话数和总用户查询数的统计信息
    """
    if not department_name or not week_start_date or not week_end_date:
        logger.exception("获取部门对话统计需要部门名称和时间范围")
        return {"total_conversations": 0, "total_user_queries": 0}

    try:
        # 将日期转换为时间戳（毫秒）
        from datetime import datetime
        start_timestamp = int(datetime.strptime(week_start_date, '%Y-%m-%d').timestamp() * 1000)
        end_timestamp = int(datetime.strptime(f"{week_end_date} 23:59:59", '%Y-%m-%d %H:%M:%S').timestamp() * 1000)

        sql = """
            SELECT
                COUNT(DISTINCT ch.conversation_id) as total_conversations,
                COUNT(CASE WHEN ch.role = 'user' THEN 1 END) as total_user_queries
            FROM chat_history ch
            LEFT JOIN users u ON ch.email = u.email
            WHERE u.first_level_department = %s
                AND ch.timestamp >= %s
                AND ch.timestamp <= %s
        """

        result = execute_db_query(sql, (department_name, start_timestamp, end_timestamp), fetch='one')

        if result:
            stats = {
                "total_conversations": result.get('total_conversations', 0),
                "total_user_queries": result.get('total_user_queries', 0)
            }
            logger.info(f"部门 {department_name} 在 {week_start_date} 至 {week_end_date} 的统计: {stats}")
            return stats
        else:
            return {"total_conversations": 0, "total_user_queries": 0}

    except Exception as e:
        logger.exception(f"获取部门对话统计时发生错误: {e}", exc_info=True)
        return {"total_conversations": 0, "total_user_queries": 0}
