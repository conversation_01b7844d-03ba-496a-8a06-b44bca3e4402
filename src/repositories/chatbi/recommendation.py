"""
推荐仓储实现

负责推荐数据的持久化操作，遵循DDD架构模式
"""

from abc import ABC, abstractmethod
from typing import Optional, List
from datetime import datetime, timedelta
import json

from src.db.connection import execute_db_query, get_db_connection, Database
from src.models.recommendation import (
    UserRecommendation, 
    RecommendationStatus, 
    ActiveUser,
    RecommendationQuery
)
from src.utils.logger import logger


class RecommendationRepository(ABC):
    """推荐仓储接口"""
    
    @abstractmethod
    def find_by_user_email(self, user_email: str) -> Optional[UserRecommendation]:
        """根据用户邮箱查找有效推荐"""
        pass
    
    @abstractmethod
    def save(self, recommendation: UserRecommendation) -> UserRecommendation:
        """保存推荐"""
        pass
    
    @abstractmethod
    def update_status(self, user_email: str, user_open_id: str, 
                     status: RecommendationStatus, error_message: Optional[str] = None) -> bool:
        """更新推荐状态"""
        pass
    
    @abstractmethod
    def find_active_users(self, days: int = 30) -> List[ActiveUser]:
        """查找活跃用户"""
        pass
    
    @abstractmethod
    def find_user_queries(self, user_email: str, limit: int = 15) -> List[RecommendationQuery]:
        """查找用户历史查询"""
        pass
    
    @abstractmethod
    def find_other_users_queries(self, current_user_email: str, limit: int = 25) -> List[RecommendationQuery]:
        """查找其他用户的查询"""
        pass


class ChatbiRecommendationRepository(RecommendationRepository):
    """ChatBI数据库推荐仓储实现"""
    
    def find_by_user_email(self, user_email: str) -> Optional[UserRecommendation]:
        """根据用户邮箱查找有效推荐"""
        if not user_email or not user_email.strip():
            logger.warning("用户邮箱不能为空")
            return None
        
        sql = """
        SELECT id, user_email, user_open_id, recommendations, generated_at, expires_at,
               generation_status, error_message, created_at, updated_at
        FROM user_recommendation_offline
        WHERE user_email = %s 
        AND expires_at > NOW()
        AND generation_status = %s
        ORDER BY generated_at DESC
        LIMIT 1
        """
        
        try:
            result = execute_db_query(
                sql, 
                (user_email.strip(), RecommendationStatus.COMPLETED.value), 
                fetch='one',
                database=Database.CHATBI
            )
            
            if result:
                return self._build_recommendation_from_result(result)
            
            return None
            
        except Exception as e:
            logger.exception(f"查找用户推荐失败: {user_email}, 错误: {e}", exc_info=True)
            return None
    
    def save(self, recommendation: UserRecommendation) -> UserRecommendation:
        """保存推荐"""
        sql = """
        INSERT INTO user_recommendation_offline 
        (user_email, user_open_id, recommendations, generated_at, expires_at, generation_status, error_message)
        VALUES (%s, %s, %s, %s, %s, %s, %s)
        ON DUPLICATE KEY UPDATE 
        recommendations = VALUES(recommendations),
        generated_at = VALUES(generated_at),
        expires_at = VALUES(expires_at),
        generation_status = VALUES(generation_status),
        error_message = VALUES(error_message),
        updated_at = NOW()
        """
        
        conn = None
        cursor = None
        try:
            conn = get_db_connection(Database.CHATBI)
            cursor = conn.cursor()
            
            cursor.execute(sql, (
                recommendation.user_email,
                recommendation.user_open_id,
                recommendation.get_recommendations_json(),
                recommendation.generated_at,
                recommendation.expires_at,
                recommendation.status.value,
                recommendation.error_message
            ))
            
            conn.commit()
            
            # 获取插入的ID（如果是新记录）
            if cursor.lastrowid and not recommendation.id:
                recommendation.id = cursor.lastrowid
            
            logger.info(f"成功保存用户推荐: {recommendation.user_email}, 状态: {recommendation.status.name}")
            return recommendation
            
        except Exception as e:
            logger.exception(f"保存推荐失败: {recommendation.user_email}, 错误: {e}", exc_info=True)
            if conn and conn.is_connected():
                try:
                    conn.rollback()
                except Exception as rollback_error:
                    logger.exception(f"回滚事务失败: {rollback_error}")
            raise
        finally:
            self._close_resources(cursor, conn)
    
    def update_status(self, user_email: str, user_open_id: str, 
                     status: RecommendationStatus, error_message: Optional[str] = None) -> bool:
        """更新推荐状态"""
        sql = """
        INSERT INTO user_recommendation_offline 
        (user_email, user_open_id, recommendations, generated_at, expires_at, generation_status, error_message)
        VALUES (%s, %s, '[]', NOW(), DATE_ADD(NOW(), INTERVAL 24 HOUR), %s, %s)
        ON DUPLICATE KEY UPDATE 
        generation_status = VALUES(generation_status),
        error_message = VALUES(error_message),
        updated_at = NOW()
        """
        
        conn = None
        cursor = None
        try:
            conn = get_db_connection(Database.CHATBI)
            cursor = conn.cursor()
            
            cursor.execute(sql, (user_email, user_open_id, status.value, error_message))
            conn.commit()
            
            logger.info(f"成功更新推荐状态: {user_email}, 状态: {status.name}")
            return True
            
        except Exception as e:
            logger.exception(f"更新推荐状态失败: {user_email}, 错误: {e}", exc_info=True)
            if conn and conn.is_connected():
                try:
                    conn.rollback()
                except Exception as rollback_error:
                    logger.exception(f"回滚事务失败: {rollback_error}")
            return False
        finally:
            self._close_resources(cursor, conn)
    
    def find_active_users(self, days: int = 30) -> List[ActiveUser]:
        """查找活跃用户"""
        sql = """
        SELECT DISTINCT u.email, u.open_id, u.name, MAX(ch.timestamp) as last_activity
        FROM users u
        INNER JOIN chat_history ch ON u.email = ch.email
        WHERE ch.timestamp >= UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL %s DAY)) * 1000
        GROUP BY u.email, u.open_id, u.name
        ORDER BY last_activity DESC
        """
        
        try:
            results = execute_db_query(
                sql, 
                (days,), 
                fetch='all',
                database=Database.CHATBI
            )
            
            active_users = []
            for result in results:
                if result.get('email'):  # 确保邮箱不为空
                    try:
                        # 转换时间戳为datetime
                        last_activity = None
                        if result.get('last_activity'):
                            last_activity = datetime.fromtimestamp(result['last_activity'] / 1000)
                        
                        active_user = ActiveUser(
                            email=result['email'],
                            open_id=result['open_id'],
                            name=result['name'],
                            last_activity_time=last_activity
                        )
                        active_users.append(active_user)
                    except Exception as user_error:
                        logger.warning(f"构建活跃用户对象失败: {result}, 错误: {user_error}")
                        continue
            
            logger.info(f"获取到 {len(active_users)} 个最近 {days} 天活跃的用户")
            return active_users
            
        except Exception as e:
            logger.exception(f"查找活跃用户失败: {e}", exc_info=True)
            return []
    
    def find_user_queries(self, user_email: str, limit: int = 15) -> List[RecommendationQuery]:
        """查找用户历史查询"""
        sql = """
        SELECT GROUP_CONCAT(ch.content ORDER BY ch.timestamp ASC SEPARATOR ' ') AS aggregated_content,
               CASE
                   WHEN gc.conversation_id IS NOT NULL THEN 1  -- good case 优先级最高
                   WHEN bc.conversation_id IS NOT NULL AND bc.repair_status = 1 THEN 2  -- 已修复的 bad case 次之
                   ELSE 3  -- 其他情况优先级最低
               END as priority_level
        FROM chat_history ch
        LEFT JOIN good_case gc ON ch.conversation_id = gc.conversation_id
        LEFT JOIN bad_case bc ON ch.conversation_id = bc.conversation_id
        WHERE ch.email = %s AND ch.role = 'user' AND ch.content IS NOT NULL AND ch.content != ''
        GROUP BY ch.email, ch.conversation_id, gc.conversation_id, bc.conversation_id, bc.repair_status
        ORDER BY priority_level ASC, MAX(ch.timestamp) DESC
        LIMIT %s
        """
        
        try:
            results = execute_db_query(
                sql, 
                (user_email, limit), 
                fetch='all',
                database=Database.CHATBI
            )
            
            queries = []
            for result in results:
                if result.get('aggregated_content'):
                    try:
                        query = RecommendationQuery(
                            content=result['aggregated_content'],
                            priority=result.get('priority_level', 3)
                        )
                        queries.append(query)
                    except Exception as query_error:
                        logger.warning(f"构建推荐查询对象失败: {result}, 错误: {query_error}")
                        continue
            
            logger.info(f"为用户 {user_email} 获取到 {len(queries)} 条历史查询")
            return queries
            
        except Exception as e:
            logger.exception(f"查找用户历史查询失败: {user_email}, 错误: {e}", exc_info=True)
            return []
    
    def find_other_users_queries(self, current_user_email: str, limit: int = 25) -> List[RecommendationQuery]:
        """查找其他用户的查询"""
        sql = """
        SELECT GROUP_CONCAT(ch.content ORDER BY ch.timestamp ASC SEPARATOR ' ') AS aggregated_content,
               CASE
                   WHEN gc.conversation_id IS NOT NULL THEN 1  -- good case 优先级最高
                   WHEN bc.conversation_id IS NOT NULL AND bc.repair_status = 1 THEN 2  -- 已修复的 bad case 次之
                   ELSE 3  -- 其他情况优先级最低
               END as priority_level
        FROM chat_history ch
        LEFT JOIN good_case gc ON ch.conversation_id = gc.conversation_id
        LEFT JOIN bad_case bc ON ch.conversation_id = bc.conversation_id
        WHERE ch.email != %s AND ch.role = 'user' AND ch.content IS NOT NULL AND ch.content != ''
        GROUP BY ch.email, ch.conversation_id, gc.conversation_id, bc.conversation_id, bc.repair_status
        ORDER BY priority_level ASC, MAX(ch.timestamp) DESC
        LIMIT %s
        """
        
        try:
            results = execute_db_query(
                sql, 
                (current_user_email, limit), 
                fetch='all',
                database=Database.CHATBI
            )
            
            queries = []
            for result in results:
                if result.get('aggregated_content'):
                    try:
                        query = RecommendationQuery(
                            content=result['aggregated_content'],
                            priority=result.get('priority_level', 3)
                        )
                        queries.append(query)
                    except Exception as query_error:
                        logger.warning(f"构建推荐查询对象失败: {result}, 错误: {query_error}")
                        continue
            
            logger.info(f"为用户 {current_user_email} 获取到 {len(queries)} 条其他用户查询")
            return queries
            
        except Exception as e:
            logger.exception(f"查找其他用户查询失败: {current_user_email}, 错误: {e}", exc_info=True)
            return []
    
    def _build_recommendation_from_result(self, result: dict) -> UserRecommendation:
        """从数据库结果构建推荐实体"""
        try:
            raw = result['recommendations']
            parsed = json.loads(raw) if raw else []
        except json.JSONDecodeError:
            logger.exception(f"解析推荐JSON失败: {result['recommendations']}")
            parsed = []
        
        # 兼容旧数据结构：可能为dict包含summary/analysis，或为字符串/列表
        recommendations = []
        try:
            if isinstance(parsed, list):
                recommendations = parsed
            elif isinstance(parsed, dict):
                if 'summary' in parsed and isinstance(parsed['summary'], list):
                    recommendations = parsed['summary']
                elif 'recommendations' in parsed and isinstance(parsed['recommendations'], list):
                    recommendations = parsed['recommendations']
                else:
                    recommendations = []
            elif isinstance(parsed, str):
                if parsed.strip():
                    recommendations = [parsed.strip()]
        except Exception as adapt_err:
            logger.warning(f"适配推荐数据时出错，回退为空列表: {adapt_err}")
            recommendations = []
        
        return UserRecommendation(
            id=result['id'],
            user_email=result['user_email'],
            user_open_id=result['user_open_id'],
            recommendations=recommendations,
            generated_at=result['generated_at'],
            expires_at=result['expires_at'],
            status=RecommendationStatus(result['generation_status']),
            error_message=result.get('error_message'),
            created_at=result.get('created_at'),
            updated_at=result.get('updated_at')
        )
    
    def _close_resources(self, cursor, conn):
        """关闭数据库资源"""
        if cursor:
            try:
                cursor.close()
            except Exception as cursor_error:
                logger.exception(f"关闭游标失败: {cursor_error}")
        
        if conn and conn.is_connected():
            try:
                conn.close()
            except Exception as conn_error:
                logger.exception(f"关闭连接失败: {conn_error}")
