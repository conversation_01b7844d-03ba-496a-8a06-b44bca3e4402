#!/usr/bin/env python3
"""
测试专业agent直接调用功能
验证当直接指定agent时，消息构建和处理是否正确
"""

import sys
import os
import time
import argparse
import uuid
from datetime import datetime
from typing import Dict, Any, Optional

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.services.agent.api_query_processor import APIQueryProcessor
from src.services.user_query_service import UserQueryService
from src.utils.logger import logger

def get_user_info_by_username(username: str) -> Optional[Dict[str, Any]]:
    """
    根据用户名获取用户信息和access_token
    
    Args:
        username: 用户名
        
    Returns:
        Dict包含用户信息及access_token，未找到返回None
    """
    try:
        user_info = UserQueryService.get_user_info_by_username(username)
        if not user_info:
            logger.warning(f"❌ 未找到用户: {username}")
            return None
            
        # 获取有效access token
        from src.services.auth.user_session_service import user_session_service
        open_id = user_info.get("open_id")
        access_token = user_session_service.get_user_access_token(open_id) if open_id else None
        user_info["access_token"] = access_token
        
        logger.info(f"✅ 成功获取用户信息: {user_info['name']} ({user_info['email']})")
        return user_info
    except Exception as e:
        logger.exception(f"获取用户信息失败: {e}")
        return None

def get_default_user_info() -> Dict[str, Any]:
    """获取默认测试用户信息"""
    return {
        "name": "ChatBI自动化测试",
        "email": f"test_user_{datetime.now().strftime('%Y%m%d')}@summerfarm.net",
        "job_title": "测试工程师",
        "open_id": None,
        "access_token": None
    }

def test_agent_query(query: str, agent: str, user_info: Dict[str, Any]) -> bool:
    """
    测试指定agent的查询处理
    
    Args:
        query: 查询内容
        agent: 指定的agent名称
        user_info: 用户信息
        
    Returns:
        bool: 测试是否成功
    """
    print(f"\n{'='*60}")
    print(f"测试Agent: {agent}")
    print(f"查询内容: {query}")
    print(f"用户: {user_info['name']} ({user_info['email']})")
    print(f"{'='*60}")
    
    # 生成唯一的conversation_id
    conversation_id = str(uuid.uuid4())
    
    # 创建API查询处理器
    processor = APIQueryProcessor()
    
    start_time = time.time()
    response_count = 0
    error_occurred = False
    final_response = ""
    
    try:
        # 执行查询
        for response in processor.run_query(
            user_query=query,
            user_info=user_info,
            access_token=user_info.get("access_token"),
            conversation_id=conversation_id,
            agent=agent
        ):
            response_count += 1
            
            # 解析响应
            if isinstance(response, str) and response.startswith("[data]:"):
                import json
                try:
                    data = json.loads(response[7:])  # 去掉"[data]:"前缀
                    msg_type = data.get("type")
                    content = data.get("content", "")
                    
                    if msg_type == "error":
                        print(f"❌ 错误: {content}")
                        error_occurred = True
                    elif msg_type == "data" and content:
                        final_response += content
                        print(content, end="", flush=True)
                    elif msg_type == "heartbeat":
                        print(".", end="", flush=True)
                        
                except json.JSONDecodeError:
                    pass
                    
    except Exception as e:
        print(f"\n❌ 查询执行失败: {e}")
        error_occurred = True
    
    end_time = time.time()
    duration = end_time - start_time
    
    print(f"\n\n{'='*60}")
    print(f"测试结果:")
    print(f"  耗时: {duration:.2f}秒")
    print(f"  响应数量: {response_count}")
    print(f"  状态: {'❌ 失败' if error_occurred else '✅ 成功'}")
    print(f"  对话ID: {conversation_id}")
    
    if final_response:
        print(f"  响应长度: {len(final_response)}字符")
    
    print(f"{'='*60}")
    
    return not error_occurred

def main():
    """主测试函数"""
    parser = argparse.ArgumentParser(description='测试专业agent直接调用功能')
    parser.add_argument('--user', type=str, default=None,
                       help='指定用户名，根据用户名获取用户信息来执行测试')
    parser.add_argument('--agent', type=str, default='general_chat_bot',
                       help='指定要测试的agent名称，默认为general_chat_bot')
    parser.add_argument('--query', type=str, default='新人有多少年假？',
                       help='指定查询内容，默认为"新人有多少年假？"')
    parser.add_argument('--list-agents', action='store_true',
                       help='列出可用的agent')
    
    args = parser.parse_args()
    
    # 如果请求列出agent，显示并退出
    if args.list_agents:
        print("可用的Agent:")
        print("  - general_chat_bot: 通用聊天机器人，可以回答一般性问题")
        print("  - sales_order_analytics: 销售订单分析专家")
        print("  - sales_kpi_analytics: 销售KPI分析专家")
        print("  - warehouse_and_fulfillment: 仓储物流专家")
        print("\n使用示例:")
        print("  python test_agent_direct.py --agent general_chat_bot --query '新人有多少年假？'")
        print("  python test_agent_direct.py --user 张三 --agent sales_order_analytics --query '昨天的销售额是多少？'")
        return 0
    
    # 获取用户信息
    user_info = None
    if args.user:
        user_info = get_user_info_by_username(args.user)
        if not user_info:
            print(f"❌ 无法获取用户 {args.user} 的信息，使用默认测试用户")
            user_info = get_default_user_info()
    else:
        user_info = get_default_user_info()
    
    # 执行测试
    success = test_agent_query(args.query, args.agent, user_info)
    
    if success:
        print("🎉 测试成功完成！")
        return 0
    else:
        print("⚠️ 测试失败，请检查日志")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
