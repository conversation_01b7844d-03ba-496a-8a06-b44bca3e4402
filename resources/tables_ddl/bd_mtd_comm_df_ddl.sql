CREATE TABLE `xianmu_offline_db`.`bd_mtd_comm_df` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID，自增标识符，唯一标识每条BD绩效记录',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间，数据最后修改的时间戳，用于追踪数据变更',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间，数据首次插入的时间戳',
  `last_bd_name` varchar(255) COMMENT 'BD员工姓名，当前归属的BD销售人员姓名，如：李韬、张三、王五',
  `last_bd_id` bigint COMMENT 'BD员工ID，关联员工表的唯一标识，用于标识具体的BD人员，如：1000768',
  `dep_level3` varchar(255) COMMENT '三级部门名称（大区），BD所属的大区组织架构，如：华中大区、华东大区、华南大区',
  `dep_name` varchar(255) COMMENT '部门名称（区域），BD所属的具体区域部门，如：武汉、上海、深圳',
  `total_score_num` double COMMENT '利润积分总数，BD当月累计获得的利润积分，用于绩效考核，支持高精度计算，如：2489.944917',
  `bd_performance_rate` double COMMENT '绩效系数，BD的利润积分计算系数，影响最终佣金计算，通常为1，如：1.0000',
  `total_comm_amt` decimal(10,2) COMMENT '佣金总金额，BD当月获得的所有佣金总和，单位：元，如：1973.91',
  `a_commisstion_amt` decimal(10,2) COMMENT '高价值客户总佣金，来自A类高价值客户的佣金收入，单位：元，如：1152.50',
  `a_cust_cnt` int COMMENT '高价值客户数量，BD服务的A类高价值客户总数，如：35',
  `a_cust_comm_amt` decimal(10,2) COMMENT '高价值客户单客佣金，平均每个高价值客户贡献的佣金，单位：元，如：875.00',
  `more_than_spu_cnt` bigint COMMENT '超额SPU数量，高价值客户超出基础要求的SPU商品数量，如：185',
  `a_spu_comm_amt` decimal(10,2) COMMENT '超额SPU佣金，因超额SPU获得的额外佣金收入，单位：元，如：277.50',
  `category_comm_amt` decimal(10,2) COMMENT '品类推广佣金总额，通过品类推广活动获得的佣金，单位：元，如：821.41',
  `old_cust_comm` decimal(10,2) COMMENT '存量客户品类佣金，来自现有客户的品类推广佣金，单位：元，如：589.02',
  `new_cust_comm` decimal(10,2) COMMENT '新增客户品类佣金，来自新开发客户的品类推广佣金，单位：元，如：232.39',
  `big_sku_cnt` double COMMENT '大规格商品推广件数，品类推广中大规格商品的销售件数，支持高精度，如：635.554610',
  `old_big_sku_cnt` double COMMENT '存量客户大规格推广件数，现有客户购买的大规格商品件数，如：547.070023',
  `new_big_sku_cnt` double COMMENT '新增客户大规格推广件数，新客户购买的大规格商品件数，如：88.484587',
  `dlv_real_amt` decimal(10,2) COMMENT 'MTD履约实付GMV，月度至今已履约订单的实际支付金额总和，单位：元，如：193732.76',
  `item_profit_amt` decimal(10,2) COMMENT 'MTD履约商品毛利润，月度至今已履约商品的毛利润总额，单位：元，如：30810.09',
  `data_month` varchar(6) GENERATED ALWAYS AS (substr(`ds`,1,6)) STORED COMMENT '数据月份 (yyyyMM)，表示数据统计的月份，如202506',
  `dlv_spu_cnt` bigint COMMENT 'MTD履约SPU数量，月度至今已履约的不同SPU商品种类数，如：360',
  `more_than_spu_cust_cnt` bigint COMMENT '超额SPU客户数，产生超额SPU的高价值客户数量，如：31',
  PRIMARY KEY (`id`),
  KEY `idx_bd_mtd_comm_data_month` (`data_month`) COMMENT '按月份查询的索引，用于时间维度的分析和查询',
  KEY `idx_bd_mtd_comm_last_bd_id_a_cust_cnt` (`last_bd_id`,`a_cust_cnt`) COMMENT '按BD查询高价值客户数量的复合索引，用于客户数量排序和筛选',
  KEY `idx_bd_mtd_comm_last_bd_id_more_than_spu_cnt` (`last_bd_id`,`more_than_spu_cnt`) COMMENT '按BD查询超额SPU数量的复合索引，用于SPU业绩分析',
  KEY `idx_bd_mtd_comm_last_bd_id_a_spu_comm_amt` (`last_bd_id`,`a_spu_comm_amt`) COMMENT '按BD查询超额SPU佣金的复合索引，用于佣金排序和统计',
  KEY `idx_bd_mtd_comm_last_bd_id_dlv_real_amt` (`last_bd_id`,`dlv_real_amt`) COMMENT '按BD查询履约GMV的复合索引，用于业绩排序和GMV分析'
) ENGINE=InnoDB AUTO_INCREMENT=11024 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='BD月度至今绩效汇总表，记录每个BD销售人员的月度累计业绩数据，包括客户数量、佣金收入、品类推广、履约业绩等关键指标，用于BD绩效考核、佣金计算和业务分析。每个BD每天一条记录，支持按时间维度追踪业绩变化趋势。主要业务场景：1)BD绩效排名和考核 2)佣金计算和分配 3)客户价值分析 4)品类推广效果评估 5)GMV和利润分析';

-- ========================================
-- BD月度绩效GMV汇总表，旧版，部分区域还在使用，新版的基于高价值客户的请使用bd_mtd_comm_df
-- 此表可查询BD的拉新客户数、拜访数、鲜果GMV、乳制品GMV、非乳制品GMV、自营品牌GMV（即PB品GMV）等bd_mtd_comm_df不具备的数据。
-- ========================================
CREATE TABLE `xianmu_offline_db`.`crm_bd_month_gmv_all` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID，自增标识符，唯一标识每条BD月度GMV记录',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间，数据最后修改的时间戳，用于追踪数据变更',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间，数据首次插入的时间戳',
  `bd_id` bigint COMMENT 'BD销售员ID，关联crm_bd_org.bd_id，用于标识具体的BD人员，如：1000768',
  `bd_name` varchar(32) COMMENT 'BD销售员姓名，关联crm_bd_org.bd_name，当前归属的BD销售人员姓名，如：李韬、张三、王五',
  `total_gmv` decimal(18,2) COMMENT '总GMV，BD当月所有客户的总成交金额，包含所有类型客户的履约实付金额总和，单位：元',
  `single_gmv` decimal(18,2) COMMENT '单店GMV，BD当月单店客户（非连锁客户）的履约实付金额总和，单位：元',
  `vip_gmv` decimal(18,2) COMMENT '大客户GMV，BD当月大客户（连锁品牌客户）的履约实付金额总和，单位：元',
  `fruit_gmv` decimal(18,2) COMMENT '鲜果GMV，BD当月鲜果类商品（category.type=4）的履约实付金额总和，单位：元',
  `dairy_gmv` decimal(18,2) COMMENT '乳制品GMV，BD当月乳制品类商品（category.type=2）的履约实付金额总和，单位：元',
  `non_dairy_gmv` decimal(18,2) COMMENT '非乳制品GMV，BD当月非乳制品类商品（category.type=3）的履约实付金额总和，单位：元',
  `brand_gmv` decimal(18,2) COMMENT '自营品牌GMV，BD当月自营品牌商品（PB品）的履约实付金额总和，单位：元',
  `reward_gmv` decimal(18,2) COMMENT '固定奖励SKU的GMV，BD当月销售固定奖励商品的履约实付金额总和，用于特殊激励计算，单位：元',
  `reward_amount` bigint COMMENT '固定奖励SKU销量，BD当月销售固定奖励商品的履约件数总和，用于特殊激励计算',
  `category_award` decimal(18,2) COMMENT '品类提成，BD当月通过品类推广活动获得的提成金额，包含存量和新增客户的品类推广收益，单位：元',
  `core_merchant_amount` bigint COMMENT '核心客户数量，BD当月服务的核心客户总数，核心客户通常指高价值或重点维护的客户',
  `core_merchant_card_level` decimal(18,2) COMMENT '核心商户数净增长牌级，BD当月核心商户数量净增长对应的牌级评分，用于绩效评估',
  `month_live_amount` bigint COMMENT '月活客户数量，BD当月有下单行为的活跃客户总数，体现BD的客户维护能力',
  `pull_new_amount` bigint COMMENT '拉新客户数量，BD当月新开发的客户数量，包含通过邀请码注册和上门拜访转化的新客户',
  `performance` decimal(18,2) COMMENT 'BD绩效得分，BD当月的综合绩效评分，基于GMV、客户数、拜访等多维度计算，用于绩效考核',
  `ordinary_num` bigint COMMENT '普通拜访次数，BD当月进行的普通拜访总次数，包含电话、微信、企微等非上门拜访方式',
  `drop_in_visit_num` bigint COMMENT '上门拜访次数，BD当月进行的实地上门拜访总次数，体现BD的客户服务深度',
  `efficient_num` bigint COMMENT '有效拜访次数，BD当月进行的有效拜访总次数，有效拜访通常指能产生实际业务价值的拜访',
  `worth_num` bigint COMMENT '价值拜访次数，BD当月进行的高价值拜访总次数，价值拜访通常指对重点客户或产生重要业务成果的拜访',
  `single_month_live_num` bigint COMMENT '单店月活数量，BD当月单店客户中有下单行为的活跃客户数量',
  `vip_month_live_num` bigint COMMENT '大客户月活数量，BD当月大客户（连锁品牌客户）中有下单行为的活跃客户数量',
  `delivery_gmv` decimal(18,2) COMMENT '配送GMV，BD当月已完成配送的订单履约实付金额总和，单位：元',
  `spu_average` decimal(18,2) COMMENT '配送SPU均值，BD当月配送订单中平均每单的SPU商品种类数，体现客户购买商品的丰富度',
  `single_spu_average` decimal(18,2) COMMENT '单店配送SPU均值，BD当月单店客户配送订单中平均每单的SPU商品种类数',
  `vip_spu_average` decimal(18,2) COMMENT '大客户配送SPU均值，BD当月大客户配送订单中平均每单的SPU商品种类数',
  `category_multiply_gmv` decimal(18,2) COMMENT '品类提成乘配送金额，品类提成系数与配送金额的乘积，用于品类推广激励计算，单位：元',
  `normal_pull_new_amount` bigint COMMENT '普通拉新数量，BD当月拉新客户中注册且首单金额小于15元的客户数量，区别于高价值拉新',
  `no_order_register` bigint COMMENT '注册未下单数量，BD当月通过拉新渠道注册但尚未产生首单的客户数量，用于转化率分析',
  `data_month` varchar(6) COMMENT '数据月份，格式为YYYYMM，表示数据统计的月份，如：202506',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_crm_bd_month_gmv_data_month` (`bd_id`, `data_month`) COMMENT '按BD和月份查询的复合索引，用于唯一性约束和查询优化',
  KEY `idx_crm_bd_month_gmv_bd_id` (`bd_id`) COMMENT '按BD查询的索引，用于BD维度的分析和查询',
  KEY `idx_crm_bd_month_gmv_bd_name` (`bd_name`, `data_month`) COMMENT 'BD名字+月份索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='BD月度GMV汇总表，记录每个BD销售人员的月度GMV业绩数据，包括总GMV、各类型客户GMV、品类GMV、拜访数据、客户活跃度等关键指标。与bd_mtd_comm_df表配合使用，提供BD绩效考核、业务分析的全面数据支持。主要业务场景：1)BD月度业绩排名 2)客户类型分析 3)品类销售分析 4)拜访效果评估 5)拉新转化分析';