-- 【非常重要】普通订单的配送日通常是订单日往后推至少1天；省心送订单的话，用户可自行设定配送计划日，一般需要在下单后90天内配送完毕。
-- 【非常重要】省心送订单和普通订单的履约件数统计逻辑不同，分开处理更清晰，推荐使用UNION ALL方式，分别处理省心送订单和普通订单
-- 用户提到配送的点位数是指一个contact_id 就是一个点位数（其实就是配送的目的地）

CREATE TABLE `xianmudb`.`delivery_plan` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_no` varchar(45) COMMENT '订单号, 关联查询 `orders`.`order_no`',
  `status` smallint(6) DEFAULT '2' COMMENT '配送计划的状态: 2:已预约待占用库存(仅省心送订单有此状态，表示客户已经选定了配送日，但仓库还没有占用库存，仓库占用库存后会自动更新为‘3待配送’状态); 3:待配送(待履约); 6:已收货(代表履约完成); 11:已撤销',
  `delivery_time` date COMMENT '计划配送日，如：2025-04-04。如查询某一日的已配送/履约完成的订单，请使用这个字段且status=6来进行过滤',
  `quantity` int(11) COMMENT '本次的配送商品件数(仅省心送订单有此字段），如：2。',
  `update_time` datetime ON UPDATE CURRENT_TIMESTAMP COMMENT '配送计划更新时间',
  `deliverytype` tinyint(4) DEFAULT '0' COMMENT '配送方式：0:城配仓配送（车队），1:自提(客户到仓库自提)',
  `time_frame` varchar(50) COMMENT '如果有该字段，表示客户购买的是精准送服务，该订单也被视为‘精准送’订单。本字段表示客户要求的配送日内的配送时段，比如: 08:30-09:30',
  `admin_id` int(11) COMMENT '下单门店所属的大客户ID，关联查询`admin`.`admin_id`且`admin_type`=0',
  `order_store_no` int(11) COMMENT '下单时，库存扣减所用的库存仓编号，关联查询`warehouse_storage_center`.`warehouse_no`，库存仓名字则为:`warehouse_storage_center`.`warehouse_name`，可直接查询warehouse_storage_center获取库存仓名字和编号',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '配送计划生成时间',
  `contact_id` bigint(20) NOT NULL COMMENT '配送的目的地(收货地址)，通常一个contact_id就代表一个配送点位。关联查询`contact`.`contact_id`',
  PRIMARY KEY (`id`),
  KEY `ind_order_no` (`order_no`),
  KEY `ind_master_order_no` (`master_order_no`),
  KEY `ind_delivery_time` (`delivery_time`,`status`,`order_store_no`),
  KEY `ind_time_contact` (`contact_id`,`delivery_time`),
  KEY `ind_order_store_no_status` (`order_store_no`,`status`),
  KEY `idx_put_off_time` (`put_off_time`),
  KEY `idx_status_deliverytime` (`status`,`delivery_time`)
) ENGINE=InnoDB AUTO_INCREMENT=15402484 DEFAULT CHARSET=utf8 COMMENT='订单的配送计划表。通常而言，一笔订单只有一个配送计划，仅当订单类型为省心送时，才会有多个配送计划，此时，一个配送计划中仅包含部分商品，比如下单了10件，客户分5次配送，那么每个配送计划可能是2件商品。';

-- 例1: 统计某门店明天的SKU履约件数和履约金额。由于请求的是明天，还没到，因此统计待配送的订单。
SELECT 
    base_data.m_id,
    base_data.pd_name,
    SUM(base_data.履约件数) as 履约件数,
    SUM(base_data.price * base_data.履约件数) as 履约金额
FROM (
    -- 子查询：为每种订单类型计算对应的履约件数
    SELECT 
        orders.m_id,
        order_item.pd_name,
        order_item.price,
        CASE 
            WHEN orders.type = 1 THEN delivery_plan.quantity  -- 省心送订单：使用配送计划数量
            ELSE order_item.amount                            -- 普通订单：使用订单项数量
        END as 履约件数
    FROM orders
    INNER JOIN order_item ON orders.order_no = order_item.order_no 
    INNER JOIN delivery_plan ON orders.order_no = delivery_plan.order_no
    WHERE orders.m_id = [门店ID] 
        AND delivery_plan.status = 3  -- 统计待配送的订单
        AND delivery_plan.delivery_time = DATE_ADD(CURRENT_DATE(), INTERVAL 1 DAY) -- 明天
) as base_data
GROUP BY base_data.m_id, base_data.pd_name;

-- 例2: 统计某门店7月的履约商品清单（SKU履约件数、SKU总购买件数、SKU履约金额）：
SELECT 
    base_data.*,
    base_data.SKU单价 * base_data.SKU履约件数 AS SKU履约金额
FROM (
    -- 子查询：为每种订单类型计算对应的履约件数
    SELECT 
        m.mname AS 客户名称,
        o.order_no AS 订单编号,
        oi.pd_name AS 商品名称,
        oi.sku AS SKU编码,
        oi.weight AS 规格,
        CASE 
            WHEN o.type = 1 THEN dp.quantity  -- 省心送订单：使用配送计划数量
            ELSE oi.amount                    -- 普通订单：使用订单项数量
        END AS SKU履约件数,
        oi.amount AS SKU总购买件数,
        oi.price AS SKU单价,
        dp.delivery_time AS 履约日期
    FROM orders o
    JOIN merchant m ON o.m_id = m.m_id
    JOIN order_item oi ON o.order_no = oi.order_no
    JOIN delivery_plan dp ON o.order_no = dp.order_no
    WHERE o.m_id = [门店ID]
        AND dp.status = 6  -- 仅统计履约完成的订单
        AND dp.delivery_time >= '2025-07-01'
        AND dp.delivery_time < '2025-08-01'
) AS base_data
ORDER BY base_data.履约日期 DESC, base_data.商品名称
LIMIT 2000;
