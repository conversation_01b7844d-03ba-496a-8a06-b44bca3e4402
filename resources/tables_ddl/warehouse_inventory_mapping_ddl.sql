CREATE TABLE `xianmudb`.`warehouse_inventory_mapping` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键、自增',
  `warehouse_no` int(11) NOT NULL COMMENT '仓库编号, 对应warehouse_storage_center.warehouse_no，area_store.area_no',
  `store_no` int(11) NOT NULL COMMENT '物流中心编号，对应fence.store_no',
  `sku` varchar(33) NOT NULL COMMENT 'sku, 对应inventory.sku',
  PRIMARY KEY (`id`),
  UNIQUE KEY `warehouse_inventory_mapping_main_index` (`store_no`,`sku`),
  KEY `warehouse_inventory_mapping_warehouse_index` (`warehouse_no`,`sku`),
  KEY `idx_sku_store_warehouse` (`sku`,`store_no`,`warehouse_no`),
  KEY `idx_warehouse_no_store_no` (`warehouse_no`,`store_no`)
) ENGINE=InnoDB AUTO_INCREMENT=1678326 DEFAULT CHARSET=utf8 COMMENT='围栏sku仓库映射表'
;