CREATE TABLE `xianmudb`.`crm_bd_org` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID，自增长',
  `bd_id` bigint(20) NOT NULL COMMENT '销售代表ID，唯一标识一个销售人员，如1132198，取自`admin`.`admin_id`',
  `bd_name` varchar(255) NOT NULL COMMENT '销售代表姓名，如"龙菲", 取自`admin`.`admin_name`',
  `parent_name` varchar(255) COMMENT '上级主管姓名，关联crm_bd_org.bd_name，如"苏伟"，取自`admin`.`admin_name`。当销售主管们(M1,M2,M3)提到‘我的团队’时，是指他们的直接下属（且包括间接下属，即下属的下属）。',
  `rank` int(11) NOT NULL COMMENT '销售员的职级，取值范围:[4,3,2,1]。4: BD(普通销售代表); 3: M1(销售主管M1，是销售代表的直接主管); 2: M2(销售经理M2，是区域主管，销售经理M1的主管); 1: M3(销售总监M3，销售经理M2的主管，为销售团队的最高级管理层)。',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_bd_id_rank` (`bd_id`,`rank`),
) ENGINE=InnoDB AUTO_INCREMENT=1403 DEFAULT CHARSET=utf8mb4 COMMENT='销售组织架构表，记录销售人员信息及其上下级关系，通过parent_name建立层级关系，用于管理销售团队的组织结构。';