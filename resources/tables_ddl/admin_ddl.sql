CREATE TABLE `xianmudb`.`admin` (
  `admin_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `create_time` datetime NOT NULL COMMENT '创建时间，记录账号创建时间',
  `is_disabled` tinyint(1) DEFAULT '0' COMMENT '是否禁用：0-启用，1-禁用',
  `username` varchar(255) COMMENT '登录用户名，通常是邮箱',
  `realname` varchar(255) COMMENT '大客户的工商名称(此时admin_type必须要=0)，比如“四川茶语道企业管理有限公司”',
  `phone` varchar(18) COMMENT '联系电话',
  `saler_id` int(11) COMMENT '该大客户所属的销售员bd_id, 关联查询crm_bd_org.bd_id',
  `saler_name` varchar(20) COMMENT '该大客户所属的销售员bd_name, 关联查询crm_bd_org.bd_name',
  `name_remakes` varchar(50) COMMENT '大客户的名称，大多数情况下都应该优先使用这个字段来查询。比如浙江星巴克、浙江茶百道、乐乐茶、霸王茶姬香水柠檬',
  `major_cycle` int(2) COMMENT '报价周期：0-周报价，1-半月报价，2-月报价，3-日报价',
  `update_time` datetime ON UPDATE CURRENT_TIMESTAMP COMMENT '最后修改时间',
  `admin_type` int(11) COMMENT '0:代表这是我司的大客户，2-批发客户',
  `admin_chain` int(11) COMMENT '连锁范围：0-NKA(全国连锁)，1-LKA(小地方连锁)，2-其他连锁',
  `admin_grade` int(11) COMMENT '品牌等级：0-普通，1-KA',
  `admin_switch` int(11) DEFAULT '1' COMMENT '充送开关：0-开启，1-关闭',
  `contract_method` varchar(255) COMMENT '合作模式配置，为JSON字符串，key为收款方式，value为商品展示方式。key取值：1和2，1代表账期，2代表现结；value取值：1和2，1代表商品定量展示，2代表商品全量展示。例如：{"2":1}，表示现结且商品定量展示。{"2":2}，表示现结且商品全量展示，依此类推。',
  PRIMARY KEY (`admin_id`),
  KEY `idx_username` (`username`),
  KEY `idx_realname` (`realname`),
  KEY `idx_base_user_id` (`base_user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='存储我司大客户的信息(name_remakes), 同时记录了大客户的销售员ID(saler_id,saler_name)信息。';