CREATE TABLE `xianmudb`.`area_store` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `area_no` int(11) COMMENT '库存仓号, 关联查询`warehouse_storage_center`.`warehouse_no`，库存仓名字则为:`warehouse_storage_center`.`warehouse_name`，可直接查询warehouse_storage_center获取库存仓名字和编号',
  `sku` varchar(30) COMMENT 'sku编号, 关联查询`inventory`.`sku`',
  `quantity` int(10) unsigned DEFAULT '0' COMMENT '仓库内的实物库存数量（不等于可用库存，因为可能被客户的订单锁定了）',
  `update_time` datetime ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `online_quantity` int(10) unsigned DEFAULT '0' COMMENT '线上库存，可用库存，当可用库存大于0时，才能被客户购买',
  `sale_lock_quantity` int(11) unsigned DEFAULT '0' COMMENT '销售冻结库存量，指已经被订单占用了的库存，不可再用作它途',
  `lock_quantity` int(11) unsigned DEFAULT '0' COMMENT '全部的锁定库存额，包含销售冻结(也就是sale_lock_quantity)、调拨冻结、采退冻结、盘点冻结等',
  `road_quantity` int(11) unsigned DEFAULT '0' COMMENT '在途库存，采购或者调拨在送货途中的库存，还未入库',
  `status` int(2) DEFAULT '0' COMMENT 'sku的库内状态。0:正常, 1:采购入库中, 2:订单配送或者调拨等原因出入库中, 3:仓库盘点中',
  `advance_quantity` int(10) unsigned DEFAULT '0' COMMENT '采购预售库存',
  `tenant_id` bigint(20) DEFAULT '1' COMMENT '租户id(saas品牌方)，鲜沐为1',
  `warehouse_tenant_id` bigint(20) DEFAULT '1' COMMENT '仓库租户id(saas品牌方)，鲜沐为1',
  `owner_code` varchar(32) COMMENT '货主编码',
  `owner_name` varchar(64) COMMENT '货主名称',
  PRIMARY KEY (`id`),
  UNIQUE KEY `index_sku` (`area_no`,`sku`),
  KEY `area_store_sku_index` (`sku`),
  KEY `idx_wno_quantity` (`area_no`,`quantity`),
  KEY `idx_areano_onlinequantity` (`area_no`,`online_quantity`)
) ENGINE=InnoDB AUTO_INCREMENT=3491167 DEFAULT CHARSET=utf8 COMMENT='库存仓实际库存表，一个SKU在一个仓库只有一条库存记录';