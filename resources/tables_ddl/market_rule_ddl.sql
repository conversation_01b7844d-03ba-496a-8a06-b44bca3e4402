CREATE TABLE `market_rule` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) COMMENT '营销活动名称，比如‘8月冲量商品返现’，‘安佳8月红包返现’等',
  `type` int(11) COMMENT '活动类型。0-满返(返优惠券给客户)，1-满减(订单满一定金额后减X元)，2-红包返现(返现金红包给客户，不可提取，但可购买商品)',
  `detail` varchar(255) COMMENT '规则详情',
  `show_name` varchar(50) COMMENT '展示名称，这是客户看到的名称。',
  `start_time` datetime COMMENT '规则开始时间',
  `end_time` datetime COMMENT '规则结束时间',
  `area_no` varchar(255) COMMENT '营销活动开放区域, 关联area.area_no',
  `category_id` varchar(255) COMMENT '参与活动的品类, 关联category.id',
  `sku` varchar(1000) COMMENT '参与活动的所有sku, 关联inventory.sku',
  `update_time` datetime COMMENT '修改时间',
  `rule_detail` text,
  `create_time` datetime COMMENT '创建时间',
  `support_type` int(11) '1',
  `coupon_rule` tinyint(4) '1' COMMENT '当type=0时，即满返活动返券规则：1-确认收货后（默认） 2-支付完成后',
  `creator` varchar(50) COMMENT '创建人名字',
  `status` tinyint(4) '1' COMMENT '活动状态。0-停用 1-启用',
  PRIMARY KEY (`id`),
  KEY `idx_end_time` (`end_time`) COMMENT '结束时间索引',
  KEY `idx_joint_time` (`start_time`,`end_time`,`type`) COMMENT '时间搜索联合索引'
) ENGINE=InnoDB AUTO_INCREMENT=3030 DEFAULT CHARSET=utf8 COMMENT='营销活动规则表';

CREATE TABLE `order_preferential` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_no` varchar(50) COMMENT '订单号，关联orders.order_no',
  `amount` decimal(10,2) COMMENT '优惠金额',
  `type` int(11) COMMENT '优惠类型, 2-满减(对应market_rule.type=1) 20-满返(对应market_rule.type=0) 21-红包返现(对应market_rule.type=2)',
  `related_id` bigint(20) COMMENT '关联优惠id, 这里主要是关联 market_rule.id。但是要注意，一定要结合market_rule.type来进行查询。',
  PRIMARY KEY (`id`),
  KEY `in_order_no` (`order_no`)
) ENGINE=InnoDB AUTO_INCREMENT=8754034 DEFAULT CHARSET=utf8mb4 COMMENT='订单优惠信息表，记录订单使用了那些营销规则产生的优惠。'