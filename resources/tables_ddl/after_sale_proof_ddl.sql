CREATE TABLE `xianmudb`.`after_sale_proof` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `after_sale_order_no` varchar(36) COMMENT '售后订单号，关联after_sale_order.after_sale_order_no',
  `quantity` int(11) COMMENT '售后数量，需要配合after_sale_order.after_sale_unit字段一起使用，才能知道是多少g/kg/盒/个等',
  `proof_pic` varchar(1000) COMMENT '售后凭证图片。为‘;’符号分割的多张图片，每张图片需要加上https://azure.summerfarm.net/前缀才能访问',
  `handle_num` decimal(10,2) COMMENT '最终售后金额，客服已经审批完成了且同意的金额',
  `handle_type` int(11) COMMENT '售后处理方式,0返券，1未知，2退款，3录入账单，4退货退款，5退货录入账单，6换货，7补发，8人工退款，9拒收退款，10拒收账单，11拦截退款，12拦截录入账单 13退运费 14 退运费录入账单',
  `after_sale_type` varchar(100) COMMENT '售后原因类型，比如‘商品品质问题’、‘商品数量不符’，‘包装问题’等',
  `apply_remark` varchar(1000) COMMENT '客服审核备注',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updatetime` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_after_no` (`after_sale_order_no`),
  KEY `idx_handle_order` (`handle_type`,`after_sale_order_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='售后明细表，记录售后单的处理明细情况';