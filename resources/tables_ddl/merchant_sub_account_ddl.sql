CREATE TABLE `xianmudb`.`merchant_sub_account` (
  `account_id` bigint(30) NOT NULL AUTO_INCREMENT COMMENT '主键、自增',
  `m_id` bigint(30) COMMENT '店铺id,关联查询`merchant`.`m_id`',
  `type` int(1) COMMENT '子账号类型：0:母账号(代表这是店长)，1:子账号(通常来说是指商户店员)',
  `contact` varchar(255) COMMENT '子账户名称',
  `phone` varchar(20) COMMENT '子账户手机号',
  `status` int(11) DEFAULT '0' COMMENT '子账户是否审核通过（门店店长可审核子账户）：0:待审核, 1:审核通过, 2:已注销',
  `delete_flag` int(1) DEFAULT '1' COMMENT '删除标识：0、已删除 1、未删除',
  `login_time` datetime COMMENT '账户的最后登录时间，可以用来统计商城的今日活跃用户数（无法完整的统计历史数据，因为该字段仅表示当前登录时间，无法统计历史登录时间）。例如：‘2025-06-03 21:26:22’',
  PRIMARY KEY (`account_id`),
  KEY `merchant_sub_account_mid_index` (`m_id`),
  KEY `idx_phone` (`phone`)
) ENGINE=InnoDB AUTO_INCREMENT=576959 DEFAULT CHARSET=utf8 COMMENT='商户子账号主表。一个merchant至少有一个子账号，商户可能存在多个子账号。';