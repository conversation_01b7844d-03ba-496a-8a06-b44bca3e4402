CREATE TABLE `major_price` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `sku` varchar(20) comment '商品sku,取自inventory.sku',
  `pd_name` varchar(50) comment '商品名称,取自products.pd_name',
  `weight` varchar(100) comment '商品规格,取自inventory.weight',
  `area_no` int(11) comment '运营服务区编号,取自area.area_no',
  `admin_id` int(11) comment '大客户id,取自admin.admin_id',
  `price` decimal(11,2) comment '该大客户的商品价格',
  `area_name` varchar(30) comment '运营服务区名称,取自area.area_name',
  `direct` int(2) comment '是否为账期, 1:账期, 2:现结',
  `valid_time` datetime COMMENT '报价单开始生效时间',
  `invalid_time` datetime COMMENT '报价单失效时间',
  `mall_show` int(11) DEFAULT '0' COMMENT '用户是否展示',
  `price_type` tinyint(4) COMMENT '价格类型：0:代表商城价, 1:合同价（指定价，固定价格）, 2:合同价（毛利率n%）, 3:商城价上浮n%, 4:商城价下浮n%, 5:商城价加价, 6:商城价减价',
  `cost` decimal(13,2) COMMENT '商品在该运营服务区的成本价。用来计算毛利率报价价格',
  `interest_rate` decimal(11,2) COMMENT '报价毛利率',
  `fixed_price` decimal(13,2) COMMENT '毛利率报价后计算得到价格',
  `original_price` decimal(13,2) COMMENT '商城原售价',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `large_area_no` int(11) COMMENT '运营大区编号,取自large_area.large_area_no',
  `price_adjustment_value` decimal(11,2) COMMENT '商城价上浮或者下浮、加价/减价的调整值，如果是上浮/下浮，则表示百分比，如果是加价/减价，则表示元。',
  `remark` varchar(1024) COMMENT '备注',
  `status` tinyint(4) DEFAULT '0' COMMENT '状态0=仅保存, 1=已提交（待生效，需要结合生效时间来判断）',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uni_idx_admin_city_list` (`admin_id`,`direct`,`sku`,`area_no`,`valid_time`,`invalid_time`),
  KEY `uq_major_price` (`admin_id`,`direct`,`area_no`),
  KEY `sku_index` (`sku`,`admin_id`,`area_no`,`direct`),
  KEY `idx_area_time` (`area_no`,`invalid_time`,`valid_time`),
  KEY `idx_admin_invalid_time` (`admin_id`,`direct`,`invalid_time`,`status`),
  KEY `idx_admin_city_list` (`admin_id`,`direct`,`sku`,`large_area_no`,`valid_time`,`invalid_time`)
) ENGINE=InnoDB AUTO_INCREMENT=2696893 DEFAULT CHARSET=utf8 COMMENT='大客户商品价格表,也就是大客户的商品报价单';