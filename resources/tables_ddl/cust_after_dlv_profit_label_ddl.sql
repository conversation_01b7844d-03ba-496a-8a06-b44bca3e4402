CREATE TABLE `xianmu_offline_db`.`cust_after_dlv_profit_label` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT 'primary key',
  `cust_id` bigint COMMENT '客户ID',
  `dlv_profit_label` varchar(255) COMMENT '单客户履约后利润分层标签',
  `dlv_profit_rate_label` varchar(255) COMMENT '履约实付利润率标签：高（>=10%),低(<10%)',
  `life_cycle` varchar(255) COMMENT '单客户生命周期，枚举值: [准流失期,已流失期,成长期,新人期,沉默期,稳定期,适应期]',
  `life_cycle_detail` varchar(50) COMMENT '单客户生命周期细分标签, A1,A2,B1,B2等',
  `dlv_profit_group` varchar(10) COMMENT '履约后利润分层(大类：A，B，C)',
  PRIMARY KEY (`id`),
  KEY `idx_cust_id_life_cycle_detail` (`cust_id`,`life_cycle_detail`),
  KEY `idx_cust_id` (`cust_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1194720 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='平台客户分层标签表，可查询A类、B类客户等'