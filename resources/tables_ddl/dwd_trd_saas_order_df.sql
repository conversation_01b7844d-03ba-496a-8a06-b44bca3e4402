-- 【注意】此表是ODPS表，仅可使用ODPS SQL来获取数据，无法使用MySQL来查询。
-- 需要使用工具：fetch_odps_sql_result 来查询。
-- 使用时，必须要加上过滤条件ds=max_pt('dwd_trd_saas_order_df') 来获取最新数据，其他ds都是非法的，不加ds条件会报错。
CREATE TABLE IF NOT EXISTS dwd_trd_saas_order_df(
	order_no STRING COMMENT '订单编号',
	 order_id BIGINT COMMENT '订单ID',
	 order_item_id STRING COMMENT '订单项编号',
	 order_status BIGINT COMMENT '订单状态：1下单中，2待支付,3 已支付,4待收货,5已完成,6已取消,7已退款,8 关单中 ,9 已关闭,10 等待出库,11 部分配送,12 出库中',
	 order_item_status BIGINT COMMENT '订单项状态：1下单中，2待支付,3待配送,4待收货,5已完成,6已取消,7已退款',
	 order_date STRING COMMENT '下单日期',
	 order_time DATETIME COMMENT '下单时间',
	 pay_time DATETIME COMMENT '支付时间，不含尾款',
	 confirm_time DATETIME COMMENT '确认时间',
	 recall_date STRING COMMENT '召回日期',
	 recall_time DATETIME COMMENT '召回时间',
	 recall_reason STRING COMMENT '订单召回原因，各种取消；枚举：已退款,支付超时,已撤销,预售余额超时未支付,手动关单,人工退款',
	 item_id BIGINT COMMENT '商品item_id',
	 sku_id STRING COMMENT '商品SKU',
	 spu_id BIGINT COMMENT '商品SPU；即pd_id',
	 title STRING COMMENT '商品标题',
	 sub_title STRING COMMENT '商品副标题',
	 category1 STRING COMMENT '后台一级分类',
	 sku_type BIGINT COMMENT '商品类型; 0 鲜沐自营 1 鲜沐代仓 10品牌方自营',
	 warehouse_type BIGINT COMMENT '配送仓类型 0,无仓1三方仓 2自营仓',
	 supplier_sku_id BIGINT COMMENT '供应商sku的inv_id',
	 supplier_tenant_id BIGINT COMMENT '供应商Id',
	 supplier_sku STRING COMMENT '供应商sku',
	 area_item_id BIGINT COMMENT '区域商品Id',
	 supply_price DECIMAL(38,18) COMMENT '供应商报价',
	 tenant_id BIGINT COMMENT '租户Id',
	 tenant_name STRING COMMENT '租户名称',
	 store_id BIGINT COMMENT '店铺Id',
	 store_name STRING COMMENT '门店名称',
	 store_type BIGINT COMMENT '门店类型：0、直营店 1、加盟店 2、托管店',
	 account_id BIGINT COMMENT '下单账号Id',
	 brand_id BIGINT COMMENT '品牌ID（原大客户）',
	 brand_name STRING COMMENT '品牌的企业名称',
	 brand_type STRING COMMENT '品牌类型；枚举：单店,批发大客户,普通大客户,KA大客户',
	 brand_alias STRING COMMENT '品牌的品牌名称',
	 bd_id BIGINT COMMENT '销售ID',
	 bd_name STRING COMMENT '销售名称',
	 address STRING COMMENT '收货地址',
	 province STRING COMMENT '省',
	 city STRING COMMENT '市',
	 area STRING COMMENT '区',
	 sku_cnt BIGINT COMMENT '商品数量',
	 real_unit_amt DECIMAL(38,18) COMMENT '实付单价',
	 real_total_amt DECIMAL(38,18) COMMENT '实付总价',
	 origin_unit_amt DECIMAL(38,18) COMMENT '应付单价',
	 origin_total_amt DECIMAL(38,18) COMMENT '应付总价',
	 delivery_amt DECIMAL(38,18) COMMENT '运费',
	 is_credit_paid BIGINT COMMENT '是否账期订单，1是，0否',
	 delivery_time DATETIME COMMENT '配送日期',
	 finished_time DATETIME COMMENT '配送完成时间',
	 item_code STRING COMMENT '商品自有编码',
	 pricing_type BIGINT COMMENT '定价方式0、百分比上浮 1、定额上浮 2、固定价',
	 pricing_number DECIMAL(38,18) COMMENT '定价数值',
	 group_id BIGINT COMMENT '门店分组编号',
	 group_name STRING COMMENT '门店分组名称',
	 store_no BIGINT COMMENT '城配仓编码',
	 contact_name STRING COMMENT '收货人',
	 contact_phone STRING COMMENT '收货人联系电话',
	 pay_type BIGINT COMMENT '支付方式 1,线上支付 2,账期 3、余额支付',
	 online_pay_channel BIGINT COMMENT '支付渠道 0、微信 1、汇付',
	 remark STRING COMMENT '订单备注',
	 sku STRING COMMENT '货品sku编码',
	 specification STRING COMMENT '货品规格',
	 goods_type BIGINT COMMENT '商品类型 0无货商品 1报价货品 2自营货品',
	 order_type BIGINT COMMENT '0-普通订单,1=组合订单',
	 combine_order_id BIGINT COMMENT '组合订单id',
	 supplier_name STRING COMMENT '供应商名称',
	 snapshot_specification_unit STRING COMMENT '商品规格单位',
	 warehouse_no BIGINT COMMENT '仓库编号',
	 after_sale_unit STRING COMMENT '售后单位',
	 main_picture STRING COMMENT '图片路径'
)
PARTITIONED BY (ds STRING)
STORED AS ALIORC
TBLPROPERTIES ('comment'='saas订单表(全量表)');