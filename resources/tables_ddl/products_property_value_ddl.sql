CREATE TABLE `xianmudb`.`products_property_value` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键、自增',
  `pd_id` int(11) COMMENT 'pd_id, 关联products.pd_id',
  `products_property_id` int(11) COMMENT '属性id，当=2时，表示商品的品牌',
  `products_property_value` varchar(50) COMMENT '属性值，当products_property_id=2时，表示商品的品牌名称，比如‘鲜沐农场’、‘安佳’',
  PRIMARY KEY (`id`),
  UNIQUE KEY `products_property_value_index` (`pd_id`,`products_property_id`),
) ENGINE=InnoDB AUTO_INCREMENT=127934 DEFAULT CHARSET=utf8 COMMENT='商品属性值,但是我们这里主要用来获取商品的品牌信息';

-- 比如想要获取‘鲜沐农场’品牌的所有商品，则可使用：
SELECT
  ppv.pd_id,
  ppv.`products_property_value` as `商品品牌名称`,
  pd.pd_name as `商品名称`
FROM
  `products_property_value` ppv
  INNER JOIN products pd on pd.`pd_id` = ppv.`pd_id`
WHERE
  ppv.`products_property_id` = 2
  and ppv.products_property_value = '鲜沐农场';