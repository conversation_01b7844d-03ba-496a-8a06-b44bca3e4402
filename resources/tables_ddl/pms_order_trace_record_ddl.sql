CREATE TABLE `xianmudb`.`pms_order_trace_record` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID，自增长',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `order_no` varchar(64) COMMENT '关联单号：当type=11,25时，关联采购预约单表:stock_arrange.purchase_no; 当type=56时，关联采购退货单号',
  `extend_no` varchar(64) COMMENT '拓展单号：当type=11,25,56时，关联采购单主表:purchases.purchase_no',
  `sku` varchar(64) COMMENT 'SKU编码，关联商品SKU表:inventory.sku',
  `operate_no` varchar(64) COMMENT '操作单号，WMS回告单号',
  `type` int(11) COMMENT '跟踪类型：11-采购入库, 25-越库入库, 56-采购退货出库',
  `quantity` int(11) COMMENT '数量',
  `operate_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  `operator_name` varchar(64) COMMENT '操作人姓名',
  `current_no` varchar(64) COMMENT '当前记录相关编号：当type=11,25时，表示采入仓; 当type=56时，表示出库仓',
  `relation_no` varchar(64) COMMENT '当前记录间接关联编号：当type=11,25,56时，关联供应商表:supplier.id',
  `produce_at` date COMMENT '生产日期',
  `shelf_life` date COMMENT '保质期',
  `weight` decimal(10,2) COMMENT '重量(毛重)',
  `unit_price` decimal(10,2) COMMENT '单价',
  `tenant_id` bigint(20) unsigned COMMENT '租户ID，跟随业务单据(采购单，预约单，采退单)，不随操作单',
  PRIMARY KEY (`id`),
  KEY `order_no_idx` (`order_no`),
  KEY `extend_no_type_idx` (`extend_no`,`type`),
  KEY `extend_no_sku_type_idx` (`extend_no`,`sku`,`type`),
  KEY `op_no_idx` (`operate_no`)
) ENGINE=InnoDB AUTO_INCREMENT=4378 DEFAULT CHARSET=utf8mb4 COMMENT='订单跟踪记录表，记录采购单、采购退货单等履约执行情况（仓库操作维度）';
