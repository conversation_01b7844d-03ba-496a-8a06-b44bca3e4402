-- 【注意】此表是ODPS表，仅可使用ODPS SQL来获取数据，无法使用MySQL来查询。
-- 需要使用工具：fetch_odps_sql_result 来查询。
-- 使用时，必须要加上过滤条件 ds='20250707' 或者 ds between '20250701' and '20250707' 来获取对应日期的数据。不加 ds 条件会报错。
-- 【注意】本表为按天汇总的客户行为与下单分析指标明细，适用于天级别的客户留存、转化、活跃等分析，每个ds分区的数据是独立的，包含了该天的客户行为与下单数据。

CREATE TABLE IF NOT EXISTS app_chatbi_mall_cust_analytics_di
(
    cust_id                      BIGINT COMMENT '客户ID，与埋点/订单里的cust_id一致'
    ,cust_name                   STRING COMMENT '客户名称/门店名称'
    ,cust_phone                  STRING COMMENT '客户电话'
    ,cust_register_province      STRING COMMENT '客户注册省份，比如浙江、江苏、上海、广东'
    ,cust_register_city          STRING COMMENT '客户注册城市，比如杭州市、宁波市、苏州市、深圳市'
    ,cust_register_area          STRING COMMENT '客户注册行政区/区县，比如西湖区、江北区、姑苏区、福田区、连江县'
    ,cust_register_date          STRING COMMENT '客户注册日期，格式：yyyyMMdd'
    ,is_new_register_cust        BIGINT COMMENT '是否为当日新注册客户，1是，0否；用于衡量新增'
    ,cust_type                   STRING COMMENT '客户类型，枚举：面包蛋糕/茶饮/咖啡/其他/甜品冰淇淋/西餐等'
    ,area_name                   STRING COMMENT '运营区域名称，比如杭州、宁波、苏州'
    ,area_no                     BIGINT COMMENT '运营区域编号'
    ,admin_name                  STRING COMMENT '大客户名称；为空则代表平台客户/单店'
    ,admin_id                    STRING COMMENT '大客户ID；保持字符串避免跨系统类型不一致'
    ,bd_id                       BIGINT COMMENT '客户所属BD ID（仅对私海客户有值）'
    ,bd_name                     STRING COMMENT '客户所属BD姓名（仅对私海客户有值）'
    ,cust_sku_viewed_cnt         BIGINT COMMENT '当日该客户浏览过的SKU去重数量，用于衡量浏览活跃度'
    ,cust_sku_clicked_cnt        BIGINT COMMENT '当日该客户点击过的SKU去重数量，事件类型=cl'
    ,cust_stay_time_minutes      DECIMAL(18,2) COMMENT '当日累计停留时长(分钟)'
    ,cust_searched_cnt           BIGINT COMMENT '当日搜索行为次数'
    ,cust_first_order_date       STRING COMMENT '客户首单日期，格式如：20250101；来自订单宽表'
    ,is_cust_first_order_date    BIGINT COMMENT '当日是否为客户首单日，1是，0否'
    ,cust_order_cnt              BIGINT COMMENT '当日下单的去重订单数'
    ,cust_order_real_total_amt   DECIMAL(38,18) COMMENT '当日订单实际总金额(优惠后)'
    ,cust_order_origin_total_amt DECIMAL(38,18) COMMENT '当日订单原始总金额(优惠前)'
    ,cust_order_sku_ids          BIGINT COMMENT '当日下单涉及的去重SKU数量'
    ,cust_order_sku_cnt          BIGINT COMMENT '当日下单SKU件数合计'
)
COMMENT '商城客户行为与订单分析按日明细表：聚合了埋点日志(dwd_log_mall_di)与订单(dwd_trd_order_df)的关键客户维度指标，用于新增、活跃、转化等分析'
PARTITIONED BY 
(
    ds                           STRING COMMENT '分区日期，格式：yyyymmdd'
)
LIFECYCLE 1000
;

-- 示例查询：
-- 查询浙江过去7天每天的新注册客户数、登陆客户、下单客户数、总下单金额、首单客户数
select ds as 日期, 
    sum(is_new_register_cust) as 新注册客户数, 
    count(distinct cust_id) as 登陆客户数, 
    count(distinct case when cust_order_cnt>0 then cust_id end) as 下单客户数, 
    sum(cust_order_real_total_amt) as 总下单金额,
    sum(is_cust_first_order_date) as 首单客户数
from app_chatbi_mall_cust_analytics_di
where ds between to_char(date_add(getdate(),-7,'dd'), 'yyyyMMdd') 
and to_char(getdate(), 'yyyyMMdd') 
and cust_register_province='浙江';
