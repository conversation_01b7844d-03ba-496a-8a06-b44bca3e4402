CREATE TABLE `xianmudb`.`stock_arrange` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID，自增长',
  `purchase_no` varchar(30) COMMENT '采购单编号，关联采购单主表:purchases.purchase_no',
  `stock_task_id` int(11) COMMENT '任务ID，关联库存任务表:stock_task.id',
  `state` tinyint(4) COMMENT '状态：0-未完成, 1-已完成, 2-已取消, 3-已关闭，查询在途的话只要查询state=0',
  `arrange_time` date COMMENT '采购预约入库日期，也是采购预计到货时间，格式：yyyy-MM-dd',
  `admin_id` int(11) COMMENT '发起人员工ID，关联人员表:admin.admin_id',
  `admin_name` varchar(30) COMMENT '发起人员工姓名',
  `arrange_remark` varchar(100) COMMENT '备注',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_close` tinyint(4) DEFAULT '0' COMMENT '操作标识：0-未更新, 1-已更新（乐观锁）',
  `multi_supplier` tinyint(4) DEFAULT '0' COMMENT '是否是多供应商：0-否, 1-是',
  `supplier_id` bigint(20) COMMENT '供应商ID，关联供应商表:supplier.id, 供应商的名称为supplier.name',
  `source` varchar(255) DEFAULT 'xianmu' COMMENT '来源：xianmu（鲜沐），或者：saas（SaaS租户）',
  `tenant_id` bigint(20) DEFAULT '1' COMMENT '租户ID，默认1，代表鲜沐，默认情况下只取这个值。saas租户ID从2开始',
  `warehouse_no` int(11) COMMENT '仓库编号，关联仓库表:warehouse_storage_center.warehouse_no',
  PRIMARY KEY (`id`),
  KEY `idx_stock_task_id` (`stock_task_id`),
  KEY `idx_purchase_no` (`purchase_no`),
  KEY `idx_arrange_time` (`arrange_time`,`warehouse_no`,`state`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='采购预约单表, 通过purchase_no和采购单表关联，确定采购在途日期的明细在采购预约单表中，获取采购预约在途一般会给用户返回采购单、仓库名称、供应商名称、sku名称规格、预约入库和预约数量';
