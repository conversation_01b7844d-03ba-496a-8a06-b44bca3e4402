CREATE TABLE `xianmudb`.`product_cost` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID，自增',
  `update_time` datetime ON UPDATE CURRENT_TIMESTAMP COMMENT '成本价更新时间，数据变更时自动更新',
  `warehouse_no` int(11) NOT NULL COMMENT '仓库编号，关联warehouse_storage_center.warehouse_no，标识成本所属仓库',
  `sku` varchar(50) NOT NULL COMMENT 'SKU商品编码，关联inventory.sku，唯一标识商品规格',
  `sub_type` tinyint(4) COMMENT '商品经营性质分类：1=自营-代销不入仓、2=自营-代销入仓、3=自营-经销(我司采购销售)、4=代仓-代仓(客户商品代存储)',
  `current_cost` decimal(12,4) COMMENT '当前期间商品采购成本单价(元)，用于成本核算和毛利计算',
  `current_cost_day` date COMMENT '当前成本的生效日期，标识成本数据的时间归属',
  `current_cost_update_time` datetime COMMENT '当前成本最后更新的具体时间，用于追踪成本变更历史',
  `previous_cost` decimal(12,4) COMMENT '上一期间商品采购成本单价(元)，用于成本对比分析',
  `previous_cost_day` date COMMENT '上期成本的生效日期，用于历史成本追溯',
  `previous_cost_update_time` datetime COMMENT '上期成本最后更新的具体时间，用于成本变更历史追踪',
  `extra` varchar(500) COMMENT '扩展信息字段，存储额外的成本相关数据(JSON格式)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_warehouse_sku` (`warehouse_no`,`sku`)
) ENGINE=InnoDB AUTO_INCREMENT=22105 DEFAULT CHARSET=utf8 COMMENT='商品成本管理表，记录各仓库中的商品的当前成本和上一期成本信息，用于毛利分析和成本控制。注意：成本数据属于敏感信息，销售人员(BD、M1、M2)无权限查看。';
