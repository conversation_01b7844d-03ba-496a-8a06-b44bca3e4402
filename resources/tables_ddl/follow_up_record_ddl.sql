-- 注意：`condition` 因为和mysql保留字condition冲突，编写SQL时请用`condition`表示。

CREATE TABLE `xianmudb`.`follow_up_record` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID，自增长',
  `m_id` bigint(30) COMMENT '商户ID，关联商户表:merchant.m_id',
  `admin_id` int(11) COMMENT 'BD人员ID，为销售代表的ID，取自`crm_bd_org`.`bd_id`',
  `admin_name` varchar(36) COMMENT '拜访人姓名，销售代表的名字，取自`crm_bd_org`.`bd_name`',
  `follow_up_way` varchar(50) COMMENT '拜访方式。枚举值:[普通上门拜访, 普通拜访-微信, 普通拜访-电话, 普通拜访-企微, 有效拜访]',
  `condition` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '记录BD对商户的经营情况的简要报告，比如商户的生意如何，竞争对手有哪些，对平台的反馈等等',
  `add_time` datetime COMMENT '拜访记录创建时间',
  `visit_objective` tinyint(4) DEFAULT '2' COMMENT '拜访目的。枚举值，0:拉新, 1:催月活,2:客户维护,3:拓品,4:售后处理,5:催省心送',
  `visit_type` tinyint(3) unsigned DEFAULT '0' COMMENT '是否销售主管陪访。0:普通拜访, 1:销售主管陪访',
  `province` varchar(30) COMMENT '被拜访的商户的省份名称，注意这里不带省字，比如：广东、浙江、广西',
  `city` varchar(30) COMMENT '被拜访的商户的城市名称，比如：杭州市、贵阳市',
  `area` varchar(30) COMMENT '被拜访的商户的区县名称，比如：余杭区、丰泽区',
  `area_no` int(10) unsigned COMMENT '被拜访的商户的运营服务区编号，关联`area`.`area_no`',
  `last_visit_time` datetime COMMENT '在本次拜访前的上次拜访时间',
  `last_visit_record_id` int(11) COMMENT '在本次拜访前的上次拜访记录ID',
  PRIMARY KEY (`id`),
  KEY `fure_fk_m_id` (`m_id`),
  KEY `rds_idx_3` (`status`,`m_id`,`add_time`),
  KEY `idx_admin_time_id` (`add_time`,`admin_id`),
  KEY `idx_province_city_area` (`province`,`city`,`area`),
  KEY `idx_area_no` (`area_no`,`add_time`)
) ENGINE=InnoDB AUTO_INCREMENT=3776755 DEFAULT CHARSET=utf8 COMMENT='商户拜访记录表，用于记录销售人员对商户的拜访、沟通、维护等跟进情况，包含拜访方式、拜访内容、拜访目标、客户反馈等信息。';