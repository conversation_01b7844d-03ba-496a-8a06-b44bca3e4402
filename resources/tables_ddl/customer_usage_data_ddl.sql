CREATE TABLE `xianmu_offline_db`.`customer_usage_data` (
    -- Primary key and basic info
    m_id VARCHAR(50) PRIMARY KEY COMMENT '客户ID, 与`xianmudb`.`merchant`表的m_id对应',
    m_name VARCHAR(200) NOT NULL COMMENT '客户名称, 与`xianmudb`.`merchant`表的mname对应',
    usage_tier VARCHAR(100) COMMENT '用量分层, 枚举值[中用量(60<=月用量<165KG), 高用量(月用量>=165KG)]',
    phone_number VARCHAR(20) COMMENT '手机号, 与`xianmudb`.`merchant`表的phone对应',
    sales_region VARCHAR(100) COMMENT '销售大区, 这是指各M2所负责的团队名字，枚举值[上海大区, 华中大区, 华南大区, 山东大区, 昆明大区, 浙江大区, 苏皖大区, 西南大区, 闽桂大区]',
    bd_name VARCHAR(100) COMMENT 'BD名称, 与`xianmudb`.`crm_bd_org`表的bd_name对应',
    bd_m2 VARCHAR(100) COMMENT 'BD M2, 取自M1的直接主管(BD的主管之主管), 也就是M2名字，与`xianmudb`.`crm_bd_org`表的bd_name且rank=2对应',
    bd_m1 VARCHAR(100) COMMENT 'BD M1, 取自BD的直接主管，也就是M1的名字，与`xianmudb`.`crm_bd_org`表的bd_name且rank=3对应',
    
    -- Category and type info
    four_level_category_count INT COMMENT '四级类目数，这里是指客户购买的商品的类目个数, 取值 count(distinct `xianmudb`.`order_item`.`category_id`)',
    top1_usage_category VARCHAR(100) COMMENT 'TOP1用量品类',
    top1_percentage DECIMAL(10,4) COMMENT 'TOP1占比',
    top2_category VARCHAR(100) COMMENT 'TOP2品类',
    top2_percentage DECIMAL(10,4) COMMENT 'TOP2品类占比',
    top3_category VARCHAR(100) COMMENT 'TOP3品类',
    top3_percentage DECIMAL(10,4) COMMENT 'TOP3品类占比',
    customer_usage_type VARCHAR(100) COMMENT '客户用量类型, 枚举值[TOP3类目集中客户, TOP1类目集中客户, 多类目客户]',
    
    -- Actual usage columns
    actual_whipped_cream DECIMAL(15,4) DEFAULT 0 COMMENT '实际用量-搅打型稀奶油',
    actual_room_temp_milk DECIMAL(15,4) DEFAULT 0 COMMENT '实际用量-常温牛奶',
    actual_white_sugar DECIMAL(15,4) DEFAULT 0 COMMENT '实际用量-白砂糖',
    actual_high_gluten_flour DECIMAL(15,4) DEFAULT 0 COMMENT '实际用量-高筋面粉',
    actual_low_gluten_flour DECIMAL(15,4) DEFAULT 0 COMMENT '实际用量-低筋面粉',
    actual_unsalted_butter DECIMAL(15,4) DEFAULT 0 COMMENT '实际用量-无盐黄油',
    actual_vegetable_cream DECIMAL(15,4) DEFAULT 0 COMMENT '实际用量-植脂奶油',
    actual_carbonated_drinks DECIMAL(15,4) DEFAULT 0 COMMENT '实际用量-其他型碳酸饮料',
    actual_cream_cheese DECIMAL(15,4) DEFAULT 0 COMMENT '实际用量-奶油奶酪',
    actual_whole_milk_powder DECIMAL(15,4) DEFAULT 0 COMMENT '实际用量-全脂乳粉',
    actual_cookie_crumbs DECIMAL(15,4) DEFAULT 0 COMMENT '实际用量-饼干碎',
    actual_canned_condensed_milk DECIMAL(15,4) DEFAULT 0 COMMENT '实际用量-罐装炼乳',
    actual_medium_gluten_flour DECIMAL(15,4) DEFAULT 0 COMMENT '实际用量-中筋面粉',
    actual_fruit_juice_concentrate DECIMAL(15,4) DEFAULT 0 COMMENT '实际用量-果汁原浆',
    actual_mozzarella DECIMAL(15,4) DEFAULT 0 COMMENT '实际用量-马苏里拉',
    actual_popping_boba DECIMAL(15,4) DEFAULT 0 COMMENT '实际用量-波波丨晶球',
    actual_fresh_eggs DECIMAL(15,4) DEFAULT 0 COMMENT '实际用量-新鲜蛋类',
    actual_fresh_milk DECIMAL(15,4) DEFAULT 0 COMMENT '实际用量-鲜牛奶',
    actual_glutinous_rice_flour DECIMAL(15,4) DEFAULT 0 COMMENT '实际用量-糯米粉',
    actual_frozen_cooked_vegetables DECIMAL(15,4) DEFAULT 0 COMMENT '实际用量-冷冻熟蔬菜制品',
    actual_fructose DECIMAL(15,4) DEFAULT 0 COMMENT '实际用量-果糖',
    actual_mascarpone DECIMAL(15,4) DEFAULT 0 COMMENT '实际用量-马斯卡彭',
    actual_cheddar_processed_cheese DECIMAL(15,4) DEFAULT 0 COMMENT '实际用量-切达再制干酪',
    actual_canned_fruits DECIMAL(15,4) DEFAULT 0 COMMENT '实际用量-水果罐头',
    actual_fruit_fillings DECIMAL(15,4) DEFAULT 0 COMMENT '实际用量-水果类馅料',
    actual_frozen_fruit_pulp DECIMAL(15,4) DEFAULT 0 COMMENT '实际用量-冷冻果肉',
    actual_taro_balls DECIMAL(15,4) DEFAULT 0 COMMENT '实际用量-芋圆',
    
    -- Estimated improvement columns
    estimated_whipped_cream DECIMAL(15,4) DEFAULT 0 COMMENT '预估提升量-搅打型稀奶油',
    estimated_room_temp_milk DECIMAL(15,4) DEFAULT 0 COMMENT '预估提升量-常温牛奶',
    estimated_white_sugar DECIMAL(15,4) DEFAULT 0 COMMENT '预估提升量-白砂糖',
    estimated_high_gluten_flour DECIMAL(15,4) DEFAULT 0 COMMENT '预估提升量-高筋面粉',
    estimated_low_gluten_flour DECIMAL(15,4) DEFAULT 0 COMMENT '预估提升量-低筋面粉',
    estimated_unsalted_butter DECIMAL(15,4) DEFAULT 0 COMMENT '预估提升量-无盐黄油',
    estimated_vegetable_cream DECIMAL(15,4) DEFAULT 0 COMMENT '预估提升量-植脂奶油',
    estimated_carbonated_drinks DECIMAL(15,4) DEFAULT 0 COMMENT '预估提升量-其他型碳酸饮料',
    estimated_cream_cheese DECIMAL(15,4) DEFAULT 0 COMMENT '预估提升量-奶油奶酪',
    estimated_whole_milk_powder DECIMAL(15,4) DEFAULT 0 COMMENT '预估提升量-全脂乳粉',
    estimated_cookie_crumbs DECIMAL(15,4) DEFAULT 0 COMMENT '预估提升量-饼干碎',
    estimated_canned_condensed_milk DECIMAL(15,4) DEFAULT 0 COMMENT '预估提升量-罐装炼乳',
    estimated_medium_gluten_flour DECIMAL(15,4) DEFAULT 0 COMMENT '预估提升量-中筋面粉',
    estimated_fruit_juice_concentrate DECIMAL(15,4) DEFAULT 0 COMMENT '预估提升量-果汁原浆',
    estimated_mozzarella DECIMAL(15,4) DEFAULT 0 COMMENT '预估提升量-马苏里拉',
    estimated_popping_boba DECIMAL(15,4) DEFAULT 0 COMMENT '预估提升量-波波丨晶球',
    estimated_fresh_eggs DECIMAL(15,4) DEFAULT 0 COMMENT '预估提升量-新鲜蛋类',
    estimated_fresh_milk DECIMAL(15,4) DEFAULT 0 COMMENT '预估提升量-鲜牛奶',
    estimated_glutinous_rice_flour DECIMAL(15,4) DEFAULT 0 COMMENT '预估提升量-糯米粉',
    estimated_frozen_cooked_vegetables DECIMAL(15,4) DEFAULT 0 COMMENT '预估提升量-冷冻熟蔬菜制品',
    estimated_fructose DECIMAL(15,4) DEFAULT 0 COMMENT '预估提升量-果糖',
    estimated_mascarpone DECIMAL(15,4) DEFAULT 0 COMMENT '预估提升量-马斯卡彭',
    estimated_cheddar_processed_cheese DECIMAL(15,4) DEFAULT 0 COMMENT '预估提升量-切达再制干酪',
    estimated_canned_fruits DECIMAL(15,4) DEFAULT 0 COMMENT '预估提升量-水果罐头',
    estimated_fruit_fillings DECIMAL(15,4) DEFAULT 0 COMMENT '预估提升量-水果类馅料',
    estimated_frozen_fruit_pulp DECIMAL(15,4) DEFAULT 0 COMMENT '预估提升量-冷冻果肉',
    estimated_taro_balls DECIMAL(15,4) DEFAULT 0 COMMENT '预估提升量-芋圆',
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
    
    -- Indexes
    INDEX idx_customer_name (m_name),
    INDEX idx_customer_mid (m_id),
    INDEX idx_sales_region (sales_region),
    INDEX idx_bd_name (bd_name),
    INDEX idx_usage_tier (usage_tier),
    INDEX idx_top1_category (top1_usage_category)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客户用量数据表，记录每个客户在各个品类上的用量信息，包括实际用量和预估提升量，用于后续的用量分析和预测';