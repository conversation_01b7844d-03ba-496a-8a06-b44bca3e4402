# API接口文档说明
paths：API接口相对路径，同api_call_tool的api_endpoint参数
post：请求方式
summary：接口描述
description：接口描述
tags：接口所属的模块
requestBody：请求体,$ref引用都可以从components.schemas中找到参数，同api_call_tool的params参数
responses：接口的返回值

## 新增客情券接口
- 接口描述：写入接口-新增客情券或月活券接口，给商户名(客户名称)新增券
- 参数重要说明：
  - 申请备注(申请原因)如果用户提供，就自动追加文本"-AI辅助提交"；
  - 月活券适用的品类类型必须由用户提供，不可由agent生成；
- 参数示例：
  - 新增客情券:{"status":0,"couponAmount":1,"threshold":10,"merchantId":474537,"merchantName":"爱吃水果","monthLivingCoupon":0,"situationRemake":"申请客情券-AI辅助提交","createLocation":0}
  - 新增月活:{"status":0,"couponAmount":11,"threshold":100,"merchantId":1480,"monthLivingCoupon":1,"categoryType":3,"situationRemake":"申请月活券-AI辅助提交","createLocation":0}


```yaml
openapi: 3.0.1
info:
  title: ''
  description: ''
  version: 1.0.0
paths:
  /crm-service/merchantSituation/insert:
    post:
      summary: 新增客情券接口
      deprecated: false
      description: ''
      tags:
        - crm/MerchantSituationController
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MerchantSituationDTOAdd'
              description: ''
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AjaxResult'
                description: ''
              example:
                code: ''
                msg: ''
                data: {}
                success: false
          headers: {}
          x-apifox-name: 成功
      security: []
      x-apifox-folder: crm/MerchantSituationController
      x-apifox-status: released
      x-run-in-apifox: https://app.apifox.com/web/project/6277426/apis/api-288413093-run
components:
  schemas:
    MerchantSituationDTOAdd:
      type: object
      properties:
        status:
          type: integer
          description: 状态，0 待审核，默认值为0待审核
        couponAmount:
          type: number
          description: 券金额
        threshold:
          type: number
          description: 券门槛
        merchantId:
          type: integer
          description: 商户ID，merchant表的m_id
          format: int64
        merchantName:
          type: string
          description: 商户名称，merchant表的mname
        monthLivingCoupon:
          type: integer
          description: 提交类型:0客情,1月活，默认值为0客情
        situationRemake:
          type: string
          description: 申请备注
        createLocation:
          type: integer
          description: 创建位置，0 后台，默认值为0后台
        categoryType:
          type: integer
          description: 适用的品类类型，2-乳制品，3-非乳制品，4-水果(鲜果)，客情券无法输入该字段，月活券必须输入
      x-apifox-orders:
        - status
        - couponAmount
        - threshold
        - merchantId
        - merchantName
        - monthLivingCoupon
        - situationRemake
        - createLocation
        - categoryType
      required:
        - status
        - couponAmount
        - threshold
        - merchantId
        - merchantName
        - monthLivingCoupon
        - situationRemake
      x-apifox-ignore-properties: []
      x-apifox-folder: ''
    CouponBlackAndWhiteDTOAdd:
      type: object
      properties:
        id:
          type: integer
          description: primary key
          format: int64
        sku:
          type: string
          description: sku
        productName:
          type: string
          description: 商品名称
        weight:
          type: string
          description: 商品规格
        extType:
          type: integer
          description: 商品类型
        type:
          type: integer
          description: 类型 1-黑名单  2-白名单
        couponId:
          type: integer
          description: 卡劵ID
          format: int64
        creator:
          type: string
          description: 创建人
        createTime:
          type: string
          description: create time
          x-apifox-mock: '@datetime'
      x-apifox-orders:
        - id
        - sku
        - productName
        - weight
        - extType
        - type
        - couponId
        - creator
        - createTime
      x-apifox-ignore-properties: []
      x-apifox-folder: ''
    AjaxResult:
      type: object
      properties:
        code:
          type: string
          description: ''
          default: SUCCESS
        msg:
          type: string
          description: ''
        data:
          description: ''
          type: object
          properties: {}
        success:
          type: boolean
      x-apifox-orders:
        - code
        - msg
        - data
        - success
      x-apifox-ignore-properties: []
      x-apifox-folder: ''
    '':
      type: object
      properties: {}
      x-apifox-orders: []
      x-apifox-ignore-properties: []
      x-apifox-folder: ''
  securitySchemes: {}
servers: []
security: []

```