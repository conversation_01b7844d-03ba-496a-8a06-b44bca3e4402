# 飞书群聊列表服务使用说明

## 概述

`ChatListService` 是一个用于获取机器人所在群组列表的服务，支持10分钟内存缓存，并为飞书群聊搜索提供群组ID过滤功能。

## 功能特性

1. **获取群组列表**：获取机器人所在的所有群组信息
2. **内存缓存**：使用 `in_memory_cache` 装饰器缓存10分钟
3. **群聊过滤**：自动过滤出群聊类型的chat_id
4. **分页支持**：支持大量群组的分页获取
5. **错误处理**：完善的错误处理和日志记录

## 文件位置

- **服务文件**：`src/services/feishu/chat_list_service.py`
- **集成位置**：已集成到 `feishu_group_chat_content_tool.py` 中

## API 接口

### 1. get_bot_chat_list(page_size=50)

获取机器人所在的群组列表。

**参数：**
- `page_size`: 每页返回的群组数量，默认50，最大100

**返回值：**
- `List[Dict[str, Any]]`: 群组列表，每个群组包含：
  - `chat_id`: 群组ID
  - `name`: 群组名称
  - `description`: 群组描述
  - `chat_type`: 群组类型
  - `chat_mode`: 群组模式
  - `owner_id`: 群主ID
  - `member_count`: 成员数量

**示例：**
```python
from src.services.feishu.chat_list_service import get_bot_chat_list

chat_list = get_bot_chat_list(page_size=20)
for chat in chat_list:
    print(f"群组: {chat['name']} ({chat['chat_id']})")
```

### 2. get_group_chat_ids()

获取所有群聊的chat_id列表（过滤掉私聊）。

**返回值：**
- `List[str]`: 群聊ID列表

**示例：**
```python
from src.services.feishu.chat_list_service import get_group_chat_ids

chat_ids = get_group_chat_ids()
print(f"机器人在 {len(chat_ids)} 个群聊中")
```

### 3. get_chat_info_by_id(chat_id)

根据chat_id获取群组信息。

**参数：**
- `chat_id`: 群组ID

**返回值：**
- `Optional[Dict[str, Any]]`: 群组信息，如果未找到返回None

**示例：**
```python
from src.services.feishu.chat_list_service import get_chat_info_by_id

chat_info = get_chat_info_by_id("oc_xxx")
if chat_info:
    print(f"群组名称: {chat_info['name']}")
```

## 集成到群聊搜索

### 更新的搜索接口

`search_feishu_group_chat_content` 函数现在支持 `chat_ids` 参数：

```python
async def search_feishu_group_chat_content(
    wrapper: RunContextWrapper[UserInfo], 
    query: str, 
    page_size: int = 20,
    get_content: bool = True,
    chat_ids: Optional[List[str]] = None  # 新增参数
) -> Tuple[List[Dict[str, Any]], str]:
```

**新参数说明：**
- `chat_ids`: 可选的群聊ID列表
  - 如果不指定，自动获取机器人所在的所有群聊
  - 如果指定，只在这些群聊中搜索消息

**使用示例：**

1. **自动获取群聊ID（默认行为）**：
```python
results, message = await search_feishu_group_chat_content(
    wrapper=wrapper,
    query="开票信息",
    page_size=10
)
```

2. **指定特定群聊**：
```python
from src.services.feishu.chat_list_service import get_group_chat_ids

# 获取所有群聊ID
all_chat_ids = get_group_chat_ids()

# 只在前3个群聊中搜索
results, message = await search_feishu_group_chat_content(
    wrapper=wrapper,
    query="开票信息",
    page_size=10,
    chat_ids=all_chat_ids[:3]
)
```

## 缓存机制

- **缓存时间**：10分钟（600秒）
- **缓存键**：基于函数参数自动生成
- **缓存失效**：自动过期，无需手动清理

**缓存日志示例：**
```
缓存未命中: ChatListService.get_bot_chat_list (key: ...)
缓存命中: ChatListService.get_bot_chat_list (key: ...)
```

## 错误处理

1. **API调用失败**：记录详细错误信息和log_id
2. **网络超时**：自动重试机制
3. **权限不足**：清晰的错误提示
4. **数据解析错误**：使用 `getattr` 安全获取属性

## 性能优化

1. **分页获取**：避免一次性获取大量数据
2. **内存缓存**：减少重复API调用
3. **安全限制**：最大返回1000个群组，防止内存溢出
4. **超时设置**：10秒超时，避免长时间等待

## 测试结果

已通过测试验证：
- ✅ 成功获取机器人所在的群组列表
- ✅ 缓存机制正常工作
- ✅ 群聊ID过滤功能正常
- ✅ 集成到搜索接口正常

**测试输出示例：**
```
获取到 5 个群组
  群组 1: 商城&SaaS后端 (oc_e13047c401c7c92602e74cee8618d97d)
  群组 2: chatbi销售推进小队 (oc_0f6e7397fbf3f9e6778a2c86da547bdb)
  群组 3: AI项目组 (oc_75bc5e0a439f32bf59cb14318b6c5463)
```

## 技术实现要点

1. **lark_oapi SDK**：使用官方SDK进行API调用
2. **自动认证**：使用APP_ID和APP_SECRET自动获取token
3. **分页处理**：自动处理多页数据
4. **类型安全**：使用 `getattr` 安全获取可选属性
5. **日志记录**：详细的操作日志和错误信息

## 注意事项

1. **权限要求**：确保机器人具有获取群组列表的权限
2. **网络环境**：确保能够访问飞书开放平台API
3. **缓存时效**：群组信息变化后需等待缓存过期或重启服务
4. **资源限制**：大量群组可能影响性能，已设置合理限制
