# 测试架构模型配置指南

## 概述

`test_streaming_architecture_concurrent.py` 现在支持动态模型配置，允许在运行时指定不同的模型进行测试，而无需修改原始的YAML配置文件。

## 功能特性

- **兼容现有配置**: 完全兼容当前的YAML配置系统
- **动态模型覆盖**: 运行时指定模型，不修改原始配置文件
- **全局和特定配置**: 支持全局模型配置或针对特定agent的配置
- **临时配置管理**: 自动管理临时配置文件，测试完成后自动清理
- **增强测试报告**: 包含详细的模型配置信息和性能统计
- **模型性能对比**: 自动生成模型性能对比和排序
- **直观状态显示**: 使用图标和颜色直观显示性能状态

## 使用方法

### 1. 查看可用模型配置

```bash
uv run test_streaming_architecture_concurrent.py --list-models
```

### 2. 全局模型配置

对所有agent使用相同的模型:

```bash
# 使用Claude Sonnet 4
uv run test_streaming_architecture_concurrent.py --model openrouter/anthropic/claude-sonnet-4

# 使用Gemini 2.5 Pro
uv run test_streaming_architecture_concurrent.py --model openrouter/google/gemini-2.5-pro

# 使用DeepSeek V3
uv run test_streaming_architecture_concurrent.py --model xm/deepseek-v3-250324

# 使用Kimi K2
uv run test_streaming_architecture_concurrent.py --model xm/kimi-k2
```

### 3. 特定Agent配置

为不同的agent指定不同的模型:

```bash
# 销售分析用Kimi，协调器用Claude
uv run test_streaming_architecture_concurrent.py --model "sales_order_analytics:xm/kimi-k2,coordinator_bot:openrouter/anthropic/claude-sonnet-4"

# 通用聊天用Gemini，其他用默认
uv run test_streaming_architecture_concurrent.py --model "general_chat_bot:openrouter/google/gemini-2.5-pro"
```

### 4. 结合其他参数

```bash
# 使用Claude，3个并发，只测试5个用例
uv run test_streaming_architecture_concurrent.py --model openrouter/anthropic/claude-sonnet-4 --cu 3 --n 5

# 特定配置，内容长度2000字符
uv run test_streaming_architecture_concurrent.py --model "sales_order_analytics:xm/kimi-k2" --content-length 2000
```

## 支持的模型提供者

### XM (鲜沐内部API)
任意litellm-test上有的模型均支持，如：
- `xm/deepseek-v3-250324`
- `xm/kimi-k2`
- `xm/qwen3-coder`

### OpenRouter
任意openrouter上的模型都支持，如：
- `openrouter/anthropic/claude-sonnet-4`
- `openrouter/google/gemini-2.5-pro`
- `openrouter/openai/gpt-5-mini`

## 配置格式说明

### 全局配置格式
```
provider/model
```
例如: `openrouter/anthropic/claude-sonnet-4`

### 特定Agent配置格式
```
agent1:provider/model,agent2:provider/model
```
例如: `sales_order_analytics:xm/kimi-k2,coordinator_bot:openrouter/anthropic/claude-sonnet-4`

## 支持的Agent名称

- `coordinator_bot` - 协调器Bot
- `sales_order_analytics` - 销售订单分析
- `sales_kpi_analytics` - 销售KPI分析
- `warehouse_and_fulfillment` - 仓储物流
- `general_chat_bot` - 通用聊天

## 实现原理

### 1. 配置管理器 (ModelConfigManager)
- 动态修补配置加载函数
- 管理临时配置文件
- 自动清理资源

### 2. 配置覆盖机制
- 运行时拦截配置加载
- 应用模型覆盖设置
- 保持原始配置不变

### 3. 测试结果记录
- 记录使用的模型配置
- 在报告中显示模型信息
- 支持模型性能对比

## 注意事项

1. **环境变量**: 确保相应的API密钥已配置
2. **模型可用性**: 确认指定的模型在对应provider中可用
3. **资源清理**: 系统会自动清理临时配置文件
4. **并发安全**: 支持并发测试，每个测试使用独立的配置

## 示例场景

### 模型性能对比测试
```bash
# 测试Claude性能
uv run test_streaming_architecture_concurrent.py --model openrouter/anthropic/claude-sonnet-4 --n 10

# 测试Gemini性能
uv run test_streaming_architecture_concurrent.py --model openrouter/google/gemini-2.5-pro --n 10

# 测试DeepSeek性能
uv run test_streaming_architecture_concurrent.py --model xm/deepseek-v3-250324 --n 10
```

### 混合模型配置测试
```bash
# 协调器用Claude，专业分析用Kimi
uv run test_streaming_architecture_concurrent.py --model "coordinator_bot:openrouter/anthropic/claude-sonnet-4,sales_order_analytics:xm/kimi-k2,warehouse_and_fulfillment:xm/kimi-k2"
```

## 增强报告功能

### 报告内容增强
测试报告现在包含以下模型相关信息：

1. **🤖 模型配置概要**
   - 配置类型（全局统一/混合配置）
   - 使用的模型数量和类型
   - 配置的Agent数量

2. **🤖 模型性能统计**
   - 按通过率排序的模型性能
   - 每个模型的测试次数、通过率、平均耗时
   - 使用该模型的Agent列表
   - 性能状态图标（🟢🟡🔴）

3. **📊 模型对比总结**
   - 最佳和最差表现模型
   - 性能差距分析

4. **🎯 Agent详细统计**
   - 每个Agent使用的具体模型
   - 模型配置来源（覆盖配置/默认配置）
   - Agent级别的性能状态

### 报告示例
```bash
# 运行演示查看增强报告效果
uv run demo_enhanced_report.py
```

## 故障排除

1. **模型不存在**: 检查模型名称是否正确
2. **API密钥错误**: 检查环境变量配置
3. **配置解析失败**: 检查配置格式是否正确
4. **临时文件问题**: 系统会自动清理，无需手动处理
5. **报告显示异常**: 检查模型配置是否正确传递

## 扩展性

该架构设计具有良好的扩展性:
- 可轻松添加新的模型提供者
- 支持更复杂的配置规则
- 可扩展到其他测试脚本
- 报告格式可进一步定制