# Bad Case 飞书通知功能

## 功能概述

当用户标记对话为 Bad Case 时，系统会自动发送通知消息到指定的飞书群聊，以便相关人员及时关注和处理。

### 主要特性

- 🎨 **Markdown格式**：通知消息使用Markdown格式，美观易读
- 🔗 **Dashboard链接**：包含直接跳转到Dashboard查看对话详情的链接
- 👤 **用户信息**：显示标记Bad Case的用户名称
- ⏰ **时间戳**：记录标记的具体时间
- 🛡️ **容错处理**：通知发送失败不影响核心功能

## 实现原理

### 装饰器模式

使用装饰器 `feishu_notification_decorator` 来装饰 `mark_bad_case` 函数：

```python
@feishu_notification_decorator
def mark_bad_case(conversation_id: str, is_bad_case: bool = True, user_name: str = None) -> bool:
    # 原始函数逻辑
    pass
```

### 工作流程

1. 用户调用 `mark_bad_case` 函数标记对话为 Bad Case
2. 装饰器首先执行原始的 `mark_bad_case` 函数
3. 如果标记成功且 `is_bad_case=True`，装饰器会调用 `send_bad_case_notification` 发送通知
4. 通知消息发送到配置的飞书群聊

## 配置方法

### 1. 环境变量配置

在 `.env` 文件中添加以下配置：

```bash
# Bad Case通知群聊ID（可选）
# 当用户标记对话为Bad Case时，会发送通知到此群聊
# 如果不配置此项，则不会发送通知
BAD_CASE_NOTIFICATION_CHAT_ID=oc_xxxxxxxxxxxxxxxxxxxxxxxx
```

### 2. 获取群聊ID

1. 在飞书中创建或选择一个群聊
2. 确保机器人已加入该群聊
3. 获取群聊的 `chat_id`（格式通常为 `oc_` 开头的字符串）

### 3. 机器人权限

确保飞书机器人具有以下权限：
- 发送消息到群聊
- 读取群聊信息

## 通知消息格式

发送的通知消息格式如下（飞书交互式卡片格式）：

```json
{
    "header": {
        "template": "red",
        "title": {
            "content": "🚨 Bad Case 标记通知",
            "tag": "lark_md"
        }
    },
    "elements": [
        {
            "tag": "markdown",
            "content": "**标记用户**: [标记用户名]\n**对话用户**: [对话用户名]\n**对话ID**: `[conversation_id]`\n**标记时间**: [YYYY-MM-DD HH:MM:SS]\n**用户消息内容**:\n[用户消息内容]\n\n**操作**: [📊 查看对话详情](https://chat-bi.summerfarm.net/dashboard?chat=[conversation_id])\n\n---\n请相关人员及时关注和处理。"
        }
    ]
}
```

注意：这是卡片内容的JSON结构，发送时使用 `msg_type: "interactive"`。

在飞书中显示效果：
- 红色标题栏：🚨 Bad Case 标记通知
- Markdown格式的内容区域，支持粗体、代码块、链接等格式

### 通知内容示例

```
🚨 Bad Case 标记通知
标记用户: 王剑锋
对话用户: 李世民
对话ID: `714a41a9-ebb7-44a2-a215-858ed63f5bde`
标记时间: 2025-06-24 11:28:00
用户消息内容:
[18:48] 用户：总结一下最近一周关于 C 味调制芋泥的拜访记录内容
[18:49] 用户：有多少销售提到 C 味调制芋泥
[18:50] 用户：最近一周，有多少销售提到 C 味调制芋泥

📊 查看对话详情

---
请相关人员及时关注和处理。
```

## 使用示例

### 基本用法

```python
from src.services.chatbot.bad_case_service import mark_bad_case

# 标记对话为Bad Case，会自动发送通知
success = mark_bad_case(
    conversation_id="conv_123456",
    is_bad_case=True,
    user_name="张三"
)
```

### API调用

通过API标记Bad Case时，系统会自动获取用户信息并发送通知：

```bash
POST /api/mark_conversation_as_bad_case
{
    "conversation_id": "conv_123456"
}
```

## 测试功能

运行测试脚本验证功能是否正常：

```bash
python test_bad_case_notification.py
```

测试脚本会：
1. 检查模块导入是否正常
2. 验证环境变量配置
3. 测试通知发送功能
4. 测试装饰器功能

## 故障排除

### 1. 通知未发送

**可能原因：**
- 未配置 `BAD_CASE_NOTIFICATION_CHAT_ID` 环境变量
- 群聊ID格式错误
- 机器人未加入目标群聊
- 机器人权限不足

**解决方法：**
- 检查环境变量配置
- 验证群聊ID格式
- 确保机器人已加入群聊并有发送消息权限

### 2. 发送失败

**可能原因：**
- 网络连接问题
- 飞书API限流
- 机器人token过期

**解决方法：**
- 检查网络连接
- 查看日志中的详细错误信息
- 验证机器人配置

## 日志信息

系统会记录以下日志信息：

```
INFO: 已发送Bad Case通知到群聊，对话ID: conv_123456
WARNING: 发送Bad Case通知失败，对话ID: conv_123456
ERROR: 发送Bad Case通知时发生异常: [详细错误信息]
```

## 注意事项

1. **性能影响**：通知发送是同步操作，可能会略微增加响应时间
2. **错误处理**：通知发送失败不会影响Bad Case标记的核心功能
3. **隐私保护**：通知消息只包含对话ID和用户名，不包含具体对话内容
4. **配置可选**：如果未配置群聊ID，系统会跳过通知发送，不影响正常功能

## 扩展功能

### Dashboard链接功能

通知消息中包含的Dashboard链接格式为：
```
{HOST_NAME}/dashboard?chat={conversation_id}
```

点击链接后，管理员可以：
- 直接查看对话的完整内容
- 查看用户信息和对话历史
- 进行进一步的分析和处理

### 自定义通知内容

可以修改 `send_bad_case_notification` 函数来自定义通知消息格式：

```python
def send_bad_case_notification(conversation_id: str, user_name: str = None) -> bool:
    # 自定义消息内容
    message = f"自定义通知格式..."
    return send_message_to_chat(BAD_CASE_NOTIFICATION_CHAT_ID, message)
```

### 多群聊通知

可以扩展功能支持发送到多个群聊：

```python
# 在环境变量中配置多个群聊ID
BAD_CASE_NOTIFICATION_CHAT_IDS=oc_chat1,oc_chat2,oc_chat3
```
