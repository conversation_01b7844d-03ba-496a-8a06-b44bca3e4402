# 历史记录图片过滤功能文档

## 功能概述

为了优化AI对话性能并保持用户体验，我们实现了历史记录图片过滤功能。该功能确保：

1. **数据库中保留完整的图片信息**：用户可以在历史记录中看到自己发送的图片
2. **AI只接收必要的图片信息**：只有最新的用户消息包含图片，历史消息中的图片被过滤
3. **性能优化**：避免重复发送历史图片给AI，提高响应速度

## 问题背景

在多模态对话中，用户可能在历史消息中发送过图片。如果每次都将所有历史图片发送给AI，会导致：

1. **性能问题**：大量图片数据传输影响响应速度
2. **成本问题**：重复处理历史图片增加API调用成本
3. **上下文混乱**：过多的历史图片可能干扰AI对当前问题的理解

## 解决方案

### 核心策略

- **保存时**：完整保存用户消息，包括文本和图片信息（JSON格式）
- **展示时**：前端可以解析JSON格式，正常显示图片和文本
- **AI处理时**：历史消息只提取文本部分，当前消息保留完整的图片信息

### 技术实现

#### 1. 新增文本提取函数

**文件位置**：`src/services/chatbot/history_service.py`

```python
def extract_text_from_user_message(content: str) -> str:
    """
    从用户消息内容中提取纯文本，过滤掉图片信息
    
    Args:
        content: 用户消息内容，可能是纯文本或包含图片的JSON格式
        
    Returns:
        str: 提取出的纯文本内容
    """
```

**功能特性**：
- 自动检测消息格式（纯文本 vs JSON）
- 从JSON格式中提取`text`字段
- 忽略`images`字段
- 错误处理和回退机制

#### 2. 修改历史记录处理逻辑

**文件位置**：`src/services/chatbot/history_service.py`

**函数**：`get_conversation_history_as_input_list()`

**修改内容**：
- 对用户消息应用文本提取函数
- 保持助手消息不变
- 维持原有的历史记录处理逻辑

```python
# 对于用户消息，提取纯文本内容（过滤掉图片信息）
if role == 'user':
    content = extract_text_from_user_message(content)
    logger.debug(f"处理用户历史消息，提取纯文本内容 (ID: {msg.get('id', 'N/A')})")
```

#### 3. 修改推荐系统查询处理

**文件位置**：`src/services/chatbot/history_service.py`

**函数**：
- `get_user_latest_queries()`: 获取用户最近查询
- `get_other_users_latest_queries()`: 获取其他用户查询

**修改内容**：
- 对所有用户查询应用文本提取函数
- 过滤掉空查询和无效查询
- 确保推荐系统只接收纯文本内容

```python
# 过滤图片信息，只保留文本内容
filtered_queries = []
for query in raw_queries:
    if query:
        text_only = extract_text_from_user_message(query)
        if text_only.strip():  # 只添加非空的文本内容
            filtered_queries.append(text_only)
```

## 数据流程

### 1. 用户发送包含图片的消息

```json
{
  "text": "这个图片中的商品信息是什么？",
  "images": [
    "https://azure.summerfarm.net/chatbi-resource/product.png"
  ]
}
```

### 2. 数据库存储

**chat_history表**：
```sql
INSERT INTO chat_history (content, ...) VALUES (
  '{"text": "这个图片中的商品信息是什么？", "images": ["https://azure.summerfarm.net/chatbi-resource/product.png"]}',
  ...
);
```

### 3. 前端展示

前端解析JSON格式，同时显示文本和图片：
- 文本：这个图片中的商品信息是什么？
- 图片：显示缩略图，支持点击放大

### 4. AI处理历史记录

**历史消息**（发送给AI）：
```json
{
  "role": "user",
  "content": "这个图片中的商品信息是什么？"
}
```

**当前消息**（发送给AI）：
```json
{
  "role": "user", 
  "content": [
    {"type": "input_text", "text": "请分析这个新图片"},
    {"type": "input_image", "image_url": "data:image/png;base64,..."}
  ]
}
```

## 处理逻辑

### 消息类型识别

1. **纯文本消息**：直接返回原始内容
2. **JSON格式消息**：
   - 包含`text`和`images`字段：提取`text`字段
   - 其他JSON格式：返回原始内容
3. **无效JSON**：返回原始内容

### 错误处理

- JSON解析失败：返回原始内容
- 缺少`text`字段：返回原始内容
- 空内容或None：返回空字符串
- 其他异常：记录警告，返回原始内容

## 测试验证

### 单元测试

- ✅ 纯文本消息处理
- ✅ 包含图片的JSON消息文本提取
- ✅ 不包含图片的JSON消息处理
- ✅ 无效JSON处理
- ✅ 边界情况处理（空内容、None等）

### 集成测试

- ✅ 历史记录中图片信息正确过滤
- ✅ 当前消息图片信息保留
- ✅ 助手消息不受影响
- ✅ 数据库原始数据完整性

### 推荐系统测试

- ✅ 用户查询中的图片信息正确过滤
- ✅ 其他用户查询中的图片信息正确过滤
- ✅ 空查询和无效查询正确处理
- ✅ 推荐系统只接收纯文本查询内容

## 兼容性

### 向后兼容

- 支持现有的纯文本消息
- 支持新的JSON格式消息
- 不影响现有的历史记录

### 前端兼容

- 前端需要支持解析JSON格式的用户消息
- 纯文本消息保持原有显示方式
- JSON格式消息需要分别显示文本和图片

## 性能优化

### 减少数据传输

- 历史消息不包含图片数据
- 只有当前消息包含base64编码的图片
- 显著减少发送给AI的数据量

### 提高响应速度

- 避免重复处理历史图片
- 减少AI模型的处理时间
- 改善用户体验

## 监控和日志

### 关键日志

- 图片过滤操作记录
- JSON解析成功/失败统计
- 文本提取操作日志

### 性能指标

- 历史记录处理时间
- 图片过滤成功率
- AI响应时间改善

## 使用示例

### 用户发送图片消息

1. 用户粘贴图片并输入文本
2. 前端上传图片到CDN，获得URL
3. 发送包含文本和图片URL的请求
4. 后端保存JSON格式的消息到数据库
5. 下载图片并转换为base64发送给AI

### 查看历史记录

1. 前端请求历史记录
2. 后端返回包含JSON格式的消息
3. 前端解析JSON，分别显示文本和图片
4. 用户可以看到完整的对话历史

### AI处理对话

1. 获取历史记录，过滤图片信息
2. 只保留历史消息的文本部分
3. 当前消息包含完整的图片信息
4. 发送给AI进行处理

## 总结

通过实现历史记录图片过滤功能，我们成功平衡了用户体验和系统性能：

- **用户体验**：完整保留历史记录中的图片信息
- **系统性能**：避免重复发送历史图片给AI
- **数据完整性**：数据库中保存完整的消息信息
- **向后兼容**：支持现有的纯文本消息格式

这个功能为多模态对话系统提供了高效且用户友好的解决方案。
