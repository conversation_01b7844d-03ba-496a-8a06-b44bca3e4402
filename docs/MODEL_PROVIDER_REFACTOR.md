# Model Provider 重构总结

## 重构目标
将模型配置从单一模型名称改为 `provider + model` 的方式，支持多个模型提供者（如鲜沐内部API和OpenRouter）。

## 主要变更

### 1. 环境变量配置 (env.example)
新增了基于提供者的环境变量配置：
```bash
# 模型提供者配置
DEFAULT_MODEL_PROVIDER=openrouter

# 鲜沐内部API配置
PROVIDER_XM_API_KEY=your_xm_api_key
PROVIDER_XM_API_BASE=https://api.xianmu.com/v1
PROVIDER_XM_DEFAULT_MODEL=deepseek-v3-250324
PROVIDER_XM_FAST_MODEL=deepseek-v3-250324

# OpenRouter配置
PROVIDER_OPENROUTER_API_KEY=your_openrouter_api_key
PROVIDER_OPENROUTER_API_BASE=https://openrouter.ai/api/v1
PROVIDER_OPENROUTER_DEFAULT_MODEL=google/gemini-2.5-pro
PROVIDER_OPENROUTER_FAST_MODEL=openai/gpt-5-mini
PROVIDER_OPENROUTER_CLAUDE_MODEL=anthropic/claude-sonnet-4
```

### 2. 模型提供者逻辑重构 (model_provider.py)

#### 新增配置结构
- `PROVIDERS_CONFIG`: 定义了不同提供者的配置映射
- `DEFAULT_PROVIDER`: 默认提供者设置

#### 新增函数
- `get_provider_config(provider)`: 获取指定提供者的配置
- `get_model_for_name(model_name, provider)`: 支持指定提供者创建模型
- `get_default_model(provider)`: 获取指定提供者的默认模型
- `get_fast_model(provider)`: 获取指定提供者的快速模型
- `get_claude_model(provider)`: 获取Claude模型（仅openrouter支持）

#### 延迟初始化
- 使用延迟初始化避免在环境变量未配置时立即失败
- 提供向后兼容的全局变量访问方式

### 3. Agent配置文件更新
所有agent配置文件都新增了 `model_provider` 配置项：
```yaml
agent_name: sales_kpi_analytics
model_provider: openrouter  # 新增配置项
model: google/gemini-2.5-pro
model_settings:
  temperature: 0.5
  top_p: 0.9
# ... 其他配置
```

### 4. 代码调用更新

#### data_fetcher_bot.py
- 修改 `_safe_get_model` 方法，从配置中读取 `model_provider`
- 调用 `get_model_for_name(model_name, provider)` 时传入provider参数

#### 其他Bot文件
- 更新导入语句，使用延迟初始化函数
- 修改默认参数处理逻辑

### 5. 文档更新 (AGENT.md)
- 更新YAML配置示例，包含 `model_provider` 配置项
- 更新代码示例，展示新的模型配置方式

## 向后兼容性
- 保留了原有的环境变量配置（如 `XM_OPENAI_API_KEY` 等）
- 提供了延迟初始化的全局变量，确保现有代码无需修改
- 如果未指定provider，会使用默认provider

## 开发环境支持
- 在开发环境下，即使API密钥未配置也能正常运行
- 使用dummy key进行测试，避免开发时的配置复杂性

## 测试验证
创建了 `test_model_provider.py` 测试脚本，验证：
- 提供者配置获取
- 模型创建功能
- Agent配置文件解析
- 环境变量检查

## 使用方式

### 配置新的Agent
```yaml
agent_name: my_new_agent
model_provider: openrouter  # 或 xm
model: google/gemini-2.5-pro
model_settings:
  temperature: 0.1
# ... 其他配置
```

### 代码中使用
```python
# 使用默认提供者
model = get_model_for_name("google/gemini-2.5-pro")

# 指定提供者
model = get_model_for_name("deepseek-v3-250324", "xm")

# 获取特定类型的模型
default_model = get_default_model("openrouter")
fast_model = get_fast_model("xm")
claude_model = get_claude_model()  # 默认使用openrouter
```

## 注意事项
1. 确保在生产环境中正确配置相应的API密钥
2. 不同提供者支持的模型名称可能不同，需要根据实际情况配置
3. Claude模型目前仅在OpenRouter提供者中支持
4. 模型名称会自动添加 `openai/` 前缀以符合litellm要求