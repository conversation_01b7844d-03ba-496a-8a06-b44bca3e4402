# Tool Agent日志收集实现方案

## 概述

在Agent as Tool架构下，我们成功实现了一套完整的Tool Agent日志收集和记录机制，解决了Tool Agent执行过程中日志信息丢失的问题。

## 问题背景

在从Handoff架构迁移到Agent as Tool架构后，我们面临以下挑战：

1. **日志信息丢失**：Tool Agent被封装为工具函数，其内部执行过程的详细日志无法直接获取
2. **调试困难**：缺少Tool Agent的工具调用信息、执行时间、中间状态等关键调试信息
3. **历史记录不完整**：chat_history表中缺少Tool Agent的详细执行日志

## 解决方案架构

### 1. 数据结构扩展

#### AgentExecutionLog
```python
@dataclass
class AgentExecutionLog:
    """Agent执行过程的详细日志"""
    timestamp: str
    event_type: str  # 事件类型：tool_call, tool_output, log, error等
    content: str     # 日志内容
    metadata: Dict[str, Any] = field(default_factory=dict)  # 额外的元数据
```

#### AgentToolResult（扩展）
```python
@dataclass
class AgentToolResult:
    """Agent工具执行结果"""
    success: bool
    data: Any = None
    sql: Optional[str] = None
    error: Optional[str] = None
    agent_name: Optional[str] = None
    execution_time: Optional[float] = None
    # 新增：详细的执行日志
    execution_logs: List[AgentExecutionLog] = field(default_factory=list)
    # 新增：工具调用统计
    tool_calls_count: int = 0
    # 新增：使用的工具列表
    tools_used: List[str] = field(default_factory=list)
    # 新增：原始流事件数据（用于调试）
    raw_stream_events: List[Dict[str, Any]] = field(default_factory=list)
```

### 2. 流式日志捕获

#### AgentToolExecutor增强
- 实现`_execute_with_stream_logging`方法
- 使用`Runner.run_streamed`捕获详细的流事件
- 解析流事件并生成结构化日志
- 统计工具调用次数和使用的工具

```python
async def _execute_with_stream_logging(self, agent, query, user_context, agent_name):
    """使用流式执行并捕获详细的执行日志"""
    execution_logs = []
    tools_used = []
    tool_calls_count = 0
    
    # 使用流式执行
    result = Runner.run_streamed(agent, input=query, ...)
    
    # 处理流事件并收集日志
    async for event in result.stream_events():
        # 记录原始事件
        # 格式化事件消息
        # 创建执行日志条目
        # 统计工具调用
```

### 3. 日志聚合和管理

#### AgentToolRegistry扩展
- 添加`execution_results`列表保存详细执行结果
- 实现`get_all_execution_logs()`方法聚合所有Tool Agent日志
- 实现`get_execution_summary()`方法提供统计信息

#### CoordinatorBot增强
- 添加`get_aggregated_tool_logs()`方法
- 添加`get_detailed_tool_execution_logs()`方法
- 在`get_execution_summary()`中包含日志信息

### 4. chat_history集成

#### 日志保存流程更新
1. **runner.py**：在保存助手消息时获取Tool Agent日志
2. **ConversationService**：接收bot实例并提取聚合日志
3. **QueryProcessor**：传递bot实例给ConversationService

```python
# 获取Tool Agent的详细执行日志
enhanced_logs = logs if logs else ""
if bot_instance and hasattr(bot_instance, 'get_aggregated_tool_logs'):
    tool_agent_logs = bot_instance.get_aggregated_tool_logs()
    if tool_agent_logs and tool_agent_logs.strip():
        enhanced_logs += "\n\n" + tool_agent_logs
```

## 实现特性

### 1. 完整的日志链路
- **流事件捕获**：捕获Tool Agent执行过程中的所有流事件
- **结构化存储**：将日志信息结构化存储，便于查询和分析
- **聚合展示**：将多个Tool Agent的日志聚合为统一格式

### 2. 丰富的统计信息
- **执行统计**：总执行次数、成功/失败次数、平均执行时间
- **工具使用统计**：每个Agent使用的工具列表、工具调用次数
- **时间追踪**：详细的时间戳记录，便于性能分析

### 3. 调试友好
- **原始事件保存**：保存原始流事件数据用于深度调试
- **分层日志**：提供不同详细程度的日志视图
- **错误追踪**：详细记录执行过程中的错误信息

### 4. 向后兼容
- **无破坏性更改**：所有更改都是向后兼容的
- **渐进式增强**：现有功能不受影响，新功能作为增强
- **可选启用**：日志收集功能可以根据需要启用或禁用

## 验证结果

通过`verify_tool_agent_logs.py`脚本验证，所有核心功能正常工作：

✅ **数据结构测试**：AgentExecutionLog和AgentToolResult创建成功
✅ **CoordinatorBot测试**：日志聚合功能正常
✅ **AgentToolRegistry测试**：执行结果管理功能正常
✅ **JSON序列化测试**：数据序列化功能正常

## 使用示例

### 获取聚合日志
```python
bot = CoordinatorBot(user_info)
# 执行查询后
aggregated_logs = bot.get_aggregated_tool_logs()
```

### 获取详细执行信息
```python
execution_summary = bot.get_execution_summary()
print(f"使用的Agent: {execution_summary['coordinator_info']['registered_agents']}")
print(f"执行统计: {execution_summary['executor_stats']}")
```

### 查看chat_history中的日志
```sql
SELECT logs FROM chat_history 
WHERE role = 'assistant' 
AND logs LIKE '%Tool Agent%'
ORDER BY timestamp DESC;
```

## 调试工具

### 1. verify_tool_agent_logs.py
基础功能验证脚本，测试数据结构和核心功能。

### 2. debug_tool_agent_logs.py
完整的调试工具，包含：
- Tool Agent执行测试
- chat_history集成测试
- 综合测试报告

## 后续优化建议

### 1. 性能优化
- 实现日志级别控制，避免在生产环境记录过多详细日志
- 添加日志压缩和清理机制
- 优化大量日志数据的存储和查询

### 2. 功能扩展
- 添加日志搜索和过滤功能
- 实现日志可视化界面
- 支持日志导出和分析

### 3. 监控告警
- 添加Tool Agent执行失败的告警机制
- 实现性能监控和异常检测
- 提供执行趋势分析

## 总结

通过这套完整的Tool Agent日志收集方案，我们成功解决了Agent as Tool架构下的日志可见性问题，为调试、监控和优化提供了强有力的支持。该方案具有以下优势：

1. **完整性**：覆盖了从流事件捕获到历史记录保存的完整链路
2. **可扩展性**：模块化设计，便于后续功能扩展
3. **实用性**：提供了丰富的调试工具和验证脚本
4. **稳定性**：经过充分测试，确保功能的可靠性

这套方案为Agent as Tool架构的进一步发展奠定了坚实的基础。
