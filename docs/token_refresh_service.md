# Token刷新服务文档

## 概述

Token刷新服务是一个后台定时任务，用于自动刷新即将过期的用户access token，确保用户无需频繁重新登录即可持续使用系统。

## 功能特性

- **定时检查**：每5分钟检查一次数据库中的用户session
- **智能刷新**：自动识别即将在10分钟内过期的access token
- **自动更新**：使用refresh token调用飞书API获取新的access token和refresh token
- **数据库更新**：成功刷新后自动更新数据库中的token信息和过期时间
- **错误处理**：妥善处理refresh token失效等异常情况
- **高效实现**：使用threading.Event.wait()实现可中断的定时检查

## 服务架构

### 核心组件

1. **TokenRefreshService** (`src/services/auth/token_refresh_service.py`)
   - 主要的服务类，负责定时检查和刷新逻辑
   - 支持配置检查间隔和过期阈值
   - 提供启动、停止和状态查询功能

2. **UserSessionRepository** (`src/repositories/chatbi/user_session.py`)
   - 新增`find_sessions_near_expiry()`方法
   - 查询即将过期的session记录

3. **UserSessionService** (`src/services/auth/user_session_service.py`)
   - 新增`get_sessions_near_expiry()`方法
   - 提供业务层的session查询接口

## 配置参数

### 默认配置
- **检查间隔**：5分钟
- **过期阈值**：10分钟（即将在10分钟内过期的token会被刷新）

### 自定义配置
```python
# 创建自定义配置的服务实例
service = TokenRefreshService(
    check_interval_minutes=3,    # 3分钟检查一次
    expiry_threshold_minutes=15  # 15分钟内过期的token会被刷新
)
```

## 使用方法

### 自动启动
服务会在应用启动时自动启动（在`app.py`中配置）：

```python
from src.services.auth.token_refresh_service import start_token_refresh_service

# 启动Token刷新服务
start_token_refresh_service()
```

### 手动控制
```python
from src.services.auth.token_refresh_service import token_refresh_service

# 启动服务
token_refresh_service.start()

# 检查服务状态
status = token_refresh_service.get_status()
print(status)

# 停止服务
token_refresh_service.stop()
```

## 工作流程

1. **定时检查**：每5分钟执行一次检查任务
2. **查询数据库**：查找即将在10分钟内过期的活跃session
3. **调用API**：对每个需要刷新的session，使用其refresh token调用飞书API
4. **更新数据库**：成功获取新token后，更新数据库中的token信息和过期时间
5. **错误处理**：如果refresh token失效，记录日志但不影响其他session的刷新

## API调用

### 飞书Token刷新API
- **端点**：`https://open.feishu.cn/open-apis/authen/v2/oauth/token`
- **方法**：POST
- **参数**：
  ```json
  {
    "grant_type": "refresh_token",
    "client_id": "your_app_id",
    "client_secret": "your_app_secret",
    "refresh_token": "user_refresh_token"
  }
  ```

### 响应处理
- **成功**：更新access_token、refresh_token和过期时间
- **失败**：记录错误日志，特别处理refresh token失效的情况

## 日志记录

服务会记录以下关键信息：
- 服务启动和停止
- 每次检查发现的即将过期token数量
- 成功刷新的token数量
- API调用失败的详细错误信息
- refresh token失效的警告

## 监控和维护

### 服务状态检查
```python
status = token_refresh_service.get_status()
# 返回：
# {
#     "is_running": True,
#     "check_interval_minutes": 5,
#     "expiry_threshold_minutes": 10,
#     "thread_alive": True
# }
```

### 常见问题

1. **服务未启动**：检查app.py中是否正确调用了start_token_refresh_service()
2. **token刷新失败**：检查飞书应用配置和网络连接
3. **refresh token失效**：用户需要重新登录获取新的refresh token

## 与现有系统的集成

### 被动刷新保留
现有的被动token刷新逻辑（在用户请求时检查并刷新）仍然保留，与定时刷新服务形成双重保障：
- **定时刷新**：主动维护token有效性
- **被动刷新**：用户请求时的即时保障

### 数据库兼容
服务使用现有的user_session表结构，无需额外的数据库变更。

## 性能考虑

- 使用连接池进行数据库操作，避免频繁建立连接
- 批量处理多个session的刷新，提高效率
- 使用Event.wait()实现高效的定时检查，支持快速中断
- 合理的错误处理避免单个失败影响整体服务
