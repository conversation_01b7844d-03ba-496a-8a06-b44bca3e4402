# CacheEnabledLitellmModel 使用指南

## 概述

`CacheEnabledLitellmModel` 是一个支持prompt缓存的LiteLLM模型实现。当检测到`system_instructions`时，它会自动将其转换为支持缓存的格式，从而提高性能并降低API调用成本。

## 主要特性

- ✅ **自动缓存检测**: 自动检测`system_instructions`并转换为缓存格式
- ✅ **向后兼容**: 完全兼容原有的`LitellmModel`接口
- ✅ **性能优化**: 减少token消耗和响应延迟
- ✅ **成本降低**: 通过缓存减少重复处理的成本

## 缓存格式

当提供`system_instructions`时，模型会自动将其转换为以下格式：

```json
{
  "role": "system",
  "content": [
    {
      "type": "text",
      "text": "您的system instructions内容",
      "cache_control": {
        "type": "ephemeral"
      }
    }
  ]
}
```

## 使用方法

### 1. 导入模型

```python
from src.services.agent.utils.model_provider import (
    CACHE_ENABLED_LITE_LLM_MODEL,
    CACHE_ENABLED_LITE_LLM_FAST_MODEL
)
```

### 2. 在Agent中使用

```python
from agents import Agent
from src.models.user_info_class import UserInfo

# 创建使用缓存模型的Agent
agent = Agent[UserInfo](
    name="数据分析助手",
    instructions="您的长system instructions...",  # 会自动启用缓存
    model=CACHE_ENABLED_LITE_LLM_MODEL,
    tools=[...],
)
```

### 3. 在Bot中使用

```python
from src.services.agent.bots.base_bot import BaseBot

class MyBot(BaseBot):
    def create_agent(self, model=CACHE_ENABLED_LITE_LLM_MODEL):
        instruction = self.get_long_instruction()  # 长指令
        return Agent[UserInfo](
            name="我的机器人",
            instructions=instruction,
            model=model,  # 使用支持缓存的模型
        )
```

### 4. 替换现有模型

只需要将现有的模型引用替换即可：

```python
# 原来的代码
from src.services.agent.utils.model_provider import LITE_LLM_MODEL

# 替换为
from src.services.agent.utils.model_provider import CACHE_ENABLED_LITE_LLM_MODEL

# 其他代码保持不变
agent = Agent[UserInfo](
    name="助手",
    instructions=instructions,
    model=CACHE_ENABLED_LITE_LLM_MODEL,  # 只需要替换这里
)
```

## 可用模型实例

项目中提供了两个预配置的缓存模型实例：

- `CACHE_ENABLED_LITE_LLM_MODEL`: 标准缓存模型
- `CACHE_ENABLED_LITE_LLM_FAST_MODEL`: 快速缓存模型

## 适用场景

### 最佳使用场景

1. **长system prompt**: 当您有较长的系统指令时
2. **重复调用**: 相同的system instructions会被多次使用
3. **性能敏感**: 需要减少响应延迟的场景
4. **成本优化**: 希望降低API调用成本

### 示例场景

```python
# 适合使用缓存的长system instructions
long_instructions = """
你是一个专业的数据分析助手。你拥有以下能力：

1. 数据库查询和分析
2. 商业智能报告生成  
3. 数据可视化建议
4. 业务指标解释

请根据用户的问题提供准确、专业的回答。在回答时请：
- 使用清晰的逻辑结构
- 提供具体的数据支持
- 给出可行的建议
- 保持专业和友好的语调

如果需要查询数据库，请使用适当的SQL语句。
如果需要生成图表，请说明图表类型和数据要求。
...更多详细指令...
"""

# 使用缓存模型处理长指令
agent = Agent[UserInfo](
    name="数据分析助手",
    instructions=long_instructions,  # 长指令会自动启用缓存
    model=CACHE_ENABLED_LITE_LLM_MODEL,
)
```

## 技术实现

### 核心方法

```python
def _convert_system_instructions_to_cached_format(self, system_instructions: str) -> dict:
    """将system_instructions转换为支持缓存的格式"""
    return {
        "role": "system",
        "content": [
            {
                "type": "text",
                "text": system_instructions,
                "cache_control": {
                    "type": "ephemeral"
                }
            }
        ]
    }
```

### 自动检测逻辑

在`_fetch_response`方法中，当检测到`system_instructions`不为空时：

```python
if system_instructions:
    cached_system_message = self._convert_system_instructions_to_cached_format(system_instructions)
    converted_messages.insert(0, cached_system_message)
```

## 注意事项

1. **兼容性**: 完全向后兼容，可以直接替换现有的`LitellmModel`
2. **自动处理**: 无需手动配置，模型会自动检测并应用缓存
3. **性能**: 特别适合长system instructions的重复使用场景
4. **成本**: 通过缓存可以显著降低token消耗

## 测试

运行测试以验证功能：

```bash
python test_cache_enabled_model.py
python example_cache_enabled_usage.py
```

## 总结

`CacheEnabledLitellmModel`提供了一个简单而强大的方式来优化LiteLLM模型的性能。通过自动检测和转换system instructions为缓存格式，它可以显著提高响应速度并降低成本，同时保持完全的向后兼容性。
