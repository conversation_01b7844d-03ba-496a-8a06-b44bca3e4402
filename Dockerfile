# 定义基础镜像 (base stage) 用于安装依赖
FROM xianmu-registry-registry.cn-hangzhou.cr.aliyuncs.com/base/python:3.10-slim AS base

# 安装uv
COPY --from=ghcr.io/astral-sh/uv:latest /uv /uvx /bin/

# Copy the project into the image
ADD . /app

# Sync the project into a new environment, asserting the lockfile is up to date
WORKDIR /app
RUN uv sync --locked

COPY env.example .env

# 设置时区为北京时间
RUN ln -snf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo Asia/Shanghai > /etc/timezone

# 使 5700 端口对容器外部世界可用
EXPOSE 5700

CMD ["uv", "run", "app.py", "--port", "5700"]