CREATE TABLE IF NOT EXISTS `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `name` varchar(64) NOT NULL COMMENT '用户名称',
  `email` varchar(128) NOT NULL COMMENT '用户邮箱',
  `avatar` varchar(1000) DEFAULT NULL COMMENT '用户头像',
  `user_id` varchar(64) NOT NULL COMMENT '用户ID',
  `job_title` varchar(128) DEFAULT NULL COMMENT '用户职位',
  `first_level_department` varchar(128) DEFAULT NULL COMMENT '一级部门名称',
  `open_id` varchar(64) NOT NULL DEFAULT '' COMMENT '飞书用户open_id',
  `union_id` varchar(64) DEFAULT NULL COMMENT '飞书用户union_id，用于获取API token',
  `last_login_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后登录时间',
  `feishu_docs_folder_token` varchar(255) DEFAULT NULL COMMENT '飞书文档文件夹token',
  `feishu_docs_folder_name` varchar(255) DEFAULT NULL COMMENT '飞书文档文件夹名称',
  `is_admin` tinyint(4) DEFAULT '0' COMMENT '是否是admin，只有admin用户才可以查看dashboard',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_user_openid` (`open_id`),
  KEY `idx_union_id` (`union_id`),
  KEY `idx_first_level_department` (`first_level_department`)
) ENGINE=InnoDB AUTO_INCREMENT=100000 DEFAULT CHARSET=utf8mb4 COMMENT='用户表，存储用户信息';

-- 创建用户会话表
CREATE TABLE IF NOT EXISTS `user_session` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `session_id` varchar(128) NOT NULL COMMENT '自生成的session ID',
  `open_id` varchar(64) NOT NULL COMMENT '飞书用户open_id',
  `refresh_token` text NOT NULL COMMENT '飞书refresh token',
  `access_token` text COMMENT '当前access token',
  `access_token_expires_at` datetime DEFAULT NULL COMMENT 'access token过期时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `last_active_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后活跃时间',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否活跃：1-活跃，0-已失效',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_session_id` (`session_id`),
  KEY `idx_open_id` (`open_id`),
  KEY `idx_last_active` (`last_active_at`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户session表，存储refresh token实现长久登录';

-- 创建对话历史表
CREATE TABLE IF NOT EXISTS `chat_history` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `username` varchar(32) NOT NULL COMMENT '用户名称',
  `email` varchar(64) NOT NULL COMMENT '用户邮箱',
  `conversation_id` varchar(128) NOT NULL COMMENT '对话ID',
  `role` varchar(10) NOT NULL COMMENT '角色，user/assistant/system',
  `content` longtext COMMENT '对话内容',
  `logs` longtext COMMENT '日志',
  `output_as_input` text COMMENT '将AI的回复(role=assistant)作为下一轮会话的输入',
  `timestamp` bigint(20) NOT NULL COMMENT '对话时间戳',
  `is_bad_case` tinyint(4) DEFAULT '0' COMMENT '是否是bad case 0=No, 1=Yes',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `agent` varchar(255) DEFAULT NULL COMMENT '本次对话使用到的Agent名称',
  `resource_url` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '资源URL，支持多个URL用逗号分隔，如图片、文档等',
  `time_spend` int(11) DEFAULT '1' COMMENT 'AI响应的耗时(秒)，只有role=assitant才有意义。',
  `is_in_process` tinyint(4) DEFAULT '0' COMMENT '是否正在处理中 0=No, 1=Yes',
  `time_to_first_token` int(11) DEFAULT '0' COMMENT 'AI首次响应的耗时',
  `uploaded_feishu_docs` varchar(1024) DEFAULT NULL COMMENT '从AI回复的内容中提取出来的飞书文档链接。格式为JSON数组，如:[{"url": "https://example.feishu.cn/doc/123", "name": "文档1"}]',
  PRIMARY KEY (`id`),
  KEY `idx_user_convo_time` (`username`,`email`,`conversation_id`,`timestamp`),
  KEY `idx_convo_id` (`conversation_id`),
  KEY `idx_timestamp` (`timestamp`),
  KEY `idx_chat_history_agent` (`agent`)
) ENGINE=InnoDB AUTO_INCREMENT=100000 DEFAULT CHARSET=utf8mb4 COMMENT='对话历史表';

CREATE TABLE IF NOT EXISTS `bad_case` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `conversation_id` varchar(128) NOT NULL COMMENT '对话ID',
  `repair_status` tinyint(4) DEFAULT '0' COMMENT '修复状态: 0=未修复, 1=已修复, 2=暂不修复',
  `marked_by` varchar(32) DEFAULT NULL COMMENT '标记人',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '标记时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `repair_note` text COMMENT '修复说明',
  `feedback_tags` text COMMENT '选择的反馈标签(JSON格式字符串)',
  `custom_feedback` text COMMENT '用户自定义反馈文本',
  `feedback_submitted_at` timestamp NULL DEFAULT NULL COMMENT '反馈提交时间',
  `chat_history_id` bigint default null comment 'chat_history的消息ID，badcase和good case都应该是具体的一条chat history记录。',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_conversation_id` (`conversation_id`),
  KEY `idx_conversation_id` (`conversation_id`),
  KEY `idx_repair_status` (`repair_status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=100000 DEFAULT CHARSET=utf8mb4 COMMENT='Bad Case表';

CREATE TABLE IF NOT EXISTS `share_map` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `share_id` varchar(128) NOT NULL COMMENT '分享ID',
  `conversation_id` varchar(128) NOT NULL COMMENT '对话ID',
  `owner_username` varchar(32) NOT NULL COMMENT '分享人',
  `owner_email` varchar(64) NOT NULL COMMENT '分享人邮箱',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '分享时间',
  `db_created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Record creation time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_share_id` (`share_id`),
  KEY `idx_owner` (`owner_username`,`owner_email`),
  KEY `idx_conversation_id` (`conversation_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Share Map表';

CREATE TABLE IF NOT EXISTS `good_case` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `conversation_id` varchar(128) NOT NULL COMMENT '对话ID',
  `marked_by` varchar(32) DEFAULT NULL COMMENT '标记人',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '标记时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `feedback_tags` text COMMENT '选择的反馈标签(JSON格式字符串)',
  `custom_feedback` text COMMENT '用户自定义反馈文本',
  `feedback_submitted_at` timestamp NULL DEFAULT NULL COMMENT '反馈提交时间',
  `chat_history_id` bigint default null comment 'chat_history的消息ID，badcase和good case都应该是具体的一条chat history记录。',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_conversation_id` (`conversation_id`)
) ENGINE=InnoDB AUTO_INCREMENT=100000 DEFAULT CHARSET=utf8mb4 COMMENT='Good Case表';

-- 创建离线用户推荐问题表
CREATE TABLE IF NOT EXISTS `user_recommendation_offline` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `user_email` varchar(128) NOT NULL COMMENT '用户邮箱',
  `user_open_id` varchar(64) NOT NULL COMMENT '用户open_id',
  `recommendations` longtext NOT NULL COMMENT '离线生成的推荐问题列表(JSON格式字符串)',
  `generated_at` datetime NOT NULL COMMENT '离线生成时间',
  `expires_at` datetime NOT NULL COMMENT '推荐过期时间',
  `generation_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '生成状态: 0=待生成, 1=生成中, 2=生成完成, 3=生成失败',
  `error_message` text COMMENT '如果生成失败，记录错误信息',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_email` (`user_email`),
  KEY `idx_user_open_id` (`user_open_id`),
  KEY `idx_expires_at` (`expires_at`),
  KEY `idx_generation_status` (`generation_status`),
  KEY `idx_generated_at` (`generated_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='离线生成的用户推荐问题表，减少实时AI调用';

-- 创建部门Top10常问清单周度分析表
CREATE TABLE IF NOT EXISTS `department_top_questions_weekly` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `department_name` varchar(128) NOT NULL COMMENT '部门名称',
  `week_start_date` date NOT NULL COMMENT '周开始日期（周一）',
  `week_end_date` date NOT NULL COMMENT '周结束日期（周日）',
  `top_questions` longtext NOT NULL COMMENT 'Top10问题列表(JSON格式)',
  `total_conversations` int(11) NOT NULL DEFAULT 0 COMMENT '该部门该周总对话数',
  `total_user_queries` int(11) NOT NULL DEFAULT 0 COMMENT '该部门该周总用户查询数',
  `analysis_status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '分析状态: 0=待分析, 1=分析中, 2=分析完成, 3=分析失败',
  `error_message` text COMMENT '分析失败时的错误信息',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_dept_week` (`department_name`, `week_start_date`),
  KEY `idx_week_start` (`week_start_date`),
  KEY `idx_department` (`department_name`),
  KEY `idx_analysis_status` (`analysis_status`)
) ENGINE=InnoDB AUTO_INCREMENT=100000 DEFAULT CHARSET=utf8mb4 COMMENT='部门Top10常问清单周度分析结果表';