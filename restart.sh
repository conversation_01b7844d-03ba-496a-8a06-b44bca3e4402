echo "chatbi_mysql_restart"

# 因为Mac下 Python的进程名字是大写的P开头的，所以使用-i参数忽略大小写。
lsof -i :5700 | grep -i python | awk '{print $2}' | xargs kill -9

# 检查UV是否已安装
if ! command -v uv &> /dev/null; then
    echo "UV is not installed. Installing UV..."
    # 使用pip安装UV
    pip install uv

    # 验证安装
    if ! command -v uv &> /dev/null; then
        echo "Failed to install UV. Please install it manually."
        exit 1
    else
        echo "UV installed successfully."
    fi
fi

# 确保logs目录存在
mkdir -p ./logs

# git pull origin
cat ./app.pid.log | xargs kill -9

uv sync

# 检查是否有--debug参数
if [[ "$1" == "--debug" ]]; then
    # 开发模式，启用Flask的调试模式
    echo "Starting in debug mode..."
    nohup uv run app.py --debug > ./logs/chat_bi_mysql.log 2>&1 & echo $! > ./app.pid.log
else
    # 正常模式
    nohup uv run app.py > ./logs/chat_bi_mysql.log 2>&1 & echo $! > ./app.pid.log
fi

echo "chatbi_mysql_restarted, new pid is $(cat ./app.pid.log)"

# 查看日志
tail -200f ./logs/chat_bi_mysql.log
