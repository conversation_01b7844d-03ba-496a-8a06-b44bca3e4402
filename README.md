# ChatBI: 您的对话式商业智能助手

[![Python Version](https://img.shields.io/badge/python-3.8+-blue.svg)](https://www.python.org/)
[![Framework](https://img.shields.io/badge/framework-Flask-green.svg)](https://flask.palletsprojects.com/)
[![License](https://img.shields.io/badge/license-MIT-lightgrey.svg)](LICENSE)

**ChatBI** 是一个智能商业分析平台，让您能够通过自然语言与数据进行交互。它由大语言模型（LLM）和先进的 **Agent-as-Tool** 架构驱动，将复杂的数据查询转化为简单的对话。

本仓库包含 ChatBI 后端的完整源代码，包括 Flask 应用、Agent 服务、数据库管理以及飞书机器人集成。

## ✨ 核心功能

- 🤖 **智能 Agent 协调**：核心的 `CoordinatorBot` 能够智能地将任务分配给专门负责销售分析、仓储物流等领域的专业 Agent。
- 🔄 **实时流式响应**：在 Web 界面和飞书机器人中都能获得即时的反馈和结果。
- 🔐 **长效安全认证**：使用飞书 OAuth2.0、JWT 和自动令牌刷新机制，实现安全、长效的认证。
- 📊 **双端支持**：通过 Web 门户或交互式飞书机器人访问您的数据洞察。
- 🏗️ **领域驱动设计 (DDD)**：清晰、可维护且可扩展的架构，将不同职责分离到独立的层中。
- ⚡ **高性能**：通过数据库连接池、异步处理和事件驱动架构，为速度而构建。

## 🏛️ 架构总览

ChatBI 采用 **领域驱动设计（DDD）** 方法，将系统构建为清晰、解耦的层次。其核心是 **Agent as Tool** 模型，由一个中央协调者选择最合适的专业 Agent 来处理用户查询。

### 系统架构图

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                           ChatBI 系统架构                                 │
├─────────────────────────────────────────────────────────────────────────────┤
│  用户接入层                                                                 │
│  ┌─────────────────────┐              ┌─────────────────────┐                 │
│  │      Web 门户        │              │      飞书机器人       │                 │
│  └─────────────────────┘              └─────────────────────┘                 │
│           │ HTTPS/SSE                            │ WebSocket                  │
│           ▼                                       ▼                           │
├─────────────────────────────────────────────────────────────────────────────┤
│  API 网关 (Flask)                                                           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐        │
│  │   认证 API  │ │   查询 API  │ │  仪表盘 API │ │  飞书 API   │        │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘        │
├─────────────────────────────────────────────────────────────────────────────┤
│  业务服务层                                                                 │
│  ┌─────────────────────┐              ┌─────────────────────┐                 │
│  │     认证服务        │              │     Agent 服务      │                 │
│  │                     │              │  (CoordinatorBot)   │                 │
│  └─────────────────────┘              └─────────────────────┘                 │
├─────────────────────────────────────────────────────────────────────────────┤
│  数据访问层                                                                 │
│  ┌─────────────────────┐              ┌─────────────────────┐                 │
│  │  ChatBI 仓库层     │              │  业务数据库仓库层   │                 │
│  └─────────────────────┘              └─────────────────────┘                 │
├─────────────────────────────────────────────────────────────────────────────┤
│  数据库层                                                                   │
│  ┌─────────────────────┐              ┌─────────────────────┐                 │
│  │   ChatBI 数据库     │              │   业务数据库        │                 │
│  │      (MySQL)        │              │      (MySQL)        │                 │
│  └─────────────────────┘              └─────────────────────┘                 │
└─────────────────────────────────────────────────────────────────────────────┘
```

关于架构的更详细说明，请参阅 [ChatBI架构.md](ChatBI架构.md)。

## 🚀 快速上手

按照以下步骤，您可以在 30 分钟内搭建并运行 ChatBI 的开发环境。

### 1. 环境要求

- Python 3.8+
- Git
- MySQL 数据库访问权限
- 拥有机器人创建权限的飞书（Lark）帐号

### 2. 安装与配置

1.  **克隆仓库：**
    ```bash
    git clone <your-repository-url>
    cd chatbi
    ```

2.  **安装依赖 (使用 uv)：**
    本项目使用 `uv` 进行包管理。请运行以下命令来同步依赖：
    ```bash
    uv pip sync
    ```

3.  **配置环境变量：**
    复制环境变量示例文件，并填入您的具体凭证。
    ```bash
    cp .env.example .env
    ```
    编辑 `.env` 文件，填入您的飞书应用 ID/密钥和数据库连接信息。

### 3. 运行项目

-   **启动开发服务器：**
    ```bash
    python app.py --port 5700 --debug
    ```
-   **访问 Web 界面：**
    打开浏览器并访问 `http://localhost:5700`。

-   **配置飞书机器人：**
    在飞书开发者后台，将您的机器人回调 URL 设置为 `https://your-domain.com/feishu/webhook`。

## 📂 项目结构

项目遵循与领域驱动设计原则一致的逻辑结构：

```
chatbi/
├── src/
│   ├── api/          # 所有 API 端点的 Flask Blueprint
│   ├── services/     # 核心业务逻辑和领域服务
│   │   ├── agent/      # Agent 协调与执行
│   │   ├── auth/       # 认证与会话管理
│   │   └── feishu/     # 飞书机器人集成与事件处理
│   ├── repositories/ # 数据访问层（仓储）
│   ├── db/           # 数据库连接与查询管理
│   ├── models/       # 数据传输对象和领域模型
│   └── utils/        # 工具函数与助手
├── resources/
│   ├── data_fetcher_bot_config/ # 专业 Agent 的 YAML 配置文件
│   └── prompt/       # 用于 LLM 的系统提示
├── tests/          # 单元与集成测试
├── app.py          # Flask 应用主入口
└── Dockerfile      # 容器化配置
```

## 🤝 如何贡献

我们欢迎各种形式的贡献！为确保开发过程的顺畅高效，所有贡献者都应遵守 `GEMINI.md` 文件中列出的准则。这包括遵循我们的编码标准（SOLID, DRY, KISS）、架构原则（DDD）和 Git 提交规范。

在开始任何重大工作之前，请先创建一个 Issue 来讨论您提议的更改。

## 📄 许可证

本项目基于 MIT 许可证。详情请参阅 [LICENSE](LICENSE) 文件。